<template>
  <div class="home-container">
    <!-- 使用通用头部组件 -->
    <TheHeader />

    <!-- 主横幅 -->
    <section class="banner">
      <!-- 轮播背景 -->
      <div class="carousel-background">
        <div
          v-for="(bg, index) in backgroundImages"
          :key="index"
          class="carousel-slide"
          :class="{ active: currentSlide === index }"
          :style="{ backgroundImage: `url(${bg})` }"
        ></div>
      </div>

      <!-- 轮播指示器 -->
      <div class="carousel-indicators">
        <button
          v-for="(bg, index) in backgroundImages"
          :key="index"
          class="indicator"
          :class="{ active: currentSlide === index }"
          @click="goToSlide(index)"
        ></button>
      </div>

      <!-- 轮播控制按钮 -->
      <div class="carousel-controls">
        <button class="control-btn prev" @click="prevSlide">
          <el-icon><ArrowLeft /></el-icon>
        </button>
        <button class="control-btn next" @click="nextSlide">
          <el-icon><ArrowRight /></el-icon>
        </button>
      </div>

      <div class="particles-bg" ref="particlesBg"></div>
      <div class="banner-overlay"></div>
      <div class="banner-content">
        <div class="banner-text" data-aos="fade-up" data-aos-duration="1000">
          <h1 class="banner-title">
            <span class="title-line">网络安全</span>
            <span class="title-line highlight">我们共同的责任</span>
          </h1>
          <p class="banner-subtitle" data-aos="fade-up" data-aos-delay="200">
            <span class="typing-text">加入衍盾星云 · 安全可信</span>
          </p>
          <p class="banner-desc" data-aos="fade-up" data-aos-delay="400">
            和众多企业一起，共建网络空间共同体
          </p>
          <div class="banner-actions" data-aos="fade-up" data-aos-delay="600">
            <button class="btn-primary glow-btn" @click="goToLogin">
              立即开始
            </button>
            <button class="btn-secondary" @click="goToHelp">
              <el-icon><QuestionFilled /></el-icon>
              帮助中心
            </button>
          </div>
        </div>
        <div class="banner-visual" data-aos="fade-left" data-aos-delay="800">
          <div class="cyber-grid">
            <div class="grid-lines"></div>
            <div class="floating-elements">
              <div class="floating-icon network">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="floating-icon security">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="floating-icon data">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="floating-icon warning">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="floating-icon monitor">
                <el-icon><Cpu /></el-icon>
              </div>
            </div>
            <div class="grid-overlay"></div>
          </div>
        </div>
      </div>

      <!-- 向下滑动提示 -->
      <div class="scroll-hint" @click="scrollToNext">
        <div class="scroll-text">了解更多请向下滑动~</div>
        <div class="scroll-arrow">
          <el-icon><ArrowDown /></el-icon>
        </div>
      </div>
    </section>



    <!-- 平台理念 -->
    <section class="philosophy">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          <span class="title-bg">创新 · 共赢 · 安全 · 信任</span>
        </h2>
        <div class="philosophy-grid" data-aos="fade-up" data-aos-delay="200">
          <div class="philosophy-item">
            <div class="philosophy-icon innovation">
              <svg viewBox="0 0 1024 1024" width="40" height="40">
                <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF6B35"/>
                <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FF8A65"/>
                <path d="M512 256c141.376 0 256 114.624 256 256s-114.624 256-256 256-256-114.624-256-256 114.624-256 256-256z" fill="#FFB74D"/>
                <path d="M448 384h128v128h-128z" fill="#FFF"/>
                <path d="M480 320h64v64h-64z" fill="#FFF"/>
                <path d="M480 576h64v64h-64z" fill="#FFF"/>
              </svg>
            </div>
            <h3>创新驱动</h3>
            <p>持续技术创新，引领安全发展</p>
          </div>
          <div class="philosophy-item">
            <div class="philosophy-icon cooperation">
              <svg viewBox="0 0 1024 1024" width="40" height="40">
                <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#4CAF50"/>
                <path d="M320 384c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF"/>
                <path d="M704 384c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF"/>
                <path d="M512 448c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF"/>
                <path d="M256 576h512v64H256z" fill="#FFF"/>
                <path d="M320 640v64h64v-64h256v64h64v-64" fill="#FFF"/>
              </svg>
            </div>
            <h3>合作共赢</h3>
            <p>携手共建，实现多方共赢</p>
          </div>
          <div class="philosophy-item">
            <div class="philosophy-icon security">
              <svg viewBox="0 0 1024 1024" width="40" height="40">
                <path d="M512 64l320 128v256c0 177.664-123.392 326.592-288 366.336C379.392 774.592 256 625.664 256 448V192L512 64z" fill="#2196F3"/>
                <path d="M512 128l256 102.4v217.6c0 142.336-98.816 261.248-230.4 293.056C405.216 708.848 320 589.936 320 448V230.4L512 128z" fill="#42A5F5"/>
                <path d="M448 384l64 64 128-128" stroke="#FFF" stroke-width="32" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3>安全至上</h3>
            <p>以安全为核心，保障用户利益</p>
          </div>
          <div class="philosophy-item">
            <div class="philosophy-icon trust">
              <svg viewBox="0 0 1024 1024" width="40" height="40">
                <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#9C27B0"/>
                <path d="M512 192l96 192h192l-160 128 64 192-192-128-192 128 64-192-160-128h192z" fill="#FFD700"/>
                <path d="M512 256l64 128h128l-104 80 40 128-128-88-128 88 40-128-104-80h128z" fill="#FFC107"/>
              </svg>
            </div>
            <h3>信任基石</h3>
            <p>建立可信体系，赢得用户信赖</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 平台介绍 -->
    <section class="products">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          <span class="title-bg">平台服务</span>
        </h2>
        <div class="product-list">
          <div class="product-item" data-aos="flip-left" data-aos-delay="100">
            <div class="product-card">
              <div class="product-icon">
                <div class="icon-bg vulnerability">
                  <svg viewBox="0 0 1024 1024" width="50" height="50">
                    <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF5722"/>
                    <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FF7043"/>
                    <path d="M400 320h224v64H400z" fill="#FFF"/>
                    <path d="M400 416h224v64H400z" fill="#FFF"/>
                    <path d="M400 512h160v64H400z" fill="#FFF"/>
                    <path d="M640 320l64 64-64 64-64-64z" fill="#FFD700"/>
                    <path d="M320 576h384v64H320z" fill="#FFF"/>
                    <path d="M448 672h128v32H448z" fill="#FFF"/>
                  </svg>
                </div>
                <div class="icon-glow"></div>
              </div>
              <h3 class="product-title">漏洞悬赏</h3>
              <p class="product-desc">基于区块链技术的漏洞发现与奖励系统，激励优质安全研究</p>
              <div class="product-features">
                <span class="feature-tag">区块链</span>
                <span class="feature-tag">智能合约</span>
                <span class="feature-tag">自动奖励</span>
              </div>
              <div class="product-hover-effect"></div>
            </div>
          </div>
          <div class="product-item" data-aos="flip-left" data-aos-delay="200">
            <div class="product-card">
              <div class="product-icon">
                <div class="icon-bg assessment">
                  <svg viewBox="0 0 1024 1024" width="50" height="50">
                    <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#2196F3"/>
                    <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#42A5F5"/>
                    <circle cx="400" cy="400" r="80" fill="none" stroke="#FFF" stroke-width="16"/>
                    <path d="M360 400l20 20 40-40" stroke="#FFF" stroke-width="12" fill="none" stroke-linecap="round"/>
                    <path d="M320 560h384v32H320z" fill="#FFF"/>
                    <path d="M320 624h256v24H320z" fill="#FFF"/>
                    <path d="M320 672h320v24H320z" fill="#FFF"/>
                    <path d="M680 360l40 40-40 40" stroke="#FFD700" stroke-width="12" fill="none" stroke-linecap="round"/>
                  </svg>
                </div>
                <div class="icon-glow"></div>
              </div>
              <h3 class="product-title">安全评估</h3>
              <p class="product-desc">全方位系统安全体检与风险评估，精准定位潜在安全隐患</p>
              <div class="product-features">
                <span class="feature-tag">全面扫描</span>
                <span class="feature-tag">风险评估</span>
                <span class="feature-tag">专业报告</span>
              </div>
              <div class="product-hover-effect"></div>
            </div>
          </div>
          <div class="product-item" data-aos="flip-left" data-aos-delay="300">
            <div class="product-card">
              <div class="product-icon">
                <div class="icon-bg monitoring">
                  <svg viewBox="0 0 1024 1024" width="50" height="50">
                    <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#4CAF50"/>
                    <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#66BB6A"/>
                    <circle cx="512" cy="400" r="120" fill="none" stroke="#FFF" stroke-width="16"/>
                    <circle cx="512" cy="400" r="80" fill="none" stroke="#FFF" stroke-width="12"/>
                    <circle cx="512" cy="400" r="40" fill="#FFF"/>
                    <path d="M320 600h384v32H320z" fill="#FFF"/>
                    <path d="M360 650h304v20H360z" fill="#FFF"/>
                    <path d="M400 680h224v20H400z" fill="#FFF"/>
                    <circle cx="720" cy="320" r="24" fill="#FF5722"/>
                    <circle cx="720" cy="320" r="16" fill="#FF8A65" opacity="0.8"/>
                  </svg>
                </div>
                <div class="icon-glow"></div>
              </div>
              <h3 class="product-title">威胁监测</h3>
              <p class="product-desc">实时监控与智能分析，第一时间发现并应对网络安全威胁</p>
              <div class="product-features">
                <span class="feature-tag">实时监控</span>
                <span class="feature-tag">AI分析</span>
                <span class="feature-tag">快速响应</span>
              </div>
              <div class="product-hover-effect"></div>
            </div>
          </div>
          <div class="product-item" data-aos="flip-left" data-aos-delay="400">
            <div class="product-card">
              <div class="product-icon">
                <div class="icon-bg emergency">
                  <svg viewBox="0 0 1024 1024" width="50" height="50">
                    <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF9800"/>
                    <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FFB74D"/>
                    <path d="M400 280l112 160-112 160-112-160z" fill="#FFF"/>
                    <path d="M512 320l80 120-80 120-80-120z" fill="#FF5722"/>
                    <circle cx="512" cy="440" r="32" fill="#FFF"/>
                    <path d="M320 640h384v32H320z" fill="#FFF"/>
                    <path d="M360 680h304v20H360z" fill="#FFF"/>
                    <path d="M400 710h224v20H400z" fill="#FFF"/>
                    <circle cx="680" cy="320" r="20" fill="#FF5722"/>
                    <path d="M680 300v40M660 320h40" stroke="#FFF" stroke-width="4"/>
                  </svg>
                </div>
                <div class="icon-glow"></div>
              </div>
              <h3 class="product-title">应急响应</h3>
              <p class="product-desc">专业的安全事件快速响应与修复方案，最小化安全事件损失</p>
              <div class="product-features">
                <span class="feature-tag">7x24服务</span>
                <span class="feature-tag">专家团队</span>
                <span class="feature-tag">快速修复</span>
              </div>
              <div class="product-hover-effect"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 为什么选择我们 -->
    <section class="why-choose-us">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          <span class="title-bg">为什么选择衍盾星云</span>
        </h2>
        <div class="features-grid">
          <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
            <div class="feature-number">01</div>
            <div class="feature-content">
              <div class="feature-icon blockchain">
                <svg viewBox="0 0 1024 1024" width="40" height="40">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#6366F1"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#8B5CF6"/>
                  <rect x="320" y="320" width="120" height="120" rx="16" fill="#FFF"/>
                  <rect x="584" y="320" width="120" height="120" rx="16" fill="#FFF"/>
                  <rect x="320" y="584" width="120" height="120" rx="16" fill="#FFF"/>
                  <rect x="584" y="584" width="120" height="120" rx="16" fill="#FFF"/>
                  <path d="M440 380h144v8H440z" fill="#6366F1"/>
                  <path d="M440 644h144v8H440z" fill="#6366F1"/>
                  <path d="M380 440v144h8V440z" fill="#6366F1"/>
                  <path d="M644 440v144h8V440z" fill="#6366F1"/>
                  <circle cx="380" cy="380" r="8" fill="#FFD700"/>
                  <circle cx="644" cy="380" r="8" fill="#FFD700"/>
                  <circle cx="380" cy="644" r="8" fill="#FFD700"/>
                  <circle cx="644" cy="644" r="8" fill="#FFD700"/>
                </svg>
              </div>
              <h3 class="feature-title">区块链技术保障</h3>
              <p class="feature-desc">利用区块链不可篡改特性，确保漏洞提交记录真实可信，智能合约自动执行奖励结算</p>
              <div class="feature-stats">
                <span class="stat">100% 可信</span>
                <span class="stat">自动结算</span>
              </div>
            </div>
            <div class="feature-bg-effect"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
            <div class="feature-number">02</div>
            <div class="feature-content">
              <div class="feature-icon detection">
                <svg viewBox="0 0 1024 1024" width="40" height="40">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#10B981"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#34D399"/>
                  <path d="M320 600h80v80h-80z" fill="#FFF"/>
                  <path d="M440 520h80v160h-80z" fill="#FFF"/>
                  <path d="M560 440h80v240h-80z" fill="#FFF"/>
                  <path d="M680 360h80v320h-80z" fill="#FFF"/>
                  <path d="M320 320l120 80 120-80 120 80" stroke="#FFD700" stroke-width="6" fill="none" stroke-linecap="round"/>
                  <circle cx="320" cy="320" r="12" fill="#FF5722"/>
                  <circle cx="440" cy="400" r="12" fill="#FF5722"/>
                  <circle cx="560" cy="320" r="12" fill="#FF5722"/>
                  <circle cx="680" cy="400" r="12" fill="#FF5722"/>
                  <text x="360" y="300" fill="#FFF" font-size="24" font-weight="bold">+25%</text>
                </svg>
              </div>
              <h3 class="feature-title">可以多检测出业务逻辑漏洞</h3>
              <p class="feature-desc">白帽深入剖析业务逻辑，全面细致的发现可利用弱点，帮助企业强化业务健壮性</p>
              <div class="feature-stats">
                <span class="stat">提升发现率</span>
                <span class="stat">深度分析</span>
              </div>
            </div>
            <div class="feature-bg-effect"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
            <div class="feature-number">03</div>
            <div class="feature-content">
              <div class="feature-icon timer">
                <svg viewBox="0 0 1024 1024" width="40" height="40">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#F59E0B"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FBBF24"/>
                  <circle cx="512" cy="512" r="200" fill="none" stroke="#FFF" stroke-width="16"/>
                  <circle cx="512" cy="512" r="160" fill="none" stroke="#FFF" stroke-width="8"/>
                  <path d="M512 352v160l80 80" stroke="#FFF" stroke-width="12" fill="none" stroke-linecap="round"/>
                  <circle cx="512" cy="512" r="16" fill="#FFF"/>
                  <path d="M512 200v40M824 512h-40M512 824v-40M200 512h40" stroke="#FFF" stroke-width="8" stroke-linecap="round"/>
                  <text x="440" y="420" fill="#FF5722" font-size="32" font-weight="bold">90%</text>
                  <path d="M680 344l-40 40" stroke="#FF5722" stroke-width="8" stroke-linecap="round"/>
                </svg>
              </div>
              <h3 class="feature-title">缩短至少漏洞暴露窗口期</h3>
              <p class="feature-desc">依托平台工作流系统，在漏洞发现后的第一时间及时推送给企业，协助企业快速处置</p>
              <div class="feature-stats">
                <span class="stat">减少暴露期</span>
                <span class="stat">实时推送</span>
              </div>
            </div>
            <div class="feature-bg-effect"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
            <div class="feature-number">04</div>
            <div class="feature-content">
              <div class="feature-icon expert">
                <svg viewBox="0 0 1024 1024" width="40" height="40">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#EC4899"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#F472B6"/>
                  <circle cx="512" cy="400" r="80" fill="#FFF"/>
                  <path d="M512 320c-44 0-80 36-80 80s36 80 80 80 80-36 80-80-36-80-80-80z" fill="#EC4899"/>
                  <path d="M320 680c0-106 86-192 192-192s192 86 192 192v40H320v-40z" fill="#FFF"/>
                  <path d="M360 680c0-84 68-152 152-152s152 68 152 152v40H360v-40z" fill="#EC4899"/>
                  <circle cx="400" cy="320" r="24" fill="#FFD700"/>
                  <circle cx="624" cy="320" r="24" fill="#FFD700"/>
                  <circle cx="400" cy="600" r="24" fill="#FFD700"/>
                  <circle cx="624" cy="600" r="24" fill="#FFD700"/>
                  <path d="M400 320l24 24M624 320l-24 24M400 600l24-24M624 600l-24-24" stroke="#FFD700" stroke-width="4"/>
                </svg>
              </div>
              <h3 class="feature-title">白帽专家众多</h3>
              <p class="feature-desc">根据企业测试需求和重点匹配适合的白帽子人选，提供高价值漏洞，让漏洞无处可逃</p>
              <div class="feature-stats">
                <span class="stat">精准匹配</span>
                <span class="stat">专家团队</span>
              </div>
            </div>
            <div class="feature-bg-effect"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="scenarios">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">
          <span class="title-bg">应用场景</span>
        </h2>
        <div class="scenarios-grid" data-aos="fade-up" data-aos-delay="200">
          <div class="scenario-card enterprise" data-aos="fade-up" data-aos-delay="300">
            <div class="scenario-header">
              <div class="scenario-icon">
                <svg viewBox="0 0 1024 1024" width="48" height="48">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#3B82F6"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#60A5FA"/>
                  <rect x="320" y="280" width="384" height="280" rx="16" fill="#FFF"/>
                  <rect x="340" y="300" width="344" height="40" fill="#3B82F6"/>
                  <rect x="360" y="360" width="80" height="80" rx="8" fill="#E5E7EB"/>
                  <rect x="460" y="360" width="80" height="80" rx="8" fill="#E5E7EB"/>
                  <rect x="560" y="360" width="80" height="80" rx="8" fill="#E5E7EB"/>
                  <rect x="360" y="460" width="280" height="20" rx="4" fill="#D1D5DB"/>
                  <rect x="360" y="500" width="200" height="20" rx="4" fill="#D1D5DB"/>
                </svg>
              </div>
              <div class="scenario-badge">企业首选</div>
            </div>
            <h3 class="scenario-title">企业安全建设</h3>
            <p class="scenario-subtitle">业务驱动场景</p>
            <div class="scenario-features">
              <div class="feature-item">
                <span>企业注重服务品质和效果提升</span>
              </div>
              <div class="feature-item">
                <span>业务变更频繁，需要持续安全保障</span>
              </div>
              <div class="feature-item">
                <span>实战化验证企业安全能力建设</span>
              </div>
            </div>
          </div>

          <div class="scenario-card risk" data-aos="fade-up" data-aos-delay="400">
            <div class="scenario-header">
              <div class="scenario-icon">
                <svg viewBox="0 0 1024 1024" width="48" height="48">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#EF4444"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#F87171"/>
                  <path d="M512 200L600 400H424L512 200z" fill="#FFF"/>
                  <circle cx="512" cy="480" r="24" fill="#FFF"/>
                  <rect x="500" y="520" width="24" height="120" rx="12" fill="#FFF"/>
                  <path d="M400 680h224l-16 40H416l-16-40z" fill="#FFF"/>
                </svg>
              </div>
              <div class="scenario-badge">风险控制</div>
            </div>
            <h3 class="scenario-title">风险预防控制</h3>
            <p class="scenario-subtitle">预知风险场景</p>
            <div class="scenario-features">
              <div class="feature-item">
                <span>系统上线前外部安全测试验证</span>
              </div>
              <div class="feature-item">
                <span>重保前专项体检和漏洞修复</span>
              </div>
              <div class="feature-item">
                <span>合规驱动的未知安全隐患发现</span>
              </div>
            </div>
          </div>

          <div class="scenario-card intelligent" data-aos="fade-up" data-aos-delay="500">
            <div class="scenario-header">
              <div class="scenario-icon">
                <svg viewBox="0 0 1024 1024" width="48" height="48">
                  <path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#8B5CF6"/>
                  <path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#A78BFA"/>
                  <circle cx="512" cy="400" r="80" fill="#FFF"/>
                  <circle cx="512" cy="400" r="40" fill="#8B5CF6"/>
                  <path d="M400 500h224v120H400z" fill="#FFF"/>
                  <rect x="420" y="520" width="40" height="8" fill="#8B5CF6"/>
                  <rect x="480" y="520" width="40" height="8" fill="#8B5CF6"/>
                  <rect x="540" y="520" width="40" height="8" fill="#8B5CF6"/>
                  <rect x="420" y="540" width="60" height="8" fill="#A78BFA"/>
                  <rect x="500" y="540" width="80" height="8" fill="#A78BFA"/>
                  <rect x="420" y="560" width="40" height="8" fill="#C4B5FD"/>
                  <rect x="480" y="560" width="100" height="8" fill="#C4B5FD"/>
                  <circle cx="400" cy="350" r="16" fill="#FFD700"/>
                  <circle cx="624" cy="350" r="16" fill="#FFD700"/>
                  <circle cx="400" cy="450" r="16" fill="#FFD700"/>
                  <circle cx="624" cy="450" r="16" fill="#FFD700"/>
                  <path d="M400 350l24 24M624 350l-24 24M400 450l24-24M624 450l-24-24" stroke="#FFD700" stroke-width="3"/>
                </svg>
              </div>
              <div class="scenario-badge">AI驱动</div>
            </div>
            <h3 class="scenario-title">智能化运营</h3>
            <p class="scenario-subtitle">技术创新场景</p>
            <div class="scenario-features">
              <div class="feature-item">
                <span>AI驱动的智能威胁检测</span>
              </div>
              <div class="feature-item">
                <span>区块链促使安全中心建设</span>
              </div>
              <div class="feature-item">
                <span>区块链技术保障数据可信</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <AppFooter />
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import auth from '../utils/auth';
import TheHeader from '../components/TheHeader.vue';
import AppFooter from '../components/AppFooter.vue';
import AOS from 'aos';
import 'aos/dist/aos.css';
import {
  QuestionFilled,
  Connection,
  Lock,
  WarningFilled,
  Cpu,
  ArrowLeft,
  ArrowRight,
  ArrowDown,
  TrendCharts
} from '@element-plus/icons-vue';

export default {
  name: 'Home',
  components: {
    TheHeader,
    AppFooter,
    QuestionFilled,
    Connection,
    Lock,
    WarningFilled,
    Cpu,
    ArrowLeft,
    ArrowRight,
    ArrowDown,
    TrendCharts
  },
  setup() {
    const currentUser = ref(null);
    const router = useRouter();
    const showDropdown = ref(false);
    const userDropdown = ref(null);
    const particlesBg = ref(null);

    // 轮播图相关数据
    const currentSlide = ref(0);
    const backgroundImages = ref([
      '/assets/images/bg1.png',
      '/assets/images/bg2.png',
      '/assets/images/bg3.png',
      '/assets/images/bg4.png',
      '/assets/images/bg5.png'
    ]);
    let carouselTimer = null;

    const checkLoginStatus = async () => {
      if (auth.isLoggedIn()) {
        currentUser.value = auth.getCurrentUser();
        console.log('当前用户类型:', currentUser.value.userType);

        // 异步验证登录状态
        try {
          const isValid = await auth.validateLoginStatus();
          if (!isValid) {
            currentUser.value = null;
          }
        } catch (error) {
          console.error('验证登录状态失败:', error);
        }
      } else {
        currentUser.value = null;
      }
    };



    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value;
    };

    const handleClickOutside = (event) => {
      if (userDropdown.value && !userDropdown.value.contains(event.target)) {
        showDropdown.value = false;
      }
    };

    // 按钮点击事件
    const goToLogin = () => {
      router.push('/login');
    };

    const goToHelp = () => {
      router.push('/help');
    };

    // 滚动到下一个section
    const scrollToNext = () => {
      const nextSection = document.querySelector('.philosophy');
      if (nextSection) {
        nextSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    };

    // 轮播图控制方法
    const nextSlide = () => {
      currentSlide.value = (currentSlide.value + 1) % backgroundImages.value.length;
    };

    const prevSlide = () => {
      currentSlide.value = currentSlide.value === 0
        ? backgroundImages.value.length - 1
        : currentSlide.value - 1;
    };

    const goToSlide = (index) => {
      currentSlide.value = index;
    };

    // 自动轮播
    const startCarousel = () => {
      carouselTimer = setInterval(() => {
        nextSlide();
      }, 4000); // 每4秒切换一次
    };

    const stopCarousel = () => {
      if (carouselTimer) {
        clearInterval(carouselTimer);
        carouselTimer = null;
      }
    };

    // 创建粒子背景
    const createParticles = () => {
      if (!particlesBg.value) return;

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      particlesBg.value.appendChild(canvas);

      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;

      const particles = [];
      const particleCount = 50;

      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 2 + 1,
          opacity: Math.random() * 0.5 + 0.2
        });
      }

      const animate = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
          particle.x += particle.vx;
          particle.y += particle.vy;

          if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
          if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
          ctx.fill();
        });

        // 连接粒子
        particles.forEach((particle, i) => {
          particles.slice(i + 1).forEach(otherParticle => {
            const dx = particle.x - otherParticle.x;
            const dy = particle.y - otherParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(particle.x, particle.y);
              ctx.lineTo(otherParticle.x, otherParticle.y);
              ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`;
              ctx.stroke();
            }
          });
        });

        requestAnimationFrame(animate);
      };

      animate();
    };

    // 数字动画
    const animateNumbers = () => {
      const statNums = document.querySelectorAll('.stat-num');
      statNums.forEach(num => {
        const target = parseInt(num.getAttribute('data-count'));
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            current = target;
            clearInterval(timer);
          }
          num.textContent = Math.floor(current) + (target === 99 ? '%' : '+');
        }, 20);
      });
    };

    // 打字机效果
    const typeWriter = () => {
      const element = document.querySelector('.typing-text');
      if (!element) return;

      const text = element.textContent;
      element.textContent = '';
      let i = 0;

      const timer = setInterval(() => {
        if (i < text.length) {
          element.textContent += text.charAt(i);
          i++;
        } else {
          clearInterval(timer);
        }
      }, 100);
    };

    // 初始加载
    onMounted(async () => {
      checkLoginStatus();
      document.addEventListener('click', handleClickOutside);

      // 初始化AOS动画
      AOS.init({
        duration: 1000,
        once: true,
        offset: 100
      });

      await nextTick();
      createParticles();
      startCarousel(); // 启动轮播

      // 延迟执行动画
      setTimeout(() => {
        animateNumbers();
        typeWriter();
      }, 1000);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside);
      stopCarousel(); // 停止轮播
    });

    return {
      currentUser,
      showDropdown,
      toggleDropdown,
      userDropdown,
      particlesBg,
      goToLogin,
      goToHelp,
      scrollToNext,
      // 轮播图相关
      currentSlide,
      backgroundImages,
      nextSlide,
      prevSlide,
      goToSlide
    };
  }
};
</script>

<style scoped>
/* 全局变量 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --tech-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --glow-color: #00f2fe;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --bg-light: #f8fafc;
  --bg-dark: #1a202c;
  --border-radius: 16px;
  --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-light);
}

/* 横幅样式 */
.banner {
  margin-top: 60px;
  min-height: calc(100vh - 50px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
  overflow: hidden;
}

/* 轮播背景 */
.carousel-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 4;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.indicator:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicator.active {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* 轮播控制按钮 */
.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  z-index: 4;
  pointer-events: none;
}

.control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  pointer-events: auto;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.control-btn.prev {
  left: 0;
}

.control-btn.next {
  right: 0;
}

/* 向下滑动提示 */
.scroll-hint {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  z-index: 6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scroll-hint:hover {
  transform: translateX(-50%) translateY(-5px);
}

.scroll-text {
  font-size: 16px;
  margin-bottom: 8px;
  opacity: 0.9;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scroll-arrow {
  font-size: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .carousel-controls {
    padding: 0 15px;
  }

  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .carousel-indicators {
    bottom: 20px;
    gap: 8px;
  }

  .indicator {
    width: 10px;
    height: 10px;
  }

  .scroll-hint {
    bottom: 20px;
  }

  .scroll-text {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .scroll-arrow {
    font-size: 18px;
  }

  .banner-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

.particles-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(1px);
  z-index: 1;
}

.banner-content {
  position: relative;
  z-index: 5;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
  padding: 0 20px;
}

.banner-text {
  text-align: left;
}

.banner-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 30px;
  line-height: 1.2;
}

.title-line {
  display: block;
  margin-bottom: 10px;
}

.title-line.highlight {
  background: linear-gradient(45deg, #00f2fe, #4facfe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.banner-subtitle {
  font-size: 1.5rem;
  margin-bottom: 20px;
  opacity: 0.9;
  font-weight: 300;
}

.typing-text {
  border-right: 2px solid var(--glow-color);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { border-color: var(--glow-color); }
  51%, 100% { border-color: transparent; }
}

.banner-desc {
  font-size: 1.1rem;
  opacity: 0.8;
  margin-bottom: 40px;
  line-height: 1.6;
}

.banner-actions {
  display: flex;
  gap: 20px;
  align-items: center;
}

.btn-primary, .btn-secondary {
  padding: 15px 30px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: var(--tech-gradient);
  color: white;
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.glow-btn {
  position: relative;
  overflow: hidden;
}

.glow-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.glow-btn:hover::before {
  left: 100%;
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.banner-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cyber-grid {
  position: relative;
  width: 450px;
  height: 450px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.grid-lines {
  position: absolute;
  width: 120%;
  height: 120%;
  top: -10%;
  left: -10%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 25px 25px;
  animation: gridMove 20s linear infinite;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, transparent 40%, rgba(255, 255, 255, 0.05) 80%);
  pointer-events: none;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(25px, 25px); }
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.floating-icon {
  position: absolute;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.08));
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.85);
  font-size: 28px;
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: float 8s ease-in-out infinite;
  transition: all 0.3s ease;
}

.floating-icon:hover {
  transform: scale(1.1);
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}

.floating-icon.network {
  top: 15%;
  right: 15%;
  animation-delay: 0s;
}

.floating-icon.security {
  top: 35%;
  left: 10%;
  animation-delay: 1.5s;
}

.floating-icon.monitor {
  top: 25%;
  right: 45%;
  animation-delay: 3s;
}

.floating-icon.data {
  bottom: 25%;
  right: 12%;
  animation-delay: 4.5s;
}

.floating-icon.warning {
  bottom: 15%;
  left: 25%;
  animation-delay: 6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-10px) rotate(270deg);
    opacity: 1;
  }
}



/* 平台理念样式 */
.philosophy {
  padding: 120px 0;
  text-align: center;
  background: var(--bg-light);
  position: relative;
}

.philosophy::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
}

.section-title {
  font-size: 3rem;
  color: var(--text-primary);
  margin-bottom: 60px;
  position: relative;
  display: inline-block;
  font-weight: 800;
}

.title-bg {
  position: relative;
  z-index: 2;
}

.title-bg::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: var(--tech-gradient);
  border-radius: 2px;
}

.philosophy-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.philosophy-item {
  padding: 40px 30px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(79, 172, 254, 0.1);
}

.philosophy-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--tech-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.philosophy-item:hover::before {
  opacity: 0.05;
}

.philosophy-item:hover {
  transform: translateY(-10px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.12),
    0 5px 15px rgba(79, 172, 254, 0.2);
  border-color: rgba(79, 172, 254, 0.3);
}

.philosophy-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.philosophy-icon:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.philosophy-icon.innovation {
  background: linear-gradient(135deg, #FFE0B2, #FFCC80);
}

.philosophy-icon.cooperation {
  background: linear-gradient(135deg, #C8E6C9, #A5D6A7);
}

.philosophy-icon.security {
  background: linear-gradient(135deg, #BBDEFB, #90CAF9);
}

.philosophy-icon.trust {
  background: linear-gradient(135deg, #E1BEE7, #CE93D8);
}

.philosophy-item h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.philosophy-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* 产品介绍样式 */
.products {
  padding: 120px 0;
  background: white;
  position: relative;
}

.product-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.product-item {
  position: relative;
  height: 100%;
}

.product-card {
  height: 100%;
  padding: 40px 30px;
  background: white;
  border-radius: var(--border-radius);
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(79, 172, 254, 0.1);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.05);
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--tech-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover::before {
  opacity: 0.05;
}

.product-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(79, 172, 254, 0.2),
    0 10px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(79, 172, 254, 0.3);
}

.product-icon {
  position: relative;
  margin-bottom: 30px;
  z-index: 2;
}

.icon-bg {
  width: 100px;
  height: 100px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 3;
  transition: all 0.3s ease;
}

.icon-bg:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.icon-bg.vulnerability {
  background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
}

.icon-bg.assessment {
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
}

.icon-bg.monitoring {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
}

.icon-bg.emergency {
  background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: var(--tech-gradient);
  border-radius: 50%;
  opacity: 0;
  filter: blur(20px);
  transition: opacity 0.3s ease;
  z-index: 1;
}

.product-card:hover .icon-glow {
  opacity: 0.3;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
}

.product-desc {
  color: var(--text-secondary);
  margin-bottom: 25px;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.feature-tag {
  padding: 6px 12px;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.product-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(79, 172, 254, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.product-card:hover .product-hover-effect {
  opacity: 1;
}

/* 为什么选择我们样式 */
.why-choose-us {
  padding: 120px 0;
  background: var(--bg-light);
  position: relative;
}

.why-choose-us::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 75% 25%, rgba(245, 87, 108, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 25% 75%, rgba(79, 172, 254, 0.1) 0%, transparent 50%);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.feature-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(79, 172, 254, 0.1);
  border-left: 4px solid rgba(79, 172, 254, 0.3);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--secondary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.12),
    0 5px 15px rgba(79, 172, 254, 0.2);
  border-left-color: rgba(79, 172, 254, 0.6);
  border-color: rgba(79, 172, 254, 0.3);
}

.feature-number {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--tech-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 800;
  color: white;
  box-shadow: var(--shadow-light);
}

.feature-content {
  position: relative;
  z-index: 2;
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
  margin-bottom: 25px;
  box-shadow: var(--shadow-light);
  transition: transform 0.3s ease;
}

.feature-icon:hover {
  transform: scale(1.1);
}

.feature-icon svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.feature-icon.blockchain svg {
  animation: blockchain-pulse 2s ease-in-out infinite;
}

.feature-icon.detection svg {
  animation: detection-bounce 1.5s ease-in-out infinite;
}

.feature-icon.timer svg {
  animation: timer-rotate 3s linear infinite;
}

.feature-icon.expert svg {
  animation: expert-glow 2s ease-in-out infinite alternate;
}

@keyframes blockchain-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes detection-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes timer-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes expert-glow {
  0% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1)); }
  100% { filter: drop-shadow(0 4px 16px rgba(236, 72, 153, 0.3)); }
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-primary);
  line-height: 1.3;
}

.feature-desc {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat {
  padding: 8px 16px;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.feature-bg-effect {
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(79, 172, 254, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover .feature-bg-effect {
  opacity: 1;
}

/* 应用场景样式 */
.scenarios {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.scenarios::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.scenario-card {
  background: white;
  border-radius: 20px;
  padding: 32px 24px;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  height: fit-content;
}

.scenario-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--glow-color));
}

.scenario-card.enterprise::before {
  background: linear-gradient(90deg, #3B82F6, #60A5FA);
}

.scenario-card.risk::before {
  background: linear-gradient(90deg, #EF4444, #F87171);
}

.scenario-card.intelligent::before {
  background: linear-gradient(90deg, #8B5CF6, #A78BFA);
}

.scenario-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15);
}

.scenario-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.scenario-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.scenario-card:hover .scenario-icon {
  transform: scale(1.1) rotate(5deg);
}

.scenario-badge {
  background: linear-gradient(135deg, var(--primary-color), var(--glow-color));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.scenario-card.enterprise .scenario-badge {
  background: linear-gradient(135deg, #3B82F6, #60A5FA);
}

.scenario-card.risk .scenario-badge {
  background: linear-gradient(135deg, #EF4444, #F87171);
}

.scenario-card.intelligent .scenario-badge {
  background: linear-gradient(135deg, #8B5CF6, #A78BFA);
}

.scenario-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.3;
}

.scenario-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 24px;
  font-weight: 500;
}

.scenario-features {
  margin-bottom: 28px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  padding: 10px 14px;
  font-size:30px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 10px;
  border-left: 3px solid var(--glow-color);
  transition: all 0.3s ease;
}

.scenario-card.enterprise .feature-item {
  border-left-color: #3B82F6;
}

.scenario-card.risk .feature-item {
  border-left-color: #EF4444;
}

.scenario-card.intelligent .feature-item {
  border-left-color: #8B5CF6;
}

.feature-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateX(4px);
}

.feature-icon {
  font-size: 1.2rem;
  margin-right: 12px;
  min-width: 24px;
}

.feature-item span:last-child {
  color: var(--text-secondary);
  font-size: 1.05rem;
  line-height: 1.5;
  font-weight: 500;
}

.scenario-stats {
  display: flex;
  gap: 24px;
  padding-top: 24px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.scenario-card.enterprise .stat-number {
  color: #3B82F6;
}

.scenario-card.risk .stat-number {
  color: #EF4444;
}

.scenario-card.intelligent .stat-number {
  color: #8B5CF6;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 应用场景响应式设计 */
@media (max-width: 1200px) {
  .scenarios-grid {
    max-width: 1000px;
    gap: 24px;
  }
}

@media (max-width: 992px) {
  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 32px;
    max-width: 600px;
  }

  .scenario-card {
    padding: 36px 28px;
  }

  .scenario-title {
    font-size: 1.5rem;
  }

  .scenario-subtitle {
    font-size: 1.05rem;
  }

  .feature-item span:last-child {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .scenarios {
    padding: 80px 0;
  }

  .scenarios-grid {
    gap: 24px;
    padding: 0 16px;
  }

  .scenario-card {
    padding: 32px 24px;
    border-radius: 18px;
  }

  .scenario-header {
    margin-bottom: 18px;
  }

  .scenario-icon {
    width: 52px;
    height: 52px;
  }

  .scenario-title {
    font-size: 1.35rem;
  }

  .scenario-subtitle {
    font-size: 1rem;
  }

  .scenario-stats {
    gap: 16px;
  }

  .stat-number {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .scenario-card {
    padding: 24px 20px;
  }

  .scenario-icon {
    width: 48px;
    height: 48px;
  }

  .scenario-title {
    font-size: 1.2rem;
  }

  .feature-item {
    padding: 10px 12px;
    margin-bottom: 12px;
  }

  .feature-icon {
    font-size: 1rem;
    margin-right: 8px;
  }

  .feature-item span:last-child {
    font-size: 0.85rem;
  }
}





/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .banner-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 40px;
  }

  .banner-text {
    text-align: center;
  }

  .philosophy-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* 中等屏幕响应式设计 */
@media screen and (max-width: 1200px) {
  .product-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media screen and (max-width: 768px) {
  .banner {
    min-height: 80vh;
    padding: 40px 0;
  }

  .banner-title {
    font-size: 2.5rem;
  }

  .banner-subtitle {
    font-size: 1.2rem;
  }

  .banner-desc {
    font-size: 1rem;
  }

  .banner-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
    max-width: 250px;
  }

  .statistics-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .philosophy-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .product-list {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .timeline-item {
    flex-direction: column !important;
    text-align: center !important;
  }

  .timeline-item:nth-child(even) .timeline-content {
    text-align: center;
  }

  .scenario-timeline::before {
    display: none;
  }

  .timeline-marker {
    margin: 0 0 20px 0;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .cyber-grid {
    width: 300px;
    height: 300px;
  }

  .element {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 480px) {
  .banner-title {
    font-size: 2rem;
  }

  .product-card,
  .feature-card {
    padding: 30px 20px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .container {
    padding: 0 15px;
  }
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--tech-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gradient);
}

/* 选择文本样式 */
::selection {
  background: var(--glow-color);
  color: white;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 全局动画优化 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
}
</style> 