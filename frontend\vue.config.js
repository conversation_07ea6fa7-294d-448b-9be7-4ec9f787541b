const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
    transpileDependencies: true,

    // 开发服务器配置
    devServer: {
        port: 8080,
        host: '0.0.0.0', // 绑定到所有网络接口，允许通过IP访问
        open: false,
        allowedHosts: 'all', // 允许所有主机访问
        proxy: {
            '/api': {
                target: 'http://*************:3000',
                changeOrigin: true,
                secure: false,
                pathRewrite: {
                    '^/api': '/api'
                }
            }
        }
    },

    // 配置别名
    configureWebpack: {
        resolve: {
            alias: {
                '@': require('path').resolve(__dirname, 'src')
            }
        }
    },

    // 生产环境配置
    publicPath: process.env.NODE_ENV === 'production' ? './' : '/',

    // 关闭生产环境的source map
    productionSourceMap: false,

    // CSS配置
    css: {
        extract: process.env.NODE_ENV === 'production'
    }
})