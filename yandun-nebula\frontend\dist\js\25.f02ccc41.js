"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[25],{72025:(e,l,t)=>{t.r(l),t.d(l,{default:()=>ye});t(52675),t(89463);var n=t(95976),a=t(10160),i={class:"review-task-container"},r={class:"container main-content"},c={class:"filter-section"},o={class:"pagination-container"},s={key:0,class:"vulnerability-details"},u={class:"detail-header"},d={class:"detail-title"},f={class:"detail-meta"},b={class:"meta-item"},k={class:"meta-item"},m={key:0,class:"detail-section blockchain-section"},v={class:"section-title"},p={class:"blockchain-info-card"},h={class:"blockchain-fields"},y={class:"field-row"},g={class:"field-item"},_={class:"field-value hash-value"},L={class:"hash-text"},F={class:"field-row"},V={class:"field-item"},C={class:"field-value hash-value"},w={class:"hash-text"},x={class:"field-row"},A={class:"field-item"},S={class:"field-value"},T={key:1,class:"detail-section blockchain-section"},z={class:"section-title"},W={class:"blockchain-info-card"},j={class:"blockchain-fields"},R={class:"field-row"},K={class:"field-item"},U={class:"field-value hash-value"},D={class:"hash-text"},H={class:"field-row"},E={class:"field-item"},X={class:"field-value hash-value"},I={class:"hash-text"},B={class:"field-row"},Q={class:"field-item"},N={class:"field-value"},q={class:"detail-section"},J={class:"section-content"},O={class:"detail-section"},P={class:"section-content"},G={key:2,class:"detail-section"},M={class:"section-content"},Y={class:"detail-section"},Z={class:"section-content"},$={key:3,class:"detail-section"},ee={class:"url-list"},le={class:"url-text"},te={key:4,class:"detail-section"},ne={class:"attachment-list"},ae={class:"attachment-text"},ie={class:"dialog-footer"},re={class:"dialog-footer"},ce={class:"dialog-footer"};function oe(e,l,t,oe,se,ue){var de=(0,n.g2)("TheHeader"),fe=(0,n.g2)("el-input"),be=(0,n.g2)("el-option"),ke=(0,n.g2)("el-select"),me=(0,n.g2)("el-button"),ve=(0,n.g2)("el-card"),pe=(0,n.g2)("el-table-column"),he=(0,n.g2)("el-tag"),ye=(0,n.g2)("el-table"),ge=(0,n.g2)("el-pagination"),_e=(0,n.g2)("el-divider"),Le=(0,n.g2)("el-dialog"),Fe=(0,n.g2)("el-form-item"),Ve=(0,n.g2)("el-input-number"),Ce=(0,n.g2)("el-form"),we=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",i,[(0,n.bF)(de),(0,n.Lk)("div",r,[(0,n.bF)(ve,{class:"filter-card"},{default:(0,n.k6)(function(){return[(0,n.Lk)("div",c,[(0,n.bF)(fe,{modelValue:oe.searchQuery,"onUpdate:modelValue":l[0]||(l[0]=function(e){return oe.searchQuery=e}),placeholder:"搜索漏洞标题或提交者",clearable:"",class:"search-input",onInput:oe.handleSearch},{prefix:(0,n.k6)(function(){return l[19]||(l[19]=[(0,n.Lk)("i",{class:"el-icon-search"},null,-1)])}),_:1},8,["modelValue","onInput"]),(0,n.bF)(ke,{modelValue:oe.filterStatus,"onUpdate:modelValue":l[1]||(l[1]=function(e){return oe.filterStatus=e}),placeholder:"状态筛选",clearable:"",onChange:oe.handleSearch},{default:(0,n.k6)(function(){return[(0,n.bF)(be,{label:"待审核",value:"pending"}),(0,n.bF)(be,{label:"已确认",value:"confirmed"}),(0,n.bF)(be,{label:"已拒绝",value:"rejected"}),(0,n.bF)(be,{label:"已修复",value:"fixed"}),(0,n.bF)(be,{label:"重复提交",value:"duplicate"})]}),_:1},8,["modelValue","onChange"]),(0,n.bF)(ke,{modelValue:oe.filterSeverity,"onUpdate:modelValue":l[2]||(l[2]=function(e){return oe.filterSeverity=e}),placeholder:"严重程度",clearable:"",onChange:oe.handleSearch},{default:(0,n.k6)(function(){return[(0,n.bF)(be,{label:"严重",value:"critical"}),(0,n.bF)(be,{label:"高危",value:"high"}),(0,n.bF)(be,{label:"中危",value:"medium"}),(0,n.bF)(be,{label:"低危",value:"low"}),(0,n.bF)(be,{label:"信息",value:"info"})]}),_:1},8,["modelValue","onChange"]),(0,n.bF)(me,{type:"primary",onClick:oe.handleSearch},{default:(0,n.k6)(function(){return l[20]||(l[20]=[(0,n.eW)("搜索")])}),_:1,__:[20]},8,["onClick"]),(0,n.bF)(me,{onClick:oe.resetFilters},{default:(0,n.k6)(function(){return l[21]||(l[21]=[(0,n.eW)("重置")])}),_:1,__:[21]},8,["onClick"])])]}),_:1}),(0,n.bF)(ve,{class:"table-card"},{default:(0,n.k6)(function(){return[l[25]||(l[25]=(0,n.Lk)("div",{class:"table-header"},[(0,n.Lk)("h2",{class:"table-title"},"漏洞列表")],-1)),(0,n.bo)(((0,n.uX)(),(0,n.Wv)(ye,{data:oe.vulnerabilities,border:"",style:{width:"100%"}},{default:(0,n.k6)(function(){return[(0,n.bF)(pe,{prop:"id",label:"ID",width:"80"}),(0,n.bF)(pe,{prop:"title",label:"漏洞标题","min-width":"200"}),(0,n.bF)(pe,{prop:"submitter",label:"提交者",width:"120"}),(0,n.bF)(pe,{prop:"severity",label:"严重程度",width:"100"},{default:(0,n.k6)(function(e){return[(0,n.bF)(he,{type:oe.getSeverityType(e.row.severity)},{default:(0,n.k6)(function(){return[(0,n.eW)((0,a.v_)(oe.getSeverityLabel(e.row.severity)),1)]}),_:2},1032,["type"])]}),_:1}),(0,n.bF)(pe,{prop:"createdAt",label:"提交时间",width:"180"}),(0,n.bF)(pe,{label:"操作",width:"220"},{default:(0,n.k6)(function(e){return[(0,n.bF)(me,{size:"small",onClick:function(l){return oe.viewDetails(e.row)}},{default:(0,n.k6)(function(){return l[22]||(l[22]=[(0,n.eW)("查看")])}),_:2,__:[22]},1032,["onClick"]),(0,n.bF)(me,{size:"small",type:"success",onClick:function(l){return oe.confirmVulnerability(e.row)},disabled:"pending"!==e.row.status},{default:(0,n.k6)(function(){return l[23]||(l[23]=[(0,n.eW)("确认")])}),_:2,__:[23]},1032,["onClick","disabled"]),(0,n.bF)(me,{size:"small",type:"danger",onClick:function(l){return oe.rejectVulnerability(e.row)},disabled:"pending"!==e.row.status},{default:(0,n.k6)(function(){return l[24]||(l[24]=[(0,n.eW)("拒绝")])}),_:2,__:[24]},1032,["onClick","disabled"])]}),_:1})]}),_:1},8,["data"])),[[we,oe.loading]]),(0,n.Lk)("div",o,[(0,n.bF)(ge,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:oe.total,"page-size":oe.pageSize,"current-page":oe.currentPage,"page-sizes":[10,20,50,100],onSizeChange:oe.handleSizeChange,onCurrentChange:oe.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]}),_:1,__:[25]})]),(0,n.bF)(Le,{title:"漏洞详情",modelValue:oe.dialogVisible,"onUpdate:modelValue":l[9]||(l[9]=function(e){return oe.dialogVisible=e}),width:"70%","before-close":oe.handleDialogClose},{footer:(0,n.k6)(function(){return[(0,n.Lk)("span",ie,[(0,n.bF)(me,{onClick:oe.handleDialogClose},{default:(0,n.k6)(function(){return l[54]||(l[54]=[(0,n.eW)("关闭")])}),_:1,__:[54]},8,["onClick"]),(0,n.bF)(me,{type:"success",onClick:l[7]||(l[7]=function(e){return oe.confirmVulnerability(oe.selectedVulnerability)}),disabled:!oe.selectedVulnerability||"pending"!==oe.selectedVulnerability.status},{default:(0,n.k6)(function(){return l[55]||(l[55]=[(0,n.eW)("确认")])}),_:1,__:[55]},8,["disabled"]),(0,n.bF)(me,{type:"danger",onClick:l[8]||(l[8]=function(e){return oe.rejectVulnerability(oe.selectedVulnerability)}),disabled:!oe.selectedVulnerability||"pending"!==oe.selectedVulnerability.status},{default:(0,n.k6)(function(){return l[56]||(l[56]=[(0,n.eW)("拒绝")])}),_:1,__:[56]},8,["disabled"])])]}),default:(0,n.k6)(function(){return[oe.selectedVulnerability?((0,n.uX)(),(0,n.CE)("div",s,[(0,n.Lk)("div",u,[(0,n.Lk)("h3",d,(0,a.v_)(oe.selectedVulnerability.title),1),(0,n.Lk)("div",f,[(0,n.bF)(he,{type:oe.getSeverityType(oe.selectedVulnerability.severity),class:"meta-item"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,a.v_)(oe.getSeverityLabel(oe.selectedVulnerability.severity)),1)]}),_:1},8,["type"]),(0,n.Lk)("span",b,"提交者: "+(0,a.v_)(oe.selectedVulnerability.submitter),1),(0,n.Lk)("span",k,"提交时间: "+(0,a.v_)(oe.selectedVulnerability.createdAt),1)])]),(0,n.bF)(_e),oe.hasSubmissionBlockchainInfo(oe.selectedVulnerability)?((0,n.uX)(),(0,n.CE)("div",m,[(0,n.Lk)("h4",v,[l[27]||(l[27]=(0,n.Lk)("i",{class:"el-icon-upload"},null,-1)),l[28]||(l[28]=(0,n.eW)(" 漏洞提交记录 - 区块链验证 ")),(0,n.bF)(he,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return l[26]||(l[26]=[(0,n.Lk)("i",{class:"el-icon-check",style:{"margin-right":"4px"}},null,-1),(0,n.eW)(" 链上可验证 ")])}),_:1,__:[26]})]),(0,n.Lk)("div",p,[(0,n.Lk)("div",h,[(0,n.Lk)("div",y,[(0,n.Lk)("div",g,[l[30]||(l[30]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",_,[(0,n.Lk)("span",L,(0,a.v_)(oe.selectedVulnerability.submissionTransactionHash),1),(0,n.bF)(me,{type:"text",size:"small",onClick:l[3]||(l[3]=function(e){return oe.copyToClipboard(oe.selectedVulnerability.submissionTransactionHash)}),class:"copy-btn"},{default:(0,n.k6)(function(){return l[29]||(l[29]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[29]})])])]),(0,n.Lk)("div",F,[(0,n.Lk)("div",V,[l[32]||(l[32]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",C,[(0,n.Lk)("span",w,(0,a.v_)(oe.selectedVulnerability.submissionBlockHash),1),(0,n.bF)(me,{type:"text",size:"small",onClick:l[4]||(l[4]=function(e){return oe.copyToClipboard(oe.selectedVulnerability.submissionBlockHash)}),class:"copy-btn"},{default:(0,n.k6)(function(){return l[31]||(l[31]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[31]})])])]),(0,n.Lk)("div",x,[(0,n.Lk)("div",A,[l[33]||(l[33]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",S,(0,a.v_)(oe.formatTimestamp(oe.selectedVulnerability.submissionBlockchainTimestamp)),1)])])]),l[34]||(l[34]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("i",{class:"el-icon-info notice-icon"}),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"此漏洞提交信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])])):(0,n.Q3)("",!0),oe.hasApprovalBlockchainInfo(oe.selectedVulnerability)?((0,n.uX)(),(0,n.CE)("div",T,[(0,n.Lk)("h4",z,[l[36]||(l[36]=(0,n.Lk)("i",{class:"el-icon-check"},null,-1)),l[37]||(l[37]=(0,n.eW)(" 企业审批记录 - 区块链验证 ")),(0,n.bF)(he,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return l[35]||(l[35]=[(0,n.Lk)("i",{class:"el-icon-check",style:{"margin-right":"4px"}},null,-1),(0,n.eW)(" 链上可验证 ")])}),_:1,__:[35]})]),(0,n.Lk)("div",W,[(0,n.Lk)("div",j,[(0,n.Lk)("div",R,[(0,n.Lk)("div",K,[l[39]||(l[39]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",U,[(0,n.Lk)("span",D,(0,a.v_)(oe.selectedVulnerability.approvalTransactionHash),1),(0,n.bF)(me,{type:"text",size:"small",onClick:l[5]||(l[5]=function(e){return oe.copyToClipboard(oe.selectedVulnerability.approvalTransactionHash)}),class:"copy-btn"},{default:(0,n.k6)(function(){return l[38]||(l[38]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[38]})])])]),(0,n.Lk)("div",H,[(0,n.Lk)("div",E,[l[41]||(l[41]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",X,[(0,n.Lk)("span",I,(0,a.v_)(oe.selectedVulnerability.approvalBlockHash),1),(0,n.bF)(me,{type:"text",size:"small",onClick:l[6]||(l[6]=function(e){return oe.copyToClipboard(oe.selectedVulnerability.approvalBlockHash)}),class:"copy-btn"},{default:(0,n.k6)(function(){return l[40]||(l[40]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[40]})])])]),(0,n.Lk)("div",B,[(0,n.Lk)("div",Q,[l[42]||(l[42]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",N,(0,a.v_)(oe.formatTimestamp(oe.selectedVulnerability.approvalBlockchainTimestamp)),1)])])]),l[43]||(l[43]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("i",{class:"el-icon-info notice-icon"}),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"此企业审批信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])])):(0,n.Q3)("",!0),(0,n.Lk)("div",q,[l[44]||(l[44]=(0,n.Lk)("h4",{class:"section-title"},"漏洞描述",-1)),(0,n.Lk)("p",J,(0,a.v_)(oe.selectedVulnerability.description),1)]),(0,n.Lk)("div",O,[l[45]||(l[45]=(0,n.Lk)("h4",{class:"section-title"},"复现步骤",-1)),(0,n.Lk)("p",P,(0,a.v_)(oe.selectedVulnerability.reproductionSteps||"暂无复现步骤"),1)]),oe.selectedVulnerability.aiAnalysis?((0,n.uX)(),(0,n.CE)("div",G,[l[46]||(l[46]=(0,n.Lk)("h4",{class:"section-title"},"AI建议",-1)),(0,n.Lk)("p",M,(0,a.v_)(oe.selectedVulnerability.aiAnalysis),1)])):(0,n.Q3)("",!0),(0,n.Lk)("div",Y,[l[47]||(l[47]=(0,n.Lk)("h4",{class:"section-title"},"修复建议",-1)),(0,n.Lk)("p",Z,(0,a.v_)(oe.selectedVulnerability.remediation||"暂无修复建议"),1)]),oe.getUrlList(oe.selectedVulnerability).length>0?((0,n.uX)(),(0,n.CE)("div",$,[l[50]||(l[50]=(0,n.Lk)("h4",{class:"section-title"},"相关URL",-1)),(0,n.Lk)("div",ee,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(oe.getUrlList(oe.selectedVulnerability),function(e,t){return(0,n.uX)(),(0,n.CE)("div",{key:t,class:"url-item"},[l[49]||(l[49]=(0,n.Lk)("i",{class:"el-icon-link"},null,-1)),(0,n.Lk)("span",le,(0,a.v_)(e),1),(0,n.bF)(me,{type:"text",size:"small",onClick:function(l){return oe.copyToClipboard(e)}},{default:(0,n.k6)(function(){return l[48]||(l[48]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1),(0,n.eW)(" 复制 ")])}),_:2,__:[48]},1032,["onClick"])])}),128))])])):(0,n.Q3)("",!0),oe.getAttachmentList(oe.selectedVulnerability).length>0?((0,n.uX)(),(0,n.CE)("div",te,[l[53]||(l[53]=(0,n.Lk)("h4",{class:"section-title"},"附件链接",-1)),(0,n.Lk)("div",ne,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(oe.getAttachmentList(oe.selectedVulnerability),function(e,t){return(0,n.uX)(),(0,n.CE)("div",{key:t,class:"attachment-item"},[l[52]||(l[52]=(0,n.Lk)("i",{class:"el-icon-cloudy"},null,-1)),(0,n.Lk)("span",ae,(0,a.v_)(e),1),(0,n.bF)(me,{type:"text",size:"small",onClick:function(l){return oe.copyToClipboard(e)}},{default:(0,n.k6)(function(){return l[51]||(l[51]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1),(0,n.eW)(" 复制链接 ")])}),_:2,__:[51]},1032,["onClick"])])}),128))])])):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0)]}),_:1},8,["modelValue","before-close"]),(0,n.bF)(Le,{title:"确认漏洞",modelValue:oe.confirmDialogVisible,"onUpdate:modelValue":l[14]||(l[14]=function(e){return oe.confirmDialogVisible=e}),width:"50%"},{footer:(0,n.k6)(function(){return[(0,n.Lk)("span",re,[(0,n.bF)(me,{onClick:l[13]||(l[13]=function(e){return oe.confirmDialogVisible=!1})},{default:(0,n.k6)(function(){return l[58]||(l[58]=[(0,n.eW)("取消")])}),_:1,__:[58]}),(0,n.bF)(me,{type:"primary",onClick:oe.submitConfirm,loading:oe.submitting},{default:(0,n.k6)(function(){return l[59]||(l[59]=[(0,n.eW)("确认")])}),_:1,__:[59]},8,["onClick","loading"])])]}),default:(0,n.k6)(function(){return[(0,n.bF)(Ce,{model:oe.confirmForm,"label-width":"120px"},{default:(0,n.k6)(function(){return[(0,n.bF)(Fe,{label:"严重程度"},{default:(0,n.k6)(function(){return[(0,n.bF)(ke,{modelValue:oe.confirmForm.severity,"onUpdate:modelValue":l[10]||(l[10]=function(e){return oe.confirmForm.severity=e}),placeholder:"选择严重程度"},{default:(0,n.k6)(function(){return[(0,n.bF)(be,{label:"严重",value:"critical"}),(0,n.bF)(be,{label:"高危",value:"high"}),(0,n.bF)(be,{label:"中危",value:"medium"}),(0,n.bF)(be,{label:"低危",value:"low"}),(0,n.bF)(be,{label:"信息",value:"info"})]}),_:1},8,["modelValue"])]}),_:1}),(0,n.bF)(Fe,{label:"奖励金额"},{default:(0,n.k6)(function(){return[(0,n.bF)(Ve,{modelValue:oe.confirmForm.reward,"onUpdate:modelValue":l[11]||(l[11]=function(e){return oe.confirmForm.reward=e}),min:0,step:100},null,8,["modelValue"]),l[57]||(l[57]=(0,n.Lk)("span",{class:"reward-unit"},"元",-1))]}),_:1,__:[57]}),(0,n.bF)(Fe,{label:"备注"},{default:(0,n.k6)(function(){return[(0,n.bF)(fe,{type:"textarea",modelValue:oe.confirmForm.comment,"onUpdate:modelValue":l[12]||(l[12]=function(e){return oe.confirmForm.comment=e}),rows:"4",placeholder:"可选，添加确认备注"},null,8,["modelValue"])]}),_:1})]}),_:1},8,["model"])]}),_:1},8,["modelValue"]),(0,n.bF)(Le,{title:"拒绝漏洞",modelValue:oe.rejectDialogVisible,"onUpdate:modelValue":l[18]||(l[18]=function(e){return oe.rejectDialogVisible=e}),width:"50%"},{footer:(0,n.k6)(function(){return[(0,n.Lk)("span",ce,[(0,n.bF)(me,{onClick:l[17]||(l[17]=function(e){return oe.rejectDialogVisible=!1})},{default:(0,n.k6)(function(){return l[60]||(l[60]=[(0,n.eW)("取消")])}),_:1,__:[60]}),(0,n.bF)(me,{type:"danger",onClick:oe.submitReject,loading:oe.submitting},{default:(0,n.k6)(function(){return l[61]||(l[61]=[(0,n.eW)("拒绝")])}),_:1,__:[61]},8,["onClick","loading"])])]}),default:(0,n.k6)(function(){return[(0,n.bF)(Ce,{model:oe.rejectForm,"label-width":"120px"},{default:(0,n.k6)(function(){return[(0,n.bF)(Fe,{label:"拒绝原因",prop:"reason",required:""},{default:(0,n.k6)(function(){return[(0,n.bF)(ke,{modelValue:oe.rejectForm.reason,"onUpdate:modelValue":l[15]||(l[15]=function(e){return oe.rejectForm.reason=e}),placeholder:"选择拒绝原因"},{default:(0,n.k6)(function(){return[(0,n.bF)(be,{label:"不是漏洞",value:"not_vulnerability"}),(0,n.bF)(be,{label:"重复提交",value:"duplicate"}),(0,n.bF)(be,{label:"超出范围",value:"out_of_scope"}),(0,n.bF)(be,{label:"信息不足",value:"insufficient_info"}),(0,n.bF)(be,{label:"其他原因",value:"other"})]}),_:1},8,["modelValue"])]}),_:1}),(0,n.bF)(Fe,{label:"详细说明",prop:"comment",required:""},{default:(0,n.k6)(function(){return[(0,n.bF)(fe,{type:"textarea",modelValue:oe.rejectForm.comment,"onUpdate:modelValue":l[16]||(l[16]=function(e){return oe.rejectForm.comment=e}),rows:"4",placeholder:"请详细说明拒绝原因"},null,8,["modelValue"])]}),_:1})]}),_:1},8,["model"])]}),_:1},8,["modelValue"])])}var se=t(55593),ue=t(24059),de=t(698),fe=(t(28706),t(64346),t(23288),t(79432),t(12040)),be=t(18057),ke=t(36149),me=t(80401);const ve={name:"ReviewTask",components:{TheHeader:me.A},setup:function(){var e=(0,fe.KR)(!1),l=(0,fe.KR)(!1),t=(0,fe.KR)([]),a=(0,fe.KR)(0),i=(0,fe.KR)(10),r=(0,fe.KR)(1),c=(0,fe.KR)(""),o=(0,fe.KR)(""),s=(0,fe.KR)(""),u=(0,fe.KR)(!1),d=(0,fe.KR)(!1),f=(0,fe.KR)(!1),b=(0,fe.KR)(null),k=(0,fe.Kh)({severity:"",reward:0,comment:""}),m=(0,fe.Kh)({reason:"",comment:""}),v=function(){var l=(0,de.A)((0,ue.A)().m(function l(){var n,u;return(0,ue.A)().w(function(l){while(1)switch(l.n){case 0:return e.value=!0,l.p=1,l.n=2,ke.A.get("/vulnerability-review",{params:{page:r.value,pageSize:i.value,query:c.value,status:o.value,severity:s.value}});case 2:n=l.v,n.data.success?(t.value=n.data.data.vulnerabilities,a.value=n.data.data.total):be.nk.error(n.data.message||"获取漏洞列表失败"),l.n=4;break;case 3:l.p=3,u=l.v,console.error("获取漏洞列表失败:",u),be.nk.error("获取漏洞列表失败，请稍后重试");case 4:return l.p=4,e.value=!1,l.f(4);case 5:return l.a(2)}},l,null,[[1,3,4,5]])}));return function(){return l.apply(this,arguments)}}(),p=function(e){b.value=e,u.value=!0},h=function(e){e&&(b.value=e,k.severity=e.severity,k.reward=x(e.severity),k.comment="",d.value=!0)},y=function(){var e=(0,de.A)((0,ue.A)().m(function e(){var t,n,a,i;return(0,ue.A)().w(function(e){while(1)switch(e.n){case 0:if(b.value){e.n=1;break}return e.a(2);case 1:return l.value=!0,e.p=2,e.n=3,ke.A.post("/vulnerability-workflow/enterprise-review/".concat(b.value.id),{action:"confirm",reward:k.reward,comment:k.comment});case 3:if(t=e.v,!t.data.success){e.n=5;break}return n=t.data.message,t.data.data&&t.data.data.blockchain&&(n+="\n区块链交易哈希: ".concat(t.data.data.blockchain.transactionHash)),be.nk.success(n),e.n=4,v();case 4:d.value=!1,u.value=!1,e.n=6;break;case 5:be.nk.error(t.data.message||"确认漏洞失败");case 6:e.n=8;break;case 7:e.p=7,i=e.v,console.error("确认漏洞失败:",i),be.nk.error((null===(a=i.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"确认漏洞失败，请稍后重试");case 8:return e.p=8,l.value=!1,e.f(8);case 9:return e.a(2)}},e,null,[[2,7,8,9]])}));return function(){return e.apply(this,arguments)}}(),g=function(e){e&&(b.value=e,m.reason="",m.comment="",f.value=!0)},_=function(){var e=(0,de.A)((0,ue.A)().m(function e(){var t,n,a,i;return(0,ue.A)().w(function(e){while(1)switch(e.n){case 0:if(b.value&&m.reason&&m.comment){e.n=1;break}return be.nk.warning("请填写拒绝原因和详细说明"),e.a(2);case 1:return l.value=!0,e.p=2,e.n=3,ke.A.post("/vulnerability-workflow/enterprise-review/".concat(b.value.id),{action:"reject",comment:"拒绝原因: ".concat(m.reason,"\n详细说明: ").concat(m.comment)});case 3:if(t=e.v,!t.data.success){e.n=5;break}return n=t.data.message,t.data.data&&t.data.data.blockchain&&(n+="\n区块链交易哈希: ".concat(t.data.data.blockchain.transactionHash)),be.nk.success(n),e.n=4,v();case 4:f.value=!1,u.value=!1,e.n=6;break;case 5:be.nk.error(t.data.message||"拒绝漏洞失败");case 6:e.n=8;break;case 7:e.p=7,i=e.v,console.error("拒绝漏洞失败:",i),be.nk.error((null===(a=i.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"拒绝漏洞失败，请稍后重试");case 8:return e.p=8,l.value=!1,e.f(8);case 9:return e.a(2)}},e,null,[[2,7,8,9]])}));return function(){return e.apply(this,arguments)}}(),L=function(){r.value=1,v()},F=function(){c.value="",o.value="",s.value="",L()},V=function(e){r.value=e,v()},C=function(e){i.value=e,r.value=1,v()},w=function(){u.value=!1,b.value=null},x=function(e){var l={critical:2e4,high:8e3,medium:2e3,low:500,info:200};return l[e]||0},A=function(e){var l={critical:"danger",high:"warning",medium:"warning",low:"info",info:"info"};return l[e]||"info"},S=function(e){var l={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"信息"};return l[e]||"未知"},T=function(e){if(!e||!e.urls)return[];try{return"string"===typeof e.urls?JSON.parse(e.urls):Array.isArray(e.urls)?e.urls:[]}catch(l){return console.error("解析URL列表失败:",l),[]}},z=function(e){if(!e||!e.attachmentLinks)return[];try{return"string"===typeof e.attachmentLinks?JSON.parse(e.attachmentLinks):Array.isArray(e.attachmentLinks)?e.attachmentLinks:[]}catch(l){return console.error("解析附件链接列表失败:",l),[]}},W=function(){var e=(0,de.A)((0,ue.A)().m(function e(l){var t,n,a;return(0,ue.A)().w(function(e){while(1)switch(e.n){case 0:if(e.p=0,!navigator.clipboard||!window.isSecureContext){e.n=2;break}return e.n=1,navigator.clipboard.writeText(l);case 1:be.nk.success("已复制到剪贴板"),e.n=3;break;case 2:t=document.createElement("textarea"),t.value=l,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select(),n=document.execCommand("copy"),document.body.removeChild(t),n?be.nk.success("已复制到剪贴板"):be.nk.error("复制失败，请手动复制");case 3:e.n=5;break;case 4:e.p=4,a=e.v,console.error("复制失败:",a),be.nk.error("复制失败，请手动复制");case 5:return e.a(2)}},e,null,[[0,4]])}));return function(l){return e.apply(this,arguments)}}(),j=function(e){return!!e&&!!e.submissionTransactionHash},R=function(e){return!!e&&!!e.approvalTransactionHash},K=function(e){if(console.log("formatTimestamp 接收到的时间戳:",e,(0,se.A)(e)),!e)return"未知";try{var l=new Date(1e3*e);return console.log("转换后的日期:",l),l.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(t){return console.error("时间格式化失败:",t),"格式错误"}};return(0,n.sV)(function(){v()}),{loading:e,submitting:l,vulnerabilities:t,total:a,pageSize:i,currentPage:r,searchQuery:c,filterStatus:o,filterSeverity:s,dialogVisible:u,confirmDialogVisible:d,rejectDialogVisible:f,selectedVulnerability:b,confirmForm:k,rejectForm:m,fetchVulnerabilities:v,viewDetails:p,confirmVulnerability:h,submitConfirm:y,rejectVulnerability:g,submitReject:_,handleSearch:L,resetFilters:F,handleCurrentChange:V,handleSizeChange:C,handleDialogClose:w,getSeverityType:A,getSeverityLabel:S,getUrlList:T,getAttachmentList:z,copyToClipboard:W,hasSubmissionBlockchainInfo:j,hasApprovalBlockchainInfo:R,formatTimestamp:K}}};var pe=t(1169);const he=(0,pe.A)(ve,[["render",oe],["__scopeId","data-v-536b69e8"]]),ye=he}}]);