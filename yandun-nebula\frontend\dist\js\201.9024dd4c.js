"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[201],{86201:(n,e,t)=>{t.r(e),t.d(e,{default:()=>x});var r=t(24059),a=t(88844),l=t(698),u=(t(23288),t(27495),t(5746),t(95976)),c=t(12040),o=t(10160),i=t(18057),s=t(30578),d=t(17383),p=t(36149),f={class:"project-management"},k={class:"page-header"},v={class:"search-filters"},b={class:"table-container"},g={class:"company-info"},m=["src"],_={class:"company-name"},h={class:"company-id"},w={class:"reward-range"},y={class:"project-time"},F={class:"participation"},C={class:"pagination"};const L={__name:"ProjectManagement",setup:function(n){var e=(0,c.KR)(!1),t=(0,c.KR)([]),L=(0,c.Kh)({search:"",status:""}),A=(0,c.Kh)({page:1,limit:10,total:0}),z=function(){var n=(0,l.A)((0,r.A)().m(function n(){var l,u,c;return(0,r.A)().w(function(n){while(1)switch(n.n){case 0:return e.value=!0,n.p=1,l=(0,a.A)({page:A.page,limit:A.limit},L),n.n=2,p.A.get("/admin/projects",{params:l});case 2:u=n.v,u.data.success&&(t.value=u.data.data.projects,A.total=u.data.data.total),n.n=4;break;case 3:n.p=3,c=n.v,console.error("加载项目列表失败:",c),i.nk.error("加载项目列表失败");case 4:return n.p=4,e.value=!1,n.f(4);case 5:return n.a(2)}},n,null,[[1,3,4,5]])}));return function(){return n.apply(this,arguments)}}(),x=function(n){window.open("/projects/".concat(n),"_blank")},W=function(){var n=(0,l.A)((0,r.A)().m(function n(e){var t;return(0,r.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,s.s.confirm("确定要发布该项目吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:console.log("发布项目ID:",e),i.nk.success("项目已发布"),z(),n.n=3;break;case 2:n.p=2,t=n.v,"cancel"!==t&&(console.error("审核项目失败:",t),i.nk.error("操作失败"));case 3:return n.a(2)}},n,null,[[0,2]])}));return function(e){return n.apply(this,arguments)}}(),B=function(){var n=(0,l.A)((0,r.A)().m(function n(e){var t;return(0,r.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,s.s.confirm("确定要暂停该项目吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:console.log("暂停项目ID:",e),i.nk.success("项目已暂停"),z(),n.n=3;break;case 2:n.p=2,t=n.v,"cancel"!==t&&(console.error("暂停项目失败:",t),i.nk.error("操作失败"));case 3:return n.a(2)}},n,null,[[0,2]])}));return function(e){return n.apply(this,arguments)}}(),T=function(){var n=(0,l.A)((0,r.A)().m(function n(e){var t;return(0,r.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,s.s.confirm("确定要完成该项目吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:console.log("完成项目ID:",e),i.nk.success("项目已完成"),z(),n.n=3;break;case 2:n.p=2,t=n.v,"cancel"!==t&&(console.error("完成项目失败:",t),i.nk.error("操作失败"));case 3:return n.a(2)}},n,null,[[0,2]])}));return function(e){return n.apply(this,arguments)}}(),j=function(){var n=(0,l.A)((0,r.A)().m(function n(e){var t,a;return(0,r.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,s.s.confirm("确定要删除该项目吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:return n.n=2,p.A["delete"]("/admin/projects/".concat(e));case 2:t=n.v,t.data.success&&(i.nk.success("删除成功"),z()),n.n=4;break;case 3:n.p=3,a=n.v,"cancel"!==a&&(console.error("删除项目失败:",a),i.nk.error("删除失败"));case 4:return n.a(2)}},n,null,[[0,3]])}));return function(e){return n.apply(this,arguments)}}(),D=function(n){var e={draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已完成",cancelled:"已取消"};return e[n]||n},I=function(n){var e={draft:"info",published:"success",in_progress:"primary",completed:"",cancelled:"danger"};return e[n]||""},V=function(n){A.limit=n,A.page=1,z()},X=function(n){A.page=n,z()},K=function(n){return n?new Date(n).toLocaleDateString("zh-CN"):"-"},N=function(n){return n?n.toLocaleString("zh-CN"):"0"};return(0,u.sV)(function(){z()}),function(n,r){var a=(0,u.g2)("el-button"),l=(0,u.g2)("el-icon"),i=(0,u.g2)("el-input"),s=(0,u.g2)("el-col"),p=(0,u.g2)("el-option"),Q=(0,u.g2)("el-select"),S=(0,u.g2)("el-row"),M=(0,u.g2)("el-table-column"),R=(0,u.g2)("el-tag"),E=(0,u.g2)("el-table"),U=(0,u.g2)("el-pagination"),P=(0,u.gN)("loading");return(0,u.uX)(),(0,u.CE)("div",f,[(0,u.Lk)("div",k,[r[4]||(r[4]=(0,u.Lk)("h1",null,"项目管理",-1)),(0,u.bF)(a,{onClick:r[0]||(r[0]=function(e){return n.$router.go(-1)})},{default:(0,u.k6)(function(){return r[3]||(r[3]=[(0,u.eW)("返回")])}),_:1,__:[3]})]),(0,u.Lk)("div",v,[(0,u.bF)(S,{gutter:20},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{span:8},{default:(0,u.k6)(function(){return[(0,u.bF)(i,{modelValue:L.search,"onUpdate:modelValue":r[1]||(r[1]=function(n){return L.search=n}),placeholder:"搜索项目标题或公司名称",clearable:"",onChange:z},{prefix:(0,u.k6)(function(){return[(0,u.bF)(l,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,c.R1)(d.Search))]}),_:1})]}),_:1},8,["modelValue"])]}),_:1}),(0,u.bF)(s,{span:4},{default:(0,u.k6)(function(){return[(0,u.bF)(Q,{modelValue:L.status,"onUpdate:modelValue":r[2]||(r[2]=function(n){return L.status=n}),placeholder:"项目状态",clearable:"",onChange:z},{default:(0,u.k6)(function(){return[(0,u.bF)(p,{label:"草稿",value:"draft"}),(0,u.bF)(p,{label:"已发布",value:"published"}),(0,u.bF)(p,{label:"进行中",value:"in_progress"}),(0,u.bF)(p,{label:"已完成",value:"completed"}),(0,u.bF)(p,{label:"已取消",value:"cancelled"})]}),_:1},8,["modelValue"])]}),_:1}),(0,u.bF)(s,{span:4},{default:(0,u.k6)(function(){return[(0,u.bF)(a,{type:"primary",onClick:z},{default:(0,u.k6)(function(){return r[5]||(r[5]=[(0,u.eW)("搜索")])}),_:1,__:[5]})]}),_:1})]}),_:1})]),(0,u.Lk)("div",b,[(0,u.bo)(((0,u.uX)(),(0,u.Wv)(E,{data:t.value,stripe:"",style:{width:"100%"}},{default:(0,u.k6)(function(){return[(0,u.bF)(M,{prop:"id",label:"ID",width:"80"}),(0,u.bF)(M,{prop:"title",label:"项目标题",width:"250"}),(0,u.bF)(M,{label:"企业信息",width:"200"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.Lk)("div",g,[e.companyLogo?((0,u.uX)(),(0,u.CE)("img",{key:0,src:e.companyLogo,class:"company-logo"},null,8,m)):(0,u.Q3)("",!0),(0,u.Lk)("div",null,[(0,u.Lk)("div",_,(0,o.v_)(e.companyName),1),(0,u.Lk)("div",h,"ID: "+(0,o.v_)(e.companyId),1)])])]}),_:1}),(0,u.bF)(M,{label:"状态",width:"100"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.bF)(R,{type:I(e.status),size:"small"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,o.v_)(D(e.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(M,{label:"奖励预算",width:"150"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.Lk)("div",w,[(0,u.Lk)("div",null,"¥"+(0,o.v_)(N(e.totalBudgetMin)),1),r[6]||(r[6]=(0,u.Lk)("div",{style:{color:"#999","font-size":"12px"}},"至",-1)),(0,u.Lk)("div",null,"¥"+(0,o.v_)(N(e.totalBudgetMax)),1)])]}),_:1}),(0,u.bF)(M,{label:"项目时间",width:"200"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.Lk)("div",y,[(0,u.Lk)("div",null,"开始: "+(0,o.v_)(K(e.startTime)),1),(0,u.Lk)("div",null,"结束: "+(0,o.v_)(K(e.endTime)),1)])]}),_:1}),(0,u.bF)(M,{label:"参与情况",width:"120"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.Lk)("div",F,[(0,u.Lk)("span",null,"👥 "+(0,o.v_)(e.participantsCount||0),1),(0,u.Lk)("span",null,"📝 "+(0,o.v_)(e.submissionsCount||0),1)])]}),_:1}),(0,u.bF)(M,{prop:"createdAt",label:"创建时间",width:"180"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.eW)((0,o.v_)(K(e.createdAt)),1)]}),_:1}),(0,u.bF)(M,{label:"操作",width:"200",fixed:"right"},{default:(0,u.k6)(function(n){var e=n.row;return[(0,u.bF)(a,{type:"primary",size:"small",onClick:function(n){return x(e.id)}},{default:(0,u.k6)(function(){return r[7]||(r[7]=[(0,u.eW)(" 查看 ")])}),_:2,__:[7]},1032,["onClick"]),"draft"===e.status?((0,u.uX)(),(0,u.Wv)(a,{key:0,type:"success",size:"small",onClick:function(n){return W(e.id)}},{default:(0,u.k6)(function(){return r[8]||(r[8]=[(0,u.eW)(" 发布 ")])}),_:2,__:[8]},1032,["onClick"])):(0,u.Q3)("",!0),"published"===e.status?((0,u.uX)(),(0,u.Wv)(a,{key:1,type:"warning",size:"small",onClick:function(n){return B(e.id)}},{default:(0,u.k6)(function(){return r[9]||(r[9]=[(0,u.eW)(" 暂停 ")])}),_:2,__:[9]},1032,["onClick"])):(0,u.Q3)("",!0),"in_progress"===e.status?((0,u.uX)(),(0,u.Wv)(a,{key:2,type:"primary",size:"small",onClick:function(n){return T(e.id)}},{default:(0,u.k6)(function(){return r[10]||(r[10]=[(0,u.eW)(" 完成 ")])}),_:2,__:[10]},1032,["onClick"])):(0,u.Q3)("",!0),(0,u.bF)(a,{type:"danger",size:"small",onClick:function(n){return j(e.id)}},{default:(0,u.k6)(function(){return r[11]||(r[11]=[(0,u.eW)(" 删除 ")])}),_:2,__:[11]},1032,["onClick"])]}),_:1})]}),_:1},8,["data"])),[[P,e.value]]),(0,u.Lk)("div",C,[(0,u.bF)(U,{"current-page":A.page,"page-size":A.limit,"page-sizes":[10,20,50,100],total:A.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:V,onCurrentChange:X},null,8,["current-page","page-size","total"])])])])}}};var A=t(1169);const z=(0,A.A)(L,[["__scopeId","data-v-517db748"]]),x=z}}]);