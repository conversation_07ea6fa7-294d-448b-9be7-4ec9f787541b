<template>
  <div class="activities-container">
    <TheHeader />
    
    <div class="main-content">
      <div class="page-header">
        <h1 class="page-title">公告与活动中心</h1>
        <div class="category-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="系统公告" name="system"></el-tab-pane>
            <el-tab-pane label="活动公告" name="event"></el-tab-pane>
            <el-tab-pane label="维护公告" name="maintenance"></el-tab-pane>
            <el-tab-pane label="重要通知" name="important"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <!-- 滚动banner区域 -->
      <!-- <div class="banner-section" v-if="featuredActivities.length > 0">
        <el-carousel height="300px">
          <el-carousel-item v-for="item in featuredActivities" :key="item.id">
            <div class="banner-content" :style="{ backgroundImage: `url(${item.bannerImage || require('@/assets/logo.png')})` }">
              <div class="banner-info">
                <h2 class="banner-title">{{ item.title }}</h2>
                <p class="banner-desc">{{ item.description }}</p>
                <div class="banner-time">
                  <i class="el-icon-time"></i>
                  {{ formatDate(item.startTime) }}
                </div>
                <el-button type="primary" @click="viewActivityDetail(item.id)">了解详情</el-button>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div> -->
      
      <!-- 活动搜索 -->
      <div class="filter-section">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索活动..."
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="applyFilters"
          >
            <template #append>
              <el-button @click="applyFilters">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 活动列表 -->
      <div class="activities-list" v-loading="loading">
        <div v-if="filteredActivities.length > 0" class="activities-grid">
          <div class="activity-card" v-for="activity in filteredActivities" :key="activity.id">
            <div class="activity-image">
              <img :src="activity.coverImage || require('@/assets/logo.png')" alt="活动封面">
            </div>
            <div class="activity-content">
              <div class="activity-header-tags">
                <div class="activity-type-tag" :class="getTypeClass(activity.type, activity.announcementType)">{{ getTypeText(activity.type, activity.announcementType) }}</div>
                <!-- 区块链信息标签 -->
                <div v-if="activity.transactionHash" class="blockchain-tag">
                  <el-icon><LinkIcon /></el-icon>
                  <span>已上链</span>
                </div>
              </div>
              <h3 class="activity-title">{{ activity.title }}</h3>
              <div class="activity-time">
                <i class="el-icon-time"></i>
                {{ formatDate(activity.startTime) }}
              </div>
              <div class="activity-desc">{{ activity.description }}</div>
              <div class="activity-location" v-if="activity.location">
                <i class="el-icon-location"></i>
                {{ activity.location }}
              </div>
              <div class="activity-footer">
                <el-button type="primary" @click="viewActivityDetail(activity.id)">查看详情</el-button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="empty-state" v-else-if="!loading">
          <el-empty description="暂无符合条件的活动，请更改筛选条件或稍后再试"></el-empty>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          v-model="currentPage"
          @current-change="handlePageChange"
        ></el-pagination>
      </div>
    </div>
        <!-- 页脚 -->
    <AppFooter />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Link as LinkIcon } from '@element-plus/icons-vue';
import AppFooter from '@/components/AppFooter.vue';
import TheHeader from '@/components/TheHeader.vue';

export default {
  name: 'Activities',
  components: {
    TheHeader,
    AppFooter,
    LinkIcon
  },
  setup() {
    const router = useRouter();
    const activities = ref([]);
    const loading = ref(false);
    const activeTab = ref('all');
    const searchKeyword = ref('');
    const currentPage = ref(1);
    const pageSize = ref(9);
    const total = ref(0);
    
    // 获取置顶/推荐活动作为轮播图展示
    const featuredActivities = computed(() => {
      return activities.value.filter(activity => activity.featured || activity.isSticky).slice(0, 5);
    });
    
    // 根据筛选条件过滤活动
    const filteredActivities = computed(() => {
      return activities.value; // 直接返回activities，因为过滤逻辑已经在fetchActivities中处理
    });
    
    const totalPages = computed(() => {
      return Math.ceil(total.value / pageSize.value);
    });

    const fetchActivities = async () => {
      loading.value = true;
      try {
        // 获取公告数据
        const announcementResponse = await fetch('/api/announcements', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        let announcements = [];
        if (announcementResponse.ok) {
          const announcementData = await announcementResponse.json();
          if (announcementData.success) {
            announcements = announcementData.data.announcements || [];
          }
        }

        // 获取活动数据
        const activityResponse = await fetch('/api/activities', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        let activityData = [];
        if (activityResponse.ok) {
          const activityResult = await activityResponse.json();
          if (activityResult.code === 0) {
            activityData = activityResult.data || [];
          }
        }

        // 合并公告和活动数据
        const combinedData = [
          // 转换公告数据格式
          ...announcements.map(announcement => ({
            id: `announcement_${announcement.id}`,
            title: announcement.title,
            description: announcement.content,
            status: 'ongoing', // 已发布的公告默认为进行中
            type: 'announcement',
            featured: announcement.isSticky,
            isSticky: announcement.isSticky,
            bannerImage: require('@/assets/logo.png'),
            coverImage: require('@/assets/logo.png'),
            startTime: new Date(announcement.publishedAt),
            endTime: new Date(announcement.publishedAt),
            views: announcement.viewCount || 0,
            priority: announcement.priority || 0,
            announcementType: announcement.type
          })),
          // 转换活动数据格式
          ...activityData.map(activity => ({
            id: `activity_${activity.id}`,
            title: activity.name,
            description: activity.description,
            status: activity.status,
            type: 'event',
            featured: false,
            isSticky: false,
            bannerImage: require('@/assets/logo.png'),
            coverImage: require('@/assets/logo.png'),
            startTime: new Date(activity.startDate),
            endTime: new Date(activity.endDate),
            views: 0,
            location: '线上',
            participantCount: 0
          }))
        ];

        // 应用筛选条件
        let filteredData = combinedData;

        // 按类型筛选
        if (activeTab.value !== 'all') {
          if (activeTab.value === 'event') {
            // 活动类型
            filteredData = filteredData.filter(item => item.type === 'event');
          } else {
            // 公告类型（system, maintenance, important）
            filteredData = filteredData.filter(item =>
              item.type === 'announcement' && item.announcementType === activeTab.value
            );
          }
        }



        // 按关键词搜索
        if (searchKeyword.value) {
          const keyword = searchKeyword.value.toLowerCase();
          filteredData = filteredData.filter(item =>
            item.title.toLowerCase().includes(keyword) ||
            item.description.toLowerCase().includes(keyword)
          );
        }

        // 排序：置顶优先，然后按优先级和时间排序
        filteredData.sort((a, b) => {
          if (a.isSticky !== b.isSticky) {
            return b.isSticky - a.isSticky;
          }
          if (a.priority !== b.priority) {
            return b.priority - a.priority;
          }
          return new Date(b.startTime) - new Date(a.startTime);
        });

        // 分页
        const startIndex = (currentPage.value - 1) * pageSize.value;
        const endIndex = startIndex + pageSize.value;

        activities.value = filteredData.slice(startIndex, endIndex);
        total.value = filteredData.length;

      } catch (error) {
        console.error('获取活动列表失败:', error);
        ElMessage.error('获取活动列表失败，请稍后再试');
        activities.value = [];
        total.value = 0;
      } finally {
        loading.value = false;
      }
    };

    const formatDate = (date) => {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    };


    
    const getTypeClass = (type, announcementType) => {
      if (type === 'event') {
        return 'type-event';
      } else if (type === 'announcement') {
        // 根据公告类型返回不同的样式类
        switch (announcementType) {
          case 'system': return 'type-system';
          case 'event': return 'type-event-announcement';
          case 'maintenance': return 'type-maintenance';
          case 'important': return 'type-important';
          default: return 'type-announcement';
        }
      }
      return '';
    };
    
    const getTypeText = (type, announcementType) => {
      if (type === 'event') {
        return '活动';
      } else if (type === 'announcement') {
        // 根据公告类型返回具体的分类名称
        switch (announcementType) {
          case 'system': return '系统公告';
          case 'activity': return '活动公告';
          case 'maintenance': return '维护公告';
          case 'important': return '重要通知';
          default: return '公告';
        }
      }
      return '';
    };

    const viewActivityDetail = (id) => {
      // 这里应该导航到活动详情页面
      router.push(`/activities/${id}`);
    };


    
    const handleTabChange = () => {
      currentPage.value = 1;
      applyFilters();
    };
    
    const applyFilters = () => {
      currentPage.value = 1;
      fetchActivities();
    };
    
    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchActivities();
    };

    // 监听筛选条件变化
    watch([activeTab, searchKeyword], () => {
      applyFilters();
    });

    onMounted(() => {
      fetchActivities();
    });

    return {
      activities,
      loading,
      activeTab,
      searchKeyword,
      currentPage,
      pageSize,
      total,
      totalPages,
      featuredActivities,
      filteredActivities,
      formatDate,
      getTypeClass,
      getTypeText,
      viewActivityDetail,
      handleTabChange,
      applyFilters,
      handlePageChange
    };
  }
}
</script>

<style scoped>
.activities-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px 40px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
}

.category-tabs {
  margin-bottom: 20px;
}

.banner-section {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.banner-content {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
}

.banner-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
}

.banner-info {
  position: relative;
  z-index: 1;
  padding: 40px;
  max-width: 60%;
  color: white;
}



.banner-title {
  font-size: 28px;
  margin: 0 0 15px;
}

.banner-desc {
  font-size: 16px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.banner-time {
  margin-bottom: 20px;
  opacity: 0.8;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.filter-label {
  margin-right: 10px;
  color: #606266;
}

.search-box {
  width: 300px;
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.activity-card {
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.activity-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.activity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-image img {
  transform: scale(1.05);
}



.activity-content {
  padding: 20px;
  position: relative;
}

.activity-header-tags {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.activity-type-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.blockchain-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
  animation: pulse 2s infinite;
}

.blockchain-tag .el-icon {
  font-size: 12px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.5);
  }
  100% {
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
  }
}

.activity-title {
  font-size: 18px;
  margin: 0 0 10px;
  color: #303133;
  height: 52px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-time {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.activity-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.activity-location {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}



.activity-footer {
  display: flex;
  justify-content: space-between;
}



.type-event {
  background-color: #ecf5ff;
  color: #409eff;
}

.type-announcement {
  background-color: #fef0f0;
  color: #f56c6c;
}

.type-system {
  background-color: #ecf5ff;
  color: #409eff;
}

.type-event-announcement {
  background-color: #f0f9ff;
  color: #67c23a;
}

.type-maintenance {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.type-important {
  background-color: #fef0f0;
  color: #f56c6c;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.pagination {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

@media screen and (max-width: 768px) {
  .main-content {
    padding: 70px 15px 30px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .banner-info {
    max-width: 90%;
    padding: 20px;
  }
  
  .banner-title {
    font-size: 22px;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .search-box {
    width: 100%;
  }
  
  .activities-grid {
    grid-template-columns: 1fr;
  }
}
</style> 