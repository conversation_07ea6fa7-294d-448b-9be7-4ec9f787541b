"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[282],{21282:(e,a,n)=>{n.r(a),n.d(a,{default:()=>oa});n(52675),n(89463),n(78459);var t=n(95976),s=n(10160),i=n(29746),l={class:"query-center-container"},c={class:"cyber-background"},r={class:"floating-particles"},o={class:"cyber-lines"},u={class:"query-content"},d={class:"query-main"},v={class:"function-selector"},f={class:"selector-tabs"},p=["onClick"],b={class:"tab-content"},k={class:"tab-title"},y={class:"tab-description"},h={class:"query-interface"},L={key:0,class:"query-panel"},m={class:"input-container"},g={class:"input-group"},C={class:"input-wrapper"},w={class:"input-group"},_={class:"input-wrapper"},H=["disabled"],I={class:"button-content"},A={key:1,class:"query-panel"},M={class:"input-container"},V={class:"input-group"},S={class:"input-wrapper"},E=["disabled"],Q={class:"button-content"},X={key:0,class:"results-container"},T={class:"results-header"},x={class:"results-title"},q={key:0,class:"loading-state"},N={key:1,class:"results-grid"},R={class:"result-header"},F={class:"result-content"},D={class:"result-title"},K={class:"result-description"},z={key:0,class:"certificate-info"},Z={class:"info-row"},j={class:"info-value"},B={key:0,class:"info-row"},P={class:"info-value blockchain-hash"},U={key:1,class:"info-row"},J={class:"info-value"},O={key:2,class:"info-row"},W={class:"info-value"},Y={key:1,class:"blockchain-info"},G={key:0,class:"db-info-section"},$={class:"info-section-title"},ee={key:0},ae={key:0,class:"info-row"},ne={class:"info-value"},te={key:1,class:"info-row"},se={class:"info-value"},ie={key:2,class:"info-row"},le={key:3,class:"info-row"},ce={class:"info-value"},re={key:1},oe={key:0,class:"info-row"},ue={class:"info-value"},de={key:1,class:"info-row"},ve={class:"info-value"},fe={key:2,class:"info-row"},pe={class:"info-value content-preview"},be={key:2},ke={key:0,class:"info-row"},ye={class:"info-value"},he={key:1,class:"info-row"},Le={class:"info-value"},me={key:2,class:"info-row"},ge={class:"info-value reward-amount"},Ce={key:3,class:"info-row"},we={class:"reward-details"},_e={class:"reward-level"},He={class:"reward-value"},Ie={key:4,class:"info-row"},Ae={key:5,class:"info-row"},Me={class:"info-value content-preview"},Ve={key:1,class:"blockchain-info-section"},Se={class:"info-row"},Ee={class:"info-value blockchain-hash"},Qe={class:"info-row"},Xe={class:"info-value blockchain-hash"},Te={class:"info-row"},xe={class:"info-value"},qe={class:"info-row"},Ne={class:"info-value blockchain-hash"},Re={class:"info-row"},Fe={class:"info-value blockchain-hash"},De={class:"info-row"},Ke={class:"info-value blockchain-hash"},ze={class:"info-row"},Ze={class:"info-value"},je={class:"result-meta"},Be={class:"meta-item"},Pe={class:"meta-item"},Ue={key:2,class:"empty-state"},Je={class:"empty-icon"};function Oe(e,a,n,Oe,We,Ye){var Ge=(0,t.g2)("TheHeader"),$e=(0,t.g2)("SvgIcon");return(0,t.uX)(),(0,t.CE)("div",l,[(0,t.bF)(Ge),(0,t.Lk)("div",c,[a[11]||(a[11]=(0,t.Lk)("div",{class:"grid-overlay"},null,-1)),(0,t.Lk)("div",r,[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)(20,function(e){return(0,t.Lk)("div",{key:e,class:"particle",style:(0,s.Tr)(Oe.getParticleStyle())},null,4)}),64))]),(0,t.Lk)("div",o,[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)(8,function(e){return(0,t.Lk)("div",{key:e,class:"cyber-line",style:(0,s.Tr)(Oe.getLineStyle(e))},null,4)}),64))])]),(0,t.Lk)("div",u,[a[53]||(a[53]=(0,t.Fv)('<div class="query-header" data-v-c5efb208><div class="title-container" data-v-c5efb208><div class="title-glow" data-v-c5efb208></div><h1 class="query-title" data-v-c5efb208><span class="title-text" data-v-c5efb208>验证中心</span><span class="title-subtitle" data-v-c5efb208>VERIFICATION CENTER</span></h1></div></div>',1)),(0,t.Lk)("div",d,[(0,t.Lk)("div",v,[(0,t.Lk)("div",f,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(Oe.functions,function(e){return(0,t.uX)(),(0,t.CE)("div",{key:e.value,class:(0,s.C4)(["selector-tab",{active:Oe.currentFunction===e.value}]),onClick:function(a){return Oe.switchFunction(e.value)}},[a[12]||(a[12]=(0,t.Lk)("div",{class:"tab-glow"},null,-1)),(0,t.Lk)("div",b,[(0,t.Lk)("h3",k,(0,s.v_)(e.title),1),(0,t.Lk)("p",y,(0,s.v_)(e.description),1)]),a[13]||(a[13]=(0,t.Lk)("div",{class:"tab-indicator"},null,-1))],10,p)}),128))])]),(0,t.Lk)("div",h,["certificate"===Oe.currentFunction?((0,t.uX)(),(0,t.CE)("div",L,[a[19]||(a[19]=(0,t.Lk)("div",{class:"panel-header"},[(0,t.Lk)("div",{class:"panel-title"},[(0,t.Lk)("h2",null,"证书验证"),(0,t.Lk)("p",null,"验证安全漏洞发现证书的真实性和有效性")])],-1)),(0,t.Lk)("div",m,[(0,t.Lk)("div",g,[a[15]||(a[15]=(0,t.Lk)("label",{class:"input-label"},"证书编号",-1)),(0,t.Lk)("div",C,[(0,t.bo)((0,t.Lk)("input",{"onUpdate:modelValue":a[0]||(a[0]=function(e){return Oe.certificateQuery.number=e}),type:"text",class:"cyber-input",placeholder:"请输入证书编号 (如: YDXY-1234567890-123)",onKeyup:a[1]||(a[1]=(0,i.jR)(function(){return Oe.queryCertificate&&Oe.queryCertificate.apply(Oe,arguments)},["enter"])),onInput:a[2]||(a[2]=function(e){return Oe.trimLeadingSpaces("certificateQuery","number")})},null,544),[[i.Jo,Oe.certificateQuery.number]]),a[14]||(a[14]=(0,t.Lk)("div",{class:"input-border"},null,-1))])]),(0,t.Lk)("div",w,[a[17]||(a[17]=(0,t.Lk)("label",{class:"input-label"},"区块链哈希",-1)),(0,t.Lk)("div",_,[(0,t.bo)((0,t.Lk)("input",{"onUpdate:modelValue":a[3]||(a[3]=function(e){return Oe.certificateQuery.blockchainHash=e}),type:"text",class:"cyber-input",placeholder:"请输入证书区块链哈希（必填）",onKeyup:a[4]||(a[4]=(0,i.jR)(function(){return Oe.queryCertificate&&Oe.queryCertificate.apply(Oe,arguments)},["enter"])),onInput:a[5]||(a[5]=function(e){return Oe.trimLeadingSpaces("certificateQuery","blockchainHash")})},null,544),[[i.Jo,Oe.certificateQuery.blockchainHash]]),a[16]||(a[16]=(0,t.Lk)("div",{class:"input-border"},null,-1))])]),(0,t.Lk)("button",{class:"cyber-button",onClick:a[6]||(a[6]=function(){return Oe.queryCertificate&&Oe.queryCertificate.apply(Oe,arguments)}),disabled:Oe.loading||!Oe.certificateQuery.number||!Oe.certificateQuery.blockchainHash},[(0,t.Lk)("span",I,[(0,t.Lk)("span",null,(0,s.v_)(Oe.loading?"验证中...":"开始验证"),1)]),a[18]||(a[18]=(0,t.Lk)("div",{class:"button-glow"},null,-1))],8,H)])])):(0,t.Q3)("",!0),"blockchain"===Oe.currentFunction?((0,t.uX)(),(0,t.CE)("div",A,[a[23]||(a[23]=(0,t.Lk)("div",{class:"panel-header"},[(0,t.Lk)("div",{class:"panel-title"},[(0,t.Lk)("h2",null,"区块链验证中心"),(0,t.Lk)("p",null,"查询和验证区块链上的交易记录和智能合约状态")])],-1)),(0,t.Lk)("div",M,[(0,t.Lk)("div",V,[a[21]||(a[21]=(0,t.Lk)("label",{class:"input-label"},"交易哈希",-1)),(0,t.Lk)("div",S,[(0,t.bo)((0,t.Lk)("input",{"onUpdate:modelValue":a[7]||(a[7]=function(e){return Oe.blockchainQuery.txHash=e}),type:"text",class:"cyber-input",placeholder:"请输入交易哈希（注意：是交易哈希，不是区块哈希）",onKeyup:a[8]||(a[8]=(0,i.jR)(function(){return Oe.queryBlockchain&&Oe.queryBlockchain.apply(Oe,arguments)},["enter"])),onInput:a[9]||(a[9]=function(e){return Oe.trimLeadingSpaces("blockchainQuery","txHash")})},null,544),[[i.Jo,Oe.blockchainQuery.txHash]]),a[20]||(a[20]=(0,t.Lk)("div",{class:"input-border"},null,-1))])]),(0,t.Lk)("button",{class:"cyber-button",onClick:a[10]||(a[10]=function(){return Oe.queryBlockchain&&Oe.queryBlockchain.apply(Oe,arguments)}),disabled:Oe.loading||!Oe.blockchainQuery.txHash},[(0,t.Lk)("span",Q,[(0,t.bF)($e,{name:"search",size:"1.2rem"}),(0,t.Lk)("span",null,(0,s.v_)(Oe.loading?"查询中...":"查询区块链"),1)]),a[22]||(a[22]=(0,t.Lk)("div",{class:"button-glow"},null,-1))],8,E)])])):(0,t.Q3)("",!0)]),Oe.hasSearched?((0,t.uX)(),(0,t.CE)("div",X,[(0,t.Lk)("div",T,[(0,t.Lk)("h3",x,[(0,t.bF)($e,{name:"document",size:"1.5rem"}),a[24]||(a[24]=(0,t.eW)(" 查询结果 "))]),(0,t.Lk)("div",{class:(0,s.C4)(["results-status",{success:Oe.queryResults.length>0,empty:0===Oe.queryResults.length}])},(0,s.v_)(Oe.queryResults.length>0?"找到 ".concat(Oe.queryResults.length," 条记录"):"未找到相关记录"),3)]),Oe.loading?((0,t.uX)(),(0,t.CE)("div",q,a[25]||(a[25]=[(0,t.Fv)('<div class="loading-animation" data-v-c5efb208><div class="loading-ring" data-v-c5efb208></div><div class="loading-ring" data-v-c5efb208></div><div class="loading-ring" data-v-c5efb208></div></div><div class="loading-text" data-v-c5efb208>正在查询区块链数据...</div>',2)]))):Oe.queryResults.length>0?((0,t.uX)(),(0,t.CE)("div",N,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(Oe.queryResults,function(e){return(0,t.uX)(),(0,t.CE)("div",{key:e.id,class:"result-card"},[a[51]||(a[51]=(0,t.Lk)("div",{class:"result-glow"},null,-1)),(0,t.Lk)("div",R,[(0,t.Lk)("div",{class:(0,s.C4)(["result-type-badge",e.type])},[(0,t.bF)($e,{name:Oe.getResultIconName(e.type),size:"0.9rem"},null,8,["name"]),(0,t.Lk)("span",null,(0,s.v_)(Oe.getResultTypeText(e.type)),1)],2),(0,t.Lk)("div",{class:(0,s.C4)(["result-status",Oe.getStatusClass(e.status)])},(0,s.v_)(e.status),3)]),(0,t.Lk)("div",F,[(0,t.Lk)("h4",D,(0,s.v_)(e.title),1),(0,t.Lk)("p",K,(0,s.v_)(e.description),1),"certificate"===e.type?((0,t.uX)(),(0,t.CE)("div",z,[(0,t.Lk)("div",Z,[a[26]||(a[26]=(0,t.Lk)("span",{class:"info-label"},"证书编号:",-1)),(0,t.Lk)("span",j,(0,s.v_)(e.certificateNumber),1)]),e.blockchainHash?((0,t.uX)(),(0,t.CE)("div",B,[a[27]||(a[27]=(0,t.Lk)("span",{class:"info-label"},"区块链哈希:",-1)),(0,t.Lk)("span",P,(0,s.v_)(e.blockchainHash),1)])):(0,t.Q3)("",!0),e.recipient?((0,t.uX)(),(0,t.CE)("div",U,[a[28]||(a[28]=(0,t.Lk)("span",{class:"info-label"},"提交者:",-1)),(0,t.Lk)("span",J,(0,s.v_)(e.recipient),1)])):(0,t.Q3)("",!0),e.companyName?((0,t.uX)(),(0,t.CE)("div",O,[a[29]||(a[29]=(0,t.Lk)("span",{class:"info-label"},"相关企业:",-1)),(0,t.Lk)("span",W,(0,s.v_)(e.companyName),1)])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0),"blockchain"===e.type?((0,t.uX)(),(0,t.CE)("div",Y,[e.dbInfo?((0,t.uX)(),(0,t.CE)("div",G,[(0,t.Lk)("h5",$,(0,s.v_)(Oe.getRecordTypeTitle(e.dbInfo.type)),1),"vulnerability"===e.dbInfo.type?((0,t.uX)(),(0,t.CE)("div",ee,[e.dbInfo.submitterName?((0,t.uX)(),(0,t.CE)("div",ae,[a[30]||(a[30]=(0,t.Lk)("span",{class:"info-label"},"提交者:",-1)),(0,t.Lk)("span",ne,(0,s.v_)(e.dbInfo.submitterName),1)])):(0,t.Q3)("",!0),e.dbInfo.companyName?((0,t.uX)(),(0,t.CE)("div",te,[a[31]||(a[31]=(0,t.Lk)("span",{class:"info-label"},"相关企业:",-1)),(0,t.Lk)("span",se,(0,s.v_)(e.dbInfo.companyName),1)])):(0,t.Q3)("",!0),e.dbInfo.severityLevel?((0,t.uX)(),(0,t.CE)("div",ie,[a[32]||(a[32]=(0,t.Lk)("span",{class:"info-label"},"严重级别:",-1)),(0,t.Lk)("span",{class:(0,s.C4)(["info-value",Oe.getSeverityClass(e.dbInfo.severityLevel)])},(0,s.v_)(Oe.getSeverityLevelText(e.dbInfo.severityLevel)),3)])):(0,t.Q3)("",!0),e.dbInfo.vulnerabilityType?((0,t.uX)(),(0,t.CE)("div",le,[a[33]||(a[33]=(0,t.Lk)("span",{class:"info-label"},"漏洞类型:",-1)),(0,t.Lk)("span",ce,(0,s.v_)(Oe.getVulnerabilityTypeText(e.dbInfo.vulnerabilityType)),1)])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0),"announcement"===e.dbInfo.type?((0,t.uX)(),(0,t.CE)("div",re,[e.dbInfo.title?((0,t.uX)(),(0,t.CE)("div",oe,[a[34]||(a[34]=(0,t.Lk)("span",{class:"info-label"},"公告标题:",-1)),(0,t.Lk)("span",ue,(0,s.v_)(e.dbInfo.title),1)])):(0,t.Q3)("",!0),e.dbInfo.author?((0,t.uX)(),(0,t.CE)("div",de,[a[35]||(a[35]=(0,t.Lk)("span",{class:"info-label"},"发布者:",-1)),(0,t.Lk)("span",ve,(0,s.v_)(e.dbInfo.author),1)])):(0,t.Q3)("",!0),e.dbInfo.content?((0,t.uX)(),(0,t.CE)("div",fe,[a[36]||(a[36]=(0,t.Lk)("span",{class:"info-label"},"公告内容:",-1)),(0,t.Lk)("span",pe,(0,s.v_)(e.dbInfo.content.substring(0,100))+(0,s.v_)(e.dbInfo.content.length>100?"...":""),1)])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0),"security_task"===e.dbInfo.type?((0,t.uX)(),(0,t.CE)("div",be,[e.dbInfo.title?((0,t.uX)(),(0,t.CE)("div",ke,[a[37]||(a[37]=(0,t.Lk)("span",{class:"info-label"},"任务标题:",-1)),(0,t.Lk)("span",ye,(0,s.v_)(e.dbInfo.title),1)])):(0,t.Q3)("",!0),e.dbInfo.publisherName?((0,t.uX)(),(0,t.CE)("div",he,[a[38]||(a[38]=(0,t.Lk)("span",{class:"info-label"},"发布者:",-1)),(0,t.Lk)("span",Le,(0,s.v_)(e.dbInfo.publisherName),1)])):(0,t.Q3)("",!0),e.dbInfo.rewardRange?((0,t.uX)(),(0,t.CE)("div",me,[a[39]||(a[39]=(0,t.Lk)("span",{class:"info-label"},"预算范围:",-1)),(0,t.Lk)("span",ge,"¥"+(0,s.v_)(e.dbInfo.rewardRange),1)])):(0,t.Q3)("",!0),e.dbInfo.rewards&&e.dbInfo.rewards.length>0?((0,t.uX)(),(0,t.CE)("div",Ce,[a[40]||(a[40]=(0,t.Lk)("span",{class:"info-label"},"奖励标准:",-1)),(0,t.Lk)("div",we,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.dbInfo.rewards,function(e){return(0,t.uX)(),(0,t.CE)("div",{key:e.level,class:"reward-item"},[(0,t.Lk)("span",_e,(0,s.v_)(e.level)+":",1),(0,t.Lk)("span",He,"¥"+(0,s.v_)(parseFloat(e.amount).toLocaleString()),1)])}),128))])])):(0,t.Q3)("",!0),e.dbInfo.status?((0,t.uX)(),(0,t.CE)("div",Ie,[a[41]||(a[41]=(0,t.Lk)("span",{class:"info-label"},"任务状态:",-1)),(0,t.Lk)("span",{class:(0,s.C4)(["info-value",Oe.getTaskStatusClass(e.dbInfo.status)])},(0,s.v_)(Oe.getTaskStatusText(e.dbInfo.status)),3)])):(0,t.Q3)("",!0),e.dbInfo.description?((0,t.uX)(),(0,t.CE)("div",Ae,[a[42]||(a[42]=(0,t.Lk)("span",{class:"info-label"},"任务描述:",-1)),(0,t.Lk)("span",Me,(0,s.v_)(e.dbInfo.description.substring(0,100))+(0,s.v_)(e.dbInfo.description.length>100?"...":""),1)])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0),e.blockchainInfo?((0,t.uX)(),(0,t.CE)("div",Ve,[a[50]||(a[50]=(0,t.Lk)("h5",{class:"info-section-title"},"区块链详细信息",-1)),(0,t.Lk)("div",Se,[a[43]||(a[43]=(0,t.Lk)("span",{class:"info-label"},"交易哈希:",-1)),(0,t.Lk)("span",Ee,(0,s.v_)(e.blockchainInfo.transactionHash),1)]),(0,t.Lk)("div",Qe,[a[44]||(a[44]=(0,t.Lk)("span",{class:"info-label"},"区块哈希:",-1)),(0,t.Lk)("span",Xe,(0,s.v_)(e.blockchainInfo.blockHash),1)]),(0,t.Lk)("div",Te,[a[45]||(a[45]=(0,t.Lk)("span",{class:"info-label"},"Gas消耗:",-1)),(0,t.Lk)("span",xe,(0,s.v_)(e.blockchainInfo.gasUsed),1)]),(0,t.Lk)("div",qe,[a[46]||(a[46]=(0,t.Lk)("span",{class:"info-label"},"合约地址:",-1)),(0,t.Lk)("span",Ne,(0,s.v_)(e.blockchainInfo.contractAddress),1)]),(0,t.Lk)("div",Re,[a[47]||(a[47]=(0,t.Lk)("span",{class:"info-label"},"发送方:",-1)),(0,t.Lk)("span",Fe,(0,s.v_)(e.blockchainInfo.from),1)]),(0,t.Lk)("div",De,[a[48]||(a[48]=(0,t.Lk)("span",{class:"info-label"},"接收方:",-1)),(0,t.Lk)("span",Ke,(0,s.v_)(e.blockchainInfo.to),1)]),(0,t.Lk)("div",ze,[a[49]||(a[49]=(0,t.Lk)("span",{class:"info-label"},"交易状态:",-1)),(0,t.Lk)("span",Ze,(0,s.v_)("0x0"===e.blockchainInfo.status?"成功":"失败"),1)])])):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0),(0,t.Lk)("div",je,[(0,t.Lk)("div",Be,[(0,t.bF)($e,{name:"time",size:"0.9rem"}),(0,t.Lk)("span",null,"提交时间："+(0,s.v_)(Oe.formatDate(e.createdAt)),1)]),(0,t.Lk)("div",Pe,[(0,t.bF)($e,{name:"time",size:"0.9rem"}),(0,t.Lk)("span",null,"当前时间："+(0,s.v_)(Oe.getCurrentTime()),1)])])])])}),128))])):((0,t.uX)(),(0,t.CE)("div",Ue,[(0,t.Lk)("div",Je,[(0,t.bF)($e,{name:"document-delete",size:"4rem"})]),a[52]||(a[52]=(0,t.Lk)("div",{class:"empty-text"},[(0,t.Lk)("h3",null,"未找到相关记录"),(0,t.Lk)("p",null,"请检查输入的信息是否正确，或尝试其他查询条件")],-1))]))])):(0,t.Q3)("",!0)])]),a[54]||(a[54]=(0,t.Fv)('<div class="login-footer" data-v-c5efb208><p class="footer-text" data-v-c5efb208> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-c5efb208>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-c5efb208>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-c5efb208>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-c5efb208><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-c5efb208> 渝公网安备50011702501094号 </a></p></div>',1))])}var We=n(24059),Ye=n(698),Ge=(n(76918),n(28706),n(74423),n(62062),n(59089),n(60739),n(23288),n(18111),n(61701),n(33110),n(26099),n(27495),n(38781),n(21699),n(25440),n(11392),n(76031),n(12040)),$e=n(18057),ea=n(80401),aa=["innerHTML"];function na(e,a,n,i,l,c){return(0,t.uX)(),(0,t.CE)("svg",{class:(0,s.C4)(["svg-icon",n.className]),style:(0,s.Tr)({width:n.size,height:n.size}),innerHTML:c.iconSvg},null,14,aa)}n(62010);const ta={name:"SvgIcon",props:{name:{type:String,required:!0},className:{type:String,default:""},size:{type:String,default:"1em"}},computed:{iconSvg:function(){var e={certificate:'\n          <path fill="currentColor" d="M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z"/>\n          <circle fill="currentColor" cx="17" cy="7" r="2"/>\n          <path fill="currentColor" d="M19,10.5L17.5,9L16,10.5L17.5,12L19,10.5Z"/>\n        ',blockchain:'\n          <path fill="currentColor" d="M6,2A2,2 0 0,0 4,4V8A2,2 0 0,0 6,10H10A2,2 0 0,0 12,8V4A2,2 0 0,0 10,2H6M6,4H10V8H6V4M14,6A2,2 0 0,0 12,8V12A2,2 0 0,0 14,14H18A2,2 0 0,0 20,12V8A2,2 0 0,0 18,6H14M14,8H18V12H14V8M6,14A2,2 0 0,0 4,16V20A2,2 0 0,0 6,22H10A2,2 0 0,0 12,20V16A2,2 0 0,0 10,14H6M6,16H10V20H6V16Z"/>\n          <path fill="currentColor" d="M10,10L14,6M10,14L14,14M18,14L14,18"/>\n        ',search:'\n          <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>\n        ',document:'\n          <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>\n        ',time:'\n          <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/>\n        ',view:'\n          <path fill="currentColor" d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>\n        ',download:'\n          <path fill="currentColor" d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>\n        ',"document-delete":'\n          <path fill="currentColor" d="M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4M8,12V14H16V12H8Z"/>\n        ',shield:'\n          <path fill="currentColor" d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>\n        ',link:'\n          <path fill="currentColor" d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z"/>\n        ',medal:'\n          <path fill="currentColor" d="M5,16L3,5L8.5,12L12,4L15.5,12L21,5L19,16H5M12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13Z"/>\n        '};return e[this.name]||""}}};var sa=n(1169);const ia=(0,sa.A)(ta,[["render",na],["__scopeId","data-v-477bbaf6"]]),la=ia,ca={name:"QueryCenter",components:{TheHeader:ea.A,SvgIcon:la},setup:function(){var e=(0,Ge.KR)("certificate"),a=(0,Ge.KR)(!1),n=(0,Ge.KR)(!1),s=(0,Ge.KR)([]),i=(0,Ge.KR)(new Date),l=null,c=[{title:"证书验证",value:"certificate",icon:"el-icon-medal",iconName:"certificate",description:"验证安全漏洞发现证书的真实性和有效性"},{title:"区块链验证中心",value:"blockchain",icon:"el-icon-link",iconName:"blockchain",description:"查询和验证区块链上的交易记录和智能合约状态"}],r=(0,Ge.Kh)({number:"",blockchainHash:""}),o=(0,Ge.Kh)({txHash:"",blockNumber:""}),u=function(){var e=5*Math.random(),a=3+4*Math.random(),n=2+4*Math.random();return{left:100*Math.random()+"%",top:100*Math.random()+"%",width:n+"px",height:n+"px",animationDelay:e+"s",animationDuration:a+"s"}},d=function(e){var a=45*e+30*Math.random(),n=100+200*Math.random();return{transform:"rotate(".concat(a,"deg)"),width:n+"px",left:100*Math.random()+"%",top:100*Math.random()+"%",animationDelay:3*Math.random()+"s"}},v=function(a){e.value=a,s.value=[],n.value=!1},f=function(){var e=(0,Ye.A)((0,We.A)().m(function e(){var t,i,l,c;return(0,We.A)().w(function(e){while(1)switch(e.n){case 0:if(r.number&&r.blockchainHash){e.n=1;break}return $e.nk.warning("请输入证书编号和区块链哈希"),e.a(2);case 1:return a.value=!0,n.value=!0,e.p=2,e.n=3,fetch("/api/certificates/verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({certificate_number:r.number,block_hash:r.blockchainHash})});case 3:return t=e.v,e.n=4,t.json();case 4:i=e.v,t.ok&&i.success?(l={id:i.data.id,type:"certificate",title:i.data.vulnerability_type+"漏洞发现证书",description:"恭喜您成功发现并报告了一个".concat(i.data.severity_level,"漏洞，该漏洞已被确认并修复。"),status:"已验证",certificateNumber:i.data.certificate_number,blockchainHash:i.data.block_hash,blockNumber:i.data.block_number,createdAt:i.data.created_at,issuer:i.data.issuer||"CCIBE安全认证中心",recipient:i.data.recipient_name,vulnerabilityLevel:i.data.severity_level,vulnerabilityType:i.data.vulnerability_type,companyName:i.data.company_name},s.value=[l],$e.nk.success("证书验证成功！")):(s.value=[],$e.nk.error({message:"相关数据错误，该证书可能是假的，请仔细检查。若有疑问请联系平台管理员：<EMAIL>",duration:8e3,showClose:!0})),e.n=6;break;case 5:e.p=5,c=e.v,console.error("查询证书失败:",c),s.value=[],$e.nk.error({message:"证书验证失败，网络连接异常。若有疑问请联系平台管理员：<EMAIL>",duration:8e3,showClose:!0});case 6:return e.p=6,a.value=!1,e.f(6);case 7:return e.a(2)}},e,null,[[2,5,6,7]])}));return function(){return e.apply(this,arguments)}}(),p=function(){var e=(0,Ye.A)((0,We.A)().m(function e(){var t,i,l,c,r;return(0,We.A)().w(function(e){while(1)switch(e.n){case 0:if(o.txHash){e.n=1;break}return $e.nk.warning("请输入交易哈希"),e.a(2);case 1:return a.value=!0,n.value=!0,e.p=2,e.n=3,fetch("/api/blockchain/query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({transaction_hash:o.txHash})});case 3:return t=e.v,e.n=4,t.json();case 4:if(i=e.v,console.log("数据库查询结果:",i),t.ok&&i.success&&0!==i.data.length){e.n=5;break}return s.value=[],$e.nk.error({message:"您输入的交易哈希有误，请仔细检查，可能存在问题！若有疑问请联系管理员：<EMAIL>",duration:8e3,showClose:!0}),e.a(2);case 5:l=i.hasWebaseData,c=i.data.map(function(e){console.log("处理数据库记录:",e);var a,n,t=y(e.vulnerability_type),s=k(e.severity_level);"vulnerability"===e.type?(a="".concat(t,"漏洞记录"),n="发现了一个".concat(s,"级别的").concat(t,"漏洞，已记录在").concat(l?"区块链":"数据库","中。")):"announcement"===e.type?(a="".concat(e.title||"系统公告"),n=e.content||"系统公告记录"):"security_task"===e.type?(a="".concat(e.title||"安全任务"),n="安全测试任务，预算范围：¥".concat(e.reward_range,"，状态：").concat(M(e.status))):(a="区块链记录",n="未知类型的区块链记录");var i={id:"blockchain_"+Date.now()+"_"+Math.random().toString(36).substring(2,11),type:"blockchain",title:a,description:n,status:l?"已确认":"数据库记录",dbInfo:{type:e.type,vulnerabilityType:e.vulnerability_type,severityLevel:e.severity_level,companyName:e.company_name,submitterName:e.submitter_name,title:e.title,author:e.author,content:e.content,publisherName:e.publisher_name,rewardRange:e.reward_range,rewards:e.rewards,status:e.status,description:e.description,createdAt:e.created_at},createdAt:e.created_at};return l&&e.webaseData&&(i.blockchainInfo={transactionHash:e.webaseData.transactionHash,blockHash:e.webaseData.blockHash,blockNumber:e.webaseData.blockNumber,gasUsed:e.webaseData.gasUsed,contractAddress:e.webaseData.contractAddress,from:e.webaseData.from,to:e.webaseData.to,status:e.webaseData.status,statusMsg:e.webaseData.statusMsg}),i}),console.log("最终结果数据:",c),s.value=c,l?$e.nk.success("区块链查询成功！"):$e.nk.success("查询成功！（区块链API不可用，仅显示数据库记录）"),e.n=7;break;case 6:e.p=6,r=e.v,console.error("查询区块链失败:",r),r.message&&r.message.includes("fetch")?(s.value=[],$e.nk.error({message:"查询失败，网络连接异常。若有疑问请联系管理员：<EMAIL>",duration:8e3,showClose:!0})):(s.value=[],$e.nk.error({message:"查询处理失败，请稍后重试。若有疑问请联系管理员：<EMAIL>",duration:8e3,showClose:!0}));case 7:return e.p=7,a.value=!1,e.f(7);case 8:return e.a(2)}},e,null,[[2,6,7,8]])}));return function(){return e.apply(this,arguments)}}(),b=function(e){var a={certificate:"安全证书",vulnerability:"漏洞记录",announcement:"系统公告",security_task:"安全任务"};return a[e]||e},k=function(e){var a={info:"提示",low:"低危",medium:"中危",high:"高危",critical:"严重"};return a[e]||e},y=function(e){var a={sql_injection:"SQL注入",xss:"XSS跨站脚本",csrf:"CSRF跨站请求伪造",file_upload:"文件上传漏洞",auth_bypass:"权限绕过",information_disclosure:"信息泄露",business_logic:"业务逻辑漏洞",command_execution:"命令执行",rce:"命令执行",lfi:"本地文件包含",rfi:"远程文件包含",xxe:"XXE外部实体注入",ssrf:"SSRF服务端请求伪造",deserialization:"反序列化漏洞",privilege_escalation:"权限提升",directory_traversal:"目录遍历",weak_authentication:"弱认证",session_management:"会话管理缺陷",access_control:"访问控制缺陷"};return a[e]||"其他"},h=function(e){var a={info:"severity-info",low:"severity-low",medium:"severity-medium",high:"severity-high",critical:"severity-critical"};return a[e]||"severity-default"},L=function(e){var a={certificate:"el-icon-medal",blockchain:"el-icon-link"};return a[e]||"el-icon-document"},m=function(e){var a={certificate:"certificate",vulnerability:"bug",announcement:"announcement",security_task:"task"};return a[e]||"document"},g=function(e){var a={已验证:"verified",有效:"valid",已确认:"confirmed",无效:"invalid",过期:"expired"};return a[e]||"default"},C=function(e){return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},w=function(){$e.nk.info("查看详情功能开发中...")},_=function(){$e.nk.info("证书下载功能开发中...")},H=function(){return i.value.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},I=function(e){var a={vulnerability:"漏洞记录信息",announcement:"公告记录信息",security_task:"安全任务信息"};return a[e]||"记录信息"},A=function(e){return e?"string"===typeof e&&e.includes("-")?"¥".concat(e):"¥".concat(parseFloat(e).toLocaleString()):"未设置"},M=function(e){var a={draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已完成",cancelled:"已取消",paused:"已暂停"};return a[e]||e},V=function(e){var a={draft:"status-draft",published:"status-published",in_progress:"status-progress",completed:"status-completed",cancelled:"status-cancelled",paused:"status-paused"};return a[e]||"status-default"},S=function(e,a){var n="certificateQuery"===e?r:o;n[a]&&n[a].startsWith(" ")&&(n[a]=n[a].replace(/^\s+/,""))};return(0,t.sV)(function(){l=setInterval(function(){i.value=new Date},1e3)}),(0,t.hi)(function(){l&&clearInterval(l)}),{currentFunction:e,loading:a,hasSearched:n,queryResults:s,functions:c,certificateQuery:r,blockchainQuery:o,getParticleStyle:u,getLineStyle:d,switchFunction:v,queryCertificate:f,queryBlockchain:p,getResultTypeText:b,getResultIcon:L,getResultIconName:m,getStatusClass:g,getSeverityLevelText:k,getVulnerabilityTypeText:y,getSeverityClass:h,formatDate:C,getCurrentTime:H,getRecordTypeTitle:I,formatReward:A,getTaskStatusText:M,getTaskStatusClass:V,trimLeadingSpaces:S,viewDetails:w,downloadCertificate:_}}},ra=(0,sa.A)(ca,[["render",Oe],["__scopeId","data-v-c5efb208"]]),oa=ra}}]);