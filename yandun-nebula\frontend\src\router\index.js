import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/Home.vue';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import NotFound from '../views/NotFound.vue';
import UserProfile from '../views/UserProfile.vue';
import Projects from '../views/Projects.vue';
import auth from '../utils/auth'; // Added import for auth utility
import AI from '../views/AI.vue';
import Ranking from '../views/Ranking.vue';

const routes = [{
        path: '/',
        name: 'Home',
        component: Home,
        meta: {
            title: '首页 - 衍盾星云'
        }
    },
    {
        path: '/login',
        name: 'Login',
        component: Login,
        meta: {
            title: '登录 - 衍盾星云'
        }
    },
    {
        path: '/register',
        name: 'Register',
        component: Register,
        meta: {
            title: '注册 - 衍盾星云'
        }
    },
    {
        path: '/user/profile',
        name: 'UserProfile',
        component: UserProfile,
        meta: {
            title: '个人中心 - 衍盾星云',
            requiresAuth: true
        }
    },
    // 添加项目大厅路由
    {
        path: '/projects',
        name: 'Projects',
        component: Projects,
        meta: {
            title: '项目大厅 - 衍盾星云'
        }
    },
    // 添加项目详情页路由
    {
        path: '/projects/:id',
        name: 'ProjectDetail',
        component: () =>
            import ('../views/ProjectDetail.vue'),
        meta: {
            title: '项目详情 - 衍盾星云'
        }
    },
    // 添加项目参与页路由
    {
        path: '/projects/:id/participate',
        name: 'ProjectParticipate',
        component: () =>
            import ('../views/ProjectParticipate.vue'),
        meta: {
            title: '参与项目 - 衍盾星云',
            requiresAuth: true
        }
    },
    {
        path: '/user/submissions',
        name: 'UserSubmissions',
        component: UserProfile,
        props: { activeTab: 'submissions' },
        meta: {
            title: '我的提交 - 衍盾星云',
            requiresAuth: true
        }
    },
    {
        path: '/user/rewards',
        name: 'UserRewards',
        component: UserProfile,
        props: { activeTab: 'rewards' },
        meta: {
            title: '我的奖励 - 衍盾星云',
            requiresAuth: true
        }
    },

    {
        path: '/submit',
        name: 'SubmitVulnerability',
        component: () =>
            import ('../views/SubmitVulnerability.vue'),
        meta: {
            title: '提交漏洞 - 衍盾星云',
            requiresAuth: true,
            requiresUserType: 'whiteHat'
        }
    },
    {
        path: '/enterprise/publish',
        name: 'EnterprisePublish',
        component: () =>
            import ('../views/enterprise/PublishTask.vue'),
        meta: {
            title: '任务发布 - 衍盾星云',
            requiresAuth: true,
            requiresUserType: 'enterprise'
        }
    },
    {
        path: '/enterprise/review',
        name: 'EnterpriseReview',
        component: () =>
            import ('../views/enterprise/ReviewTask.vue'),
        meta: {
            title: '任务审核 - 衍盾星云',
            requiresAuth: true,
            requiresUserType: 'enterprise'
        }
    },
    {
        path: '/ai',
        name: 'AI',
        component: AI,
        meta: {
            title: 'AI问答 - 衍盾星云'
        }
    },
    {
        path: '/query',
        name: 'QueryCenter',
        component: () =>
            import ('../views/QueryCenter.vue'),
        meta: {
            title: '查询中心 - 衍盾星云'
        }
    },
    {
        path: '/ranking',
        name: 'Ranking',
        component: Ranking,
        meta: {
            title: '排行榜 - 衍盾星云'
        }
    },
    {
        path: '/activities',
        name: 'Activities',
        component: () =>
            import ('../views/Activities.vue'),
        meta: {
            title: '活动中心 - 衍盾星云'
        }
    },
    // 添加活动详情页路由
    {
        path: '/activities/:id',
        name: 'ActivityDetail',
        component: () =>
            import ('../views/ActivityDetail.vue'),
        meta: {
            title: '活动详情 - 衍盾星云'
        }
    },
    // 添加社区相关路由
    {
        path: '/community',
        name: 'Community',
        component: () =>
            import ('../views/Community.vue'),
        meta: {
            title: '交流中心 - 衍盾星云'
        }
    },
    {
        path: '/community/posts/:id',
        name: 'PostDetail',
        component: () =>
            import ('../views/PostDetail.vue'),
        meta: {
            title: '帖子详情 - 衍盾星云'
        }
    },
    {
        path: '/help',
        name: 'Help',
        component: () =>
            import ('../views/Help.vue'),
        meta: {
            title: '帮助中心 - 衍盾星云'
        }
    },
    // 管理员相关路由
    {
        path: '/admin/login',
        name: 'AdminLogin',
        component: () =>
            import ('../views/AdminLogin.vue'),
        meta: {
            title: '管理员登录 - 衍盾星云'
        }
    },
    {
        path: '/admin',
        component: () =>
            import ('../layouts/AdminLayout.vue'),
        meta: {
            requiresAuth: true,
            requiresUserType: 'admin'
        },
        children: [{
                path: 'dashboard',
                name: 'AdminDashboard',
                component: () =>
                    import ('../views/admin/Dashboard.vue'),
                meta: {
                    title: '管理后台 - 衍盾星云'
                }
            },
            {
                path: 'users',
                name: 'AdminUsers',
                component: () =>
                    import ('../views/admin/UserManagement.vue'),
                meta: {
                    title: '用户管理 - 衍盾星云'
                }
            },
            {
                path: 'posts',
                name: 'AdminPosts',
                component: () =>
                    import ('../views/admin/PostManagement.vue'),
                meta: {
                    title: '帖子管理 - 衍盾星云'
                }
            },
            {
                path: 'projects',
                name: 'AdminProjects',
                component: () =>
                    import ('../views/admin/ProjectManagement.vue'),
                meta: {
                    title: '项目管理 - 衍盾星云'
                }
            },
            {
                path: 'announcements',
                name: 'AdminAnnouncements',
                component: () =>
                    import ('../views/admin/AnnouncementManagement.vue'),
                meta: {
                    title: '公告管理 - 衍盾星云'
                }
            },
            {
                path: 'disputes',
                name: 'AdminDisputes',
                component: () =>
                    import ('../views/admin/DisputeManagement.vue'),
                meta: {
                    title: '争议审核 - 衍盾星云'
                }
            },
            {
                path: 'settings',
                name: 'AdminSettings',
                component: () =>
                    import ('../views/admin/SystemSettings.vue'),
                meta: {
                    title: '系统设置 - 衍盾星云'
                }
            }
        ]
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: NotFound,
        meta: {
            title: '页面未找到 - 衍盾星云'
        }
    }
];

const router = createRouter({
    // 使用 Hash 模式（不需要服务器特殊配置）
    history: createWebHashHistory(),
    // 如果服务器配置好了，可以使用 History 模式
    // history: createWebHistory(),
    routes,
    scrollBehavior() {
        return { top: 0 };
    }
});

// 全局导航守卫
router.beforeEach(async(to, from, next) => {
    // 设置页面标题
    document.title = to.meta.title || '衍盾星云 - 基于区块链的漏洞悬赏智能响应平台';

    // 检查是否需要登录权限
    if (to.meta.requiresAuth) {
        // 首先快速检查本地登录状态
        if (!auth.isLoggedIn()) {
            // 未登录，重定向到登录页
            next({
                path: '/login',
                query: { redirect: to.fullPath }
            });
            return;
        }

        // 异步验证登录状态，但不阻塞路由跳转
        auth.validateLoginStatus().catch(error => {
            console.error('路由守卫验证登录状态失败:', error);
        });

        // 检查用户类型权限
        if (to.meta.requiresUserType) {
            const currentUser = auth.getCurrentUser();
            if (!currentUser || currentUser.userType !== to.meta.requiresUserType) {
                // 用户类型不匹配，重定向到首页
                next({ path: '/' });
                return;
            }
        }
        next();
    } else {
        next();
    }
});

export default router;