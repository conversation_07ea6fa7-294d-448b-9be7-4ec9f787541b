"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[331],{65331:(e,t,i)=>{i.r(t),i.d(t,{default:()=>gi});i(52675),i(89463),i(74423);var n=i(95976),s=i(10160),a={class:"project-detail-container"},l={class:"detail-content container"},c={class:"project-header"},o={class:"company-info"},r={class:"company-details"},u={class:"project-title"},d={class:"company-name"},v={class:"project-status"},k={class:"project-stats"},f={class:"stat-item"},m={class:"stat-value"},p={class:"stat-item"},h={class:"stat-value"},b={class:"stat-item"},L={class:"stat-value"},g={class:"stat-item"},_={class:"stat-value"},y={key:0,class:"project-section blockchain-section"},w={class:"section-title"},S={class:"section-content"},C={class:"blockchain-info-card"},x={class:"blockchain-fields"},F={class:"field-row"},z={class:"field-item"},T={class:"field-value hash-value"},V={class:"hash-text"},H={class:"field-row"},W={class:"field-item"},j={class:"field-value hash-value"},B={class:"hash-text"},M={class:"field-row"},A={class:"field-item"},R={class:"field-value"},X={class:"project-section"},D=["innerHTML"],E={class:"project-section"},Q=["innerHTML"],K={class:"project-section"},I={class:"section-content"},N={class:"scope-list"},U={class:"scope-item"},P={class:"project-section"},q={class:"section-content"},Y={class:"rewards-grid"},O={class:"reward-item low"},J={class:"reward-amount"},G={class:"reward-item medium"},Z={class:"reward-amount"},$={class:"reward-item high"},ee={class:"reward-amount"},te={class:"reward-item critical"},ie={class:"reward-amount"},ne={class:"project-section"},se={class:"section-header"},ae={class:"section-actions"},le={class:"section-content"},ce={class:"submission-title-cell"},oe={class:"title-text"},re={class:"submitter-text"},ue={key:0},de={key:1},ve={class:"action-buttons"},ke={key:0,class:"pagination-container"},fe={key:0,class:"submission-detail"},me={class:"card-header"},pe={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},he={class:"blockchain-content"},be={class:"blockchain-section submission-section"},Le={class:"section-title"},ge={class:"blockchain-fields"},_e={class:"field-row"},ye={class:"field-item"},we={class:"field-value hash-value"},Se={class:"hash-text"},Ce={class:"field-row"},xe={class:"field-item"},Fe={class:"field-value hash-value"},ze={class:"hash-text"},Te={class:"field-row"},Ve={class:"field-item"},He={class:"field-value"},We={class:"card-header"},je={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Be={class:"blockchain-content"},Me={class:"blockchain-section approval-section"},Ae={class:"section-title"},Re={class:"blockchain-fields"},Xe={class:"field-row"},De={class:"field-item"},Ee={class:"field-value hash-value"},Qe={class:"hash-text"},Ke={class:"field-row"},Ie={class:"field-item"},Ne={class:"field-value hash-value"},Ue={class:"hash-text"},Pe={class:"field-row"},qe={class:"field-item"},Ye={class:"field-value"},Oe={class:"card-header"},Je={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Ge={class:"blockchain-content"},Ze={class:"blockchain-info-grid"},$e={class:"info-item"},et={class:"info-value"},tt={class:"info-item"},it={class:"info-value hash-value"},nt={class:"hash-text"},st={class:"info-item"},at={class:"info-value hash-value"},lt={class:"hash-text"},ct={class:"info-item"},ot={class:"info-value"},rt={class:"description-content"},ut={class:"urls-content"},dt={class:"url-text"},vt={class:"links-content"},kt={key:0,class:"link-item"},ft={class:"link-value"},mt={key:1,class:"link-item"},pt={class:"link-urls"},ht={class:"url-text"},bt={class:"steps-content"},Lt={class:"remediation-content"},gt={class:"review-content"},_t={class:"card-header"},yt={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},wt={class:"blockchain-content"},St={key:0,class:"blockchain-section confirmation-section"},Ct={class:"section-title"},xt={class:"blockchain-fields"},Ft={class:"field-row"},zt={class:"field-item"},Tt={class:"field-value hash-value"},Vt={class:"hash-text"},Ht={class:"field-row"},Wt={class:"field-item"},jt={class:"field-value hash-value"},Bt={class:"hash-text"},Mt={class:"field-row"},At={class:"field-item"},Rt={class:"field-value"},Xt={key:1,class:"blockchain-section dispute-section"},Dt={class:"section-title"},Et={class:"blockchain-fields"},Qt={class:"field-row"},Kt={class:"field-item"},It={class:"field-value hash-value"},Nt={class:"hash-text"},Ut={class:"field-row"},Pt={class:"field-item"},qt={class:"field-value hash-value"},Yt={class:"hash-text"},Ot={class:"field-row"},Jt={class:"field-item"},Gt={class:"field-value"},Zt={class:"dialog-footer"};function $t(e,t,i,$t,ei,ti){var ii=(0,n.g2)("TheHeader"),ni=(0,n.g2)("el-tag"),si=(0,n.g2)("el-button"),ai=(0,n.g2)("el-option"),li=(0,n.g2)("el-select"),ci=(0,n.g2)("el-table-column"),oi=(0,n.g2)("el-table"),ri=(0,n.g2)("el-pagination"),ui=(0,n.g2)("el-card"),di=(0,n.g2)("el-descriptions-item"),vi=(0,n.g2)("el-descriptions"),ki=(0,n.g2)("el-dialog"),fi=(0,n.g2)("VulnerabilityConfirmDialog"),mi=(0,n.gN)("loading");return(0,n.uX)(),(0,n.CE)("div",a,[(0,n.bF)(ii),(0,n.Lk)("div",l,[(0,n.bo)(((0,n.uX)(),(0,n.Wv)(ui,null,{default:(0,n.k6)(function(){var e,i;return[$t.project?((0,n.uX)(),(0,n.CE)(n.FK,{key:0},[(0,n.Lk)("div",c,[(0,n.Lk)("div",o,[(0,n.Lk)("img",{src:"/logo.png",class:"company-logo",alt:"企业logo",onError:t[0]||(t[0]=function(){return $t.handleImageError&&$t.handleImageError.apply($t,arguments)})},null,32),(0,n.Lk)("div",r,[(0,n.Lk)("h1",u,(0,s.v_)($t.project.title),1),(0,n.Lk)("p",d,(0,s.v_)((null===(e=$t.project.publisher)||void 0===e?void 0:e.companyName)||(null===(i=$t.project.publisher)||void 0===i?void 0:i.username)),1)])]),(0,n.Lk)("div",v,[(0,n.bF)(ni,{type:$t.getStatusType($t.project.status)},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getStatusText($t.project.status)),1)]}),_:1},8,["type"])])]),(0,n.Lk)("div",k,[(0,n.Lk)("div",f,[t[17]||(t[17]=(0,n.Lk)("span",{class:"stat-label"},"预算范围",-1)),(0,n.Lk)("span",m,"¥"+(0,s.v_)($t.formatNumber($t.project.totalBudgetMin))+"-"+(0,s.v_)($t.formatNumber($t.project.totalBudgetMax)),1)]),(0,n.Lk)("div",p,[t[18]||(t[18]=(0,n.Lk)("span",{class:"stat-label"},"参与人数",-1)),(0,n.Lk)("span",h,(0,s.v_)($t.project.participants_count||0)+"人",1)]),(0,n.Lk)("div",b,[t[19]||(t[19]=(0,n.Lk)("span",{class:"stat-label"},"提交数量",-1)),(0,n.Lk)("span",L,(0,s.v_)($t.project.submissions_count||0)+"个",1)]),(0,n.Lk)("div",g,[t[20]||(t[20]=(0,n.Lk)("span",{class:"stat-label"},"结束时间",-1)),(0,n.Lk)("span",_,(0,s.v_)($t.formatDate($t.project.endTime)),1)])]),$t.hasBlockchainInfo($t.project)?((0,n.uX)(),(0,n.CE)("div",y,[(0,n.Lk)("h2",w,[t[22]||(t[22]=(0,n.Lk)("i",{class:"el-icon-link"},null,-1)),t[23]||(t[23]=(0,n.eW)(" 区块链记录 ")),(0,n.bF)(ni,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return t[21]||(t[21]=[(0,n.Lk)("i",{class:"el-icon-check",style:{"margin-right":"4px"}},null,-1),(0,n.eW)(" 链上可验证 ")])}),_:1,__:[21]})]),(0,n.Lk)("div",S,[(0,n.Lk)("div",C,[(0,n.Lk)("div",x,[(0,n.Lk)("div",F,[(0,n.Lk)("div",z,[t[25]||(t[25]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",T,[(0,n.Lk)("span",V,(0,s.v_)($t.project.transactionHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[1]||(t[1]=function(e){return $t.copyToClipboard($t.project.transactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[24]||(t[24]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[24]})])])]),(0,n.Lk)("div",H,[(0,n.Lk)("div",W,[t[27]||(t[27]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",j,[(0,n.Lk)("span",B,(0,s.v_)($t.project.blockHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[2]||(t[2]=function(e){return $t.copyToClipboard($t.project.blockHash,"区块哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[26]||(t[26]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[26]})])])]),(0,n.Lk)("div",M,[(0,n.Lk)("div",A,[t[28]||(t[28]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",R,(0,s.v_)($t.formatTimestamp($t.project.blockchainTimestamp)),1)])])]),t[29]||(t[29]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("i",{class:"el-icon-info notice-icon"}),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"此任务发布信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])])])):(0,n.Q3)("",!0),(0,n.Lk)("div",X,[t[30]||(t[30]=(0,n.Lk)("h2",{class:"section-title"},"项目描述",-1)),(0,n.Lk)("div",{class:"section-content",innerHTML:$t.project.description},null,8,D)]),(0,n.Lk)("div",E,[t[31]||(t[31]=(0,n.Lk)("h2",{class:"section-title"},"项目规则",-1)),(0,n.Lk)("div",{class:"section-content",innerHTML:$t.project.requirements||$t.project.rules||"请按照项目描述进行安全测试"},null,8,Q)]),(0,n.Lk)("div",K,[t[33]||(t[33]=(0,n.Lk)("h2",{class:"section-title"},"测试范围",-1)),(0,n.Lk)("div",I,[(0,n.Lk)("div",N,[(0,n.Lk)("div",U,[t[32]||(t[32]=(0,n.Lk)("i",{class:"el-icon-check scope-icon"},null,-1)),(0,n.Lk)("span",null,(0,s.v_)($t.project.testScope||"根据项目描述进行安全测试"),1)])])])]),(0,n.Lk)("div",P,[t[38]||(t[38]=(0,n.Lk)("h2",{class:"section-title"},"奖励标准",-1)),(0,n.Lk)("div",q,[(0,n.Lk)("div",Y,[(0,n.Lk)("div",O,[t[34]||(t[34]=(0,n.Lk)("div",{class:"reward-level"},"低危",-1)),(0,n.Lk)("div",J,"¥"+(0,s.v_)($t.formatNumber($t.project.lowVulReward||0)),1)]),(0,n.Lk)("div",G,[t[35]||(t[35]=(0,n.Lk)("div",{class:"reward-level"},"中危",-1)),(0,n.Lk)("div",Z,"¥"+(0,s.v_)($t.formatNumber($t.project.mediumVulReward||0)),1)]),(0,n.Lk)("div",$,[t[36]||(t[36]=(0,n.Lk)("div",{class:"reward-level"},"高危",-1)),(0,n.Lk)("div",ee,"¥"+(0,s.v_)($t.formatNumber($t.project.highVulReward||0)),1)]),(0,n.Lk)("div",te,[t[37]||(t[37]=(0,n.Lk)("div",{class:"reward-level"},"严重",-1)),(0,n.Lk)("div",ie,"¥"+(0,s.v_)($t.formatNumber($t.project.criticalVulReward||0)),1)])])])]),(0,n.Lk)("div",ne,[(0,n.Lk)("div",se,[t[39]||(t[39]=(0,n.Lk)("h2",{class:"section-title"},"提交记录",-1)),(0,n.Lk)("div",ae,[(0,n.bF)(li,{modelValue:$t.submissionFilter,"onUpdate:modelValue":t[3]||(t[3]=function(e){return $t.submissionFilter=e}),placeholder:"筛选状态",size:"small",style:{width:"140px"},onChange:$t.fetchSubmissions},{default:(0,n.k6)(function(){return[(0,n.bF)(ai,{label:"全部",value:""}),(0,n.bF)(ai,{label:"待企业审核",value:"pending"}),(0,n.bF)(ai,{label:"待用户确认",value:"enterprise_confirmed"}),(0,n.bF)(ai,{label:"用户已确认",value:"user_confirmed"}),(0,n.bF)(ai,{label:"争议中",value:"dispute"}),(0,n.bF)(ai,{label:"管理员确认",value:"admin_confirmed"}),(0,n.bF)(ai,{label:"已拒绝",value:"enterprise_rejected"}),(0,n.bF)(ai,{label:"自动确认",value:"auto_confirmed"})]}),_:1},8,["modelValue","onChange"]),(0,n.bF)(li,{modelValue:$t.severityFilter,"onUpdate:modelValue":t[4]||(t[4]=function(e){return $t.severityFilter=e}),placeholder:"危险程度",size:"small",style:{width:"120px"},onChange:$t.fetchSubmissions},{default:(0,n.k6)(function(){return[(0,n.bF)(ai,{label:"全部",value:""}),(0,n.bF)(ai,{label:"严重",value:"critical"}),(0,n.bF)(ai,{label:"高危",value:"high"}),(0,n.bF)(ai,{label:"中危",value:"medium"}),(0,n.bF)(ai,{label:"低危",value:"low"})]}),_:1},8,["modelValue","onChange"])])]),(0,n.Lk)("div",le,[(0,n.bo)(((0,n.uX)(),(0,n.Wv)(oi,{data:$t.submissions,style:{width:"100%"},"empty-text":$t.submissionsLoading?"加载中...":"暂无提交记录"},{default:(0,n.k6)(function(){return[(0,n.bF)(ci,{prop:"title",label:"漏洞标题","min-width":"200"},{default:(0,n.k6)(function(e){return[(0,n.Lk)("div",ce,[(0,n.Lk)("span",oe,(0,s.v_)(e.row.title),1),(0,n.Lk)("span",re,"提交者："+(0,s.v_)(e.row.submitter),1)])]}),_:1}),(0,n.bF)(ci,{prop:"severity",label:"危险程度",width:"100"},{default:(0,n.k6)(function(e){return[(0,n.bF)(ni,{type:$t.getSeverityType(e.row.severity),size:"small"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getSeverityText(e.row.severity)),1)]}),_:2},1032,["type"])]}),_:1}),(0,n.bF)(ci,{prop:"vulnerabilityType",label:"漏洞类型",width:"120"},{default:(0,n.k6)(function(e){return[(0,n.Lk)("span",null,(0,s.v_)(e.row.vulnerabilityType||"未分类"),1)]}),_:1}),(0,n.bF)(ci,{prop:"status",label:"审核状态",width:"100"},{default:(0,n.k6)(function(e){return[(0,n.bF)(ni,{type:$t.getStatusType(e.row.status),size:"small"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getStatusText(e.row.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,n.bF)(ci,{prop:"reward",label:"奖励金额",width:"100"},{default:(0,n.k6)(function(e){return[e.row.reward?((0,n.uX)(),(0,n.CE)("span",ue,"¥"+(0,s.v_)($t.formatNumber(e.row.reward)),1)):((0,n.uX)(),(0,n.CE)("span",de,"-"))]}),_:1}),(0,n.bF)(ci,{prop:"createdAt",label:"提交时间",width:"150"},{default:(0,n.k6)(function(e){return[(0,n.eW)((0,s.v_)($t.formatDateTime(e.row.createdAt)),1)]}),_:1}),(0,n.bF)(ci,{label:"操作",width:"220",fixed:"right"},{default:(0,n.k6)(function(e){var i;return[(0,n.Lk)("div",ve,[(0,n.bF)(si,{size:"small",type:"primary",onClick:function(t){return $t.viewSubmissionDetail(e.row)}},{default:(0,n.k6)(function(){return t[40]||(t[40]=[(0,n.eW)(" 查看详情 ")])}),_:2,__:[40]},1032,["onClick"]),"enterprise_confirmed"===e.row.status&&e.row.submitter===(null===(i=$t.currentUser)||void 0===i?void 0:i.username)?((0,n.uX)(),(0,n.CE)(n.FK,{key:0},[(0,n.bF)(si,{size:"small",type:"success",onClick:function(t){return $t.handleUserAction(e.row,"confirm")}},{default:(0,n.k6)(function(){return t[41]||(t[41]=[(0,n.eW)(" 确认 ")])}),_:2,__:[41]},1032,["onClick"]),(0,n.bF)(si,{size:"small",type:"warning",onClick:function(t){return $t.handleUserAction(e.row,"dispute")}},{default:(0,n.k6)(function(){return t[42]||(t[42]=[(0,n.eW)(" 申诉 ")])}),_:2,__:[42]},1032,["onClick"])],64)):"dispute"===e.row.status?((0,n.uX)(),(0,n.Wv)(ni,{key:1,type:"warning",size:"small"},{default:(0,n.k6)(function(){return t[43]||(t[43]=[(0,n.eW)("争议审核中")])}),_:1,__:[43]})):["user_confirmed","admin_confirmed","auto_confirmed"].includes(e.row.status)?((0,n.uX)(),(0,n.Wv)(ni,{key:2,type:"success",size:"small"},{default:(0,n.k6)(function(){return t[44]||(t[44]=[(0,n.eW)("已完成")])}),_:1,__:[44]})):(0,n.Q3)("",!0)])]}),_:1})]}),_:1},8,["data","empty-text"])),[[mi,$t.submissionsLoading]]),$t.submissionTotal>0?((0,n.uX)(),(0,n.CE)("div",ke,[(0,n.bF)(ri,{background:"",layout:"prev, pager, next",total:$t.submissionTotal,"page-size":$t.submissionPageSize,"current-page":$t.submissionCurrentPage,onCurrentChange:$t.handleSubmissionPageChange},null,8,["total","page-size","current-page","onCurrentChange"])])):(0,n.Q3)("",!0)])])],64)):(0,n.Q3)("",!0)]}),_:1})),[[mi,$t.loading]])]),(0,n.bF)(ki,{modelValue:$t.submissionDetailVisible,"onUpdate:modelValue":t[15]||(t[15]=function(e){return $t.submissionDetailVisible=e}),title:$t.selectedSubmission?$t.selectedSubmission.title:"提交详情",width:"80%","before-close":$t.closeSubmissionDetail,center:"",top:"5vh",class:"submission-detail-dialog"},{footer:(0,n.k6)(function(){return[(0,n.Lk)("div",Zt,[(0,n.bF)(si,{onClick:$t.closeSubmissionDetail},{default:(0,n.k6)(function(){return t[112]||(t[112]=[(0,n.eW)("关闭")])}),_:1,__:[112]},8,["onClick"])])]}),default:(0,n.k6)(function(){return[$t.selectedSubmission?((0,n.uX)(),(0,n.CE)("div",fe,[(0,n.bF)(ui,{class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[45]||(t[45]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-info"}),(0,n.Lk)("span",null,"基本信息")],-1)])}),default:(0,n.k6)(function(){return[(0,n.bF)(vi,{column:2,border:""},{default:(0,n.k6)(function(){return[(0,n.bF)(di,{label:"漏洞标题",span:2},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.selectedSubmission.title),1)]}),_:1}),(0,n.bF)(di,{label:"提交者"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.selectedSubmission.submitter),1)]}),_:1}),(0,n.bF)(di,{label:"提交时间"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.formatDateTime($t.selectedSubmission.createdAt)),1)]}),_:1}),(0,n.bF)(di,{label:"危险程度"},{default:(0,n.k6)(function(){return[(0,n.bF)(ni,{type:$t.getSeverityType($t.selectedSubmission.severity)},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getSeverityText($t.selectedSubmission.severity)),1)]}),_:1},8,["type"])]}),_:1}),(0,n.bF)(di,{label:"审核状态"},{default:(0,n.k6)(function(){return[(0,n.bF)(ni,{type:$t.getStatusType($t.selectedSubmission.status)},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getStatusText($t.selectedSubmission.status)),1)]}),_:1},8,["type"])]}),_:1}),$t.selectedSubmission.reward?((0,n.uX)(),(0,n.Wv)(di,{key:0,label:"奖励金额"},{default:(0,n.k6)(function(){return[(0,n.eW)(" ¥"+(0,s.v_)($t.formatNumber($t.selectedSubmission.reward)),1)]}),_:1})):(0,n.Q3)("",!0),(0,n.bF)(di,{label:"漏洞类型"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.selectedSubmission.vulnerabilityType||"未分类"),1)]}),_:1}),$t.selectedSubmission.domain?((0,n.uX)(),(0,n.Wv)(di,{key:1,label:"相关域名"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.selectedSubmission.domain),1)]}),_:1})):(0,n.Q3)("",!0)]}),_:1})]}),_:1}),$t.hasSubmissionBlockchainInfo($t.selectedSubmission)?((0,n.uX)(),(0,n.Wv)(ui,{key:0,class:"detail-card submission-blockchain-info-card",shadow:"never"},{header:(0,n.k6)(function(){return[(0,n.Lk)("div",me,[t[48]||(t[48]=(0,n.Lk)("div",{class:"blockchain-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,n.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),t[49]||(t[49]=(0,n.Lk)("span",null,"漏洞提交记录 - 区块链验证",-1)),(0,n.bF)(ni,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return[((0,n.uX)(),(0,n.CE)("svg",pe,t[46]||(t[46]=[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),t[47]||(t[47]=(0,n.eW)(" 链上可验证 "))]}),_:1,__:[47]})])]}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",he,[(0,n.Lk)("div",be,[(0,n.Lk)("div",Le,[t[51]||(t[51]=(0,n.Lk)("div",{class:"title-icon submission-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2a7.63 7.63 0 0 1-2.9-6.1V351c0-6.8 7.9-10.5 13.1-6.1l192 161c3.9 3.2 3.9 9.1 0 12.2zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"})])],-1)),t[52]||(t[52]=(0,n.Lk)("span",null,"漏洞提交操作",-1)),(0,n.bF)(ni,{type:"primary",size:"mini"},{default:(0,n.k6)(function(){return t[50]||(t[50]=[(0,n.eW)("已提交")])}),_:1,__:[50]})]),(0,n.Lk)("div",ge,[(0,n.Lk)("div",_e,[(0,n.Lk)("div",ye,[t[54]||(t[54]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",we,[(0,n.Lk)("span",Se,(0,s.v_)($t.selectedSubmission.submissionTransactionHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[5]||(t[5]=function(e){return $t.copyToClipboard($t.selectedSubmission.submissionTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[53]||(t[53]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[53]})])])]),(0,n.Lk)("div",Ce,[(0,n.Lk)("div",xe,[t[56]||(t[56]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",Fe,[(0,n.Lk)("span",ze,(0,s.v_)($t.selectedSubmission.submissionBlockHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[6]||(t[6]=function(e){return $t.copyToClipboard($t.selectedSubmission.submissionBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[55]||(t[55]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[55]})])])]),(0,n.Lk)("div",Te,[(0,n.Lk)("div",Ve,[t[57]||(t[57]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",He,(0,s.v_)($t.formatTimestamp($t.selectedSubmission.submissionBlockchainTimestamp)),1)])])])]),t[58]||(t[58]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("div",{class:"notice-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"此漏洞提交信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,n.Q3)("",!0),$t.hasApprovalBlockchainInfo($t.selectedSubmission)?((0,n.uX)(),(0,n.Wv)(ui,{key:1,class:"detail-card approval-blockchain-info-card",shadow:"never"},{header:(0,n.k6)(function(){return[(0,n.Lk)("div",We,[t[61]||(t[61]=(0,n.Lk)("div",{class:"blockchain-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,n.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),t[62]||(t[62]=(0,n.Lk)("span",null,"企业审批记录 - 区块链验证",-1)),(0,n.bF)(ni,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return[((0,n.uX)(),(0,n.CE)("svg",je,t[59]||(t[59]=[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),t[60]||(t[60]=(0,n.eW)(" 链上可验证 "))]}),_:1,__:[60]})])]}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",Be,[(0,n.Lk)("div",Me,[(0,n.Lk)("div",Ae,[t[63]||(t[63]=(0,n.Lk)("div",{class:"title-icon approval-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-55.9 718.9L205.3 532.1c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L456 692.2 773.3 374.9c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3L501.4 737.4c-6.2 6.2-14.4 9.4-22.6 9.4s-16.4-3.1-22.6-9.4z"})])],-1)),t[64]||(t[64]=(0,n.Lk)("span",null,"企业审批操作",-1)),(0,n.bF)(ni,{type:$t.getApprovalStatusType($t.selectedSubmission),size:"mini"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getApprovalStatusText($t.selectedSubmission)),1)]}),_:1},8,["type"])]),(0,n.Lk)("div",Re,[(0,n.Lk)("div",Xe,[(0,n.Lk)("div",De,[t[66]||(t[66]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",Ee,[(0,n.Lk)("span",Qe,(0,s.v_)($t.selectedSubmission.approvalTransactionHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[7]||(t[7]=function(e){return $t.copyToClipboard($t.selectedSubmission.approvalTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[65]||(t[65]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[65]})])])]),(0,n.Lk)("div",Ke,[(0,n.Lk)("div",Ie,[t[68]||(t[68]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",Ne,[(0,n.Lk)("span",Ue,(0,s.v_)($t.selectedSubmission.approvalBlockHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[8]||(t[8]=function(e){return $t.copyToClipboard($t.selectedSubmission.approvalBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[67]||(t[67]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[67]})])])]),(0,n.Lk)("div",Pe,[(0,n.Lk)("div",qe,[t[69]||(t[69]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",Ye,(0,s.v_)($t.formatTimestamp($t.selectedSubmission.approvalBlockchainTimestamp)),1)])])])]),t[70]||(t[70]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("div",{class:"notice-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"此企业审批信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,n.Q3)("",!0),$t.hasAdminReviewBlockchainInfo($t.selectedSubmission)?((0,n.uX)(),(0,n.Wv)(ui,{key:2,class:"detail-card admin-review-blockchain-card",shadow:"never"},{header:(0,n.k6)(function(){return[(0,n.Lk)("div",Oe,[t[73]||(t[73]=(0,n.Lk)("div",{class:"blockchain-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,n.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),t[74]||(t[74]=(0,n.Lk)("span",null,"管理员审核记录 - 区块链验证",-1)),(0,n.bF)(ni,{type:$t.getAdminReviewStatusType($t.selectedSubmission),size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return[((0,n.uX)(),(0,n.CE)("svg",Je,t[71]||(t[71]=[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),t[72]||(t[72]=(0,n.eW)(" 链上可验证 "))]}),_:1,__:[72]},8,["type"])])]}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",Ge,[(0,n.Lk)("div",Ze,[(0,n.Lk)("div",$e,[t[75]||(t[75]=(0,n.Lk)("div",{class:"info-label"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-55.9 718.9L205.3 532.1c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L456 692.2 773.3 374.9c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3L501.4 737.4c-6.2 6.2-14.4 9.4-22.6 9.4s-16.4-3.1-22.6-9.4z"})]),(0,n.Lk)("span",null,"审核结果")],-1)),(0,n.Lk)("div",et,[(0,n.bF)(ni,{type:$t.getAdminReviewStatusType($t.selectedSubmission),size:"small"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)($t.getAdminReviewStatusText($t.selectedSubmission)),1)]}),_:1},8,["type"])])]),(0,n.Lk)("div",tt,[t[77]||(t[77]=(0,n.Lk)("div",{class:"info-label"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})]),(0,n.Lk)("span",null,"交易哈希")],-1)),(0,n.Lk)("div",it,[(0,n.Lk)("span",nt,(0,s.v_)($t.selectedSubmission.adminReviewTransactionHash),1),(0,n.bF)(si,{type:"text",size:"mini",class:"copy-btn",onClick:t[9]||(t[9]=function(e){return $t.copyToClipboard($t.selectedSubmission.adminReviewTransactionHash,"管理员审核交易哈希")})},{default:(0,n.k6)(function(){return t[76]||(t[76]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[76]})])]),(0,n.Lk)("div",st,[t[79]||(t[79]=(0,n.Lk)("div",{class:"info-label"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})]),(0,n.Lk)("span",null,"区块哈希")],-1)),(0,n.Lk)("div",at,[(0,n.Lk)("span",lt,(0,s.v_)($t.selectedSubmission.adminReviewBlockHash),1),(0,n.bF)(si,{type:"text",size:"mini",class:"copy-btn",onClick:t[10]||(t[10]=function(e){return $t.copyToClipboard($t.selectedSubmission.adminReviewBlockHash,"管理员审核区块哈希")})},{default:(0,n.k6)(function(){return t[78]||(t[78]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[78]})])]),(0,n.Lk)("div",ct,[t[80]||(t[80]=(0,n.Lk)("div",{class:"info-label"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 768c-176.7 0-320-143.3-320-320s143.3-320 320-320 320 143.3 320 320-143.3 320-320 320zm0-568c-22.1 0-40 17.9-40 40v240c0 22.1 17.9 40 40 40s40-17.9 40-40V304c0-22.1-17.9-40-40-40z"})]),(0,n.Lk)("span",null,"记录时间")],-1)),(0,n.Lk)("div",ot,(0,s.v_)($t.formatTimestamp($t.selectedSubmission.adminReviewBlockchainTimestamp)),1)])]),t[81]||(t[81]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("div",{class:"notice-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,n.Lk)("div",{class:"notice-text"}," 此管理员审核信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。 ")],-1))])]}),_:1})):(0,n.Q3)("",!0),(0,n.bF)(ui,{class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[82]||(t[82]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-document"}),(0,n.Lk)("span",null,"漏洞描述")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",rt,(0,s.v_)($t.selectedSubmission.description||"暂无描述"),1)]}),_:1}),$t.selectedSubmission.urls&&$t.selectedSubmission.urls.length>0?((0,n.uX)(),(0,n.Wv)(ui,{key:3,class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[83]||(t[83]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-link"}),(0,n.Lk)("span",null,"相关URL")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",ut,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)($t.selectedSubmission.urls,function(e,i){return(0,n.uX)(),(0,n.CE)("div",{key:i,class:"url-item"},[(0,n.Lk)("span",dt,(0,s.v_)(e),1),(0,n.bF)(si,{size:"mini",type:"text",onClick:function(t){return $t.copyToClipboard(e)},class:"copy-btn"},{default:(0,n.k6)(function(){return t[84]||(t[84]=[(0,n.Lk)("i",{class:"el-icon-document-copy"},null,-1),(0,n.eW)(" 复制 ")])}),_:2,__:[84]},1032,["onClick"])])}),128))])]}),_:1})):(0,n.Q3)("",!0),$t.selectedSubmission.domain||$t.selectedSubmission.urls&&$t.selectedSubmission.urls.length>0?((0,n.uX)(),(0,n.Wv)(ui,{key:4,class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[85]||(t[85]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-connection"}),(0,n.Lk)("span",null,"相关链接")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",vt,[$t.selectedSubmission.domain?((0,n.uX)(),(0,n.CE)("div",kt,[t[86]||(t[86]=(0,n.Lk)("span",{class:"link-label"},"主域名：",-1)),(0,n.Lk)("span",ft,(0,s.v_)($t.selectedSubmission.domain),1)])):(0,n.Q3)("",!0),$t.selectedSubmission.urls&&$t.selectedSubmission.urls.length>0?((0,n.uX)(),(0,n.CE)("div",mt,[t[87]||(t[87]=(0,n.Lk)("span",{class:"link-label"},"涉及页面：",-1)),(0,n.Lk)("div",pt,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)($t.selectedSubmission.urls,function(e,t){return(0,n.uX)(),(0,n.CE)("div",{key:t,class:"link-url"},[(0,n.Lk)("span",ht,(0,s.v_)(e),1)])}),128))])])):(0,n.Q3)("",!0)])]}),_:1})):(0,n.Q3)("",!0),$t.selectedSubmission.reproductionSteps?((0,n.uX)(),(0,n.Wv)(ui,{key:5,class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[88]||(t[88]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-s-order"}),(0,n.Lk)("span",null,"复现步骤")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",bt,(0,s.v_)($t.selectedSubmission.reproductionSteps),1)]}),_:1})):(0,n.Q3)("",!0),$t.selectedSubmission.remediation?((0,n.uX)(),(0,n.Wv)(ui,{key:6,class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[89]||(t[89]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-s-tools"}),(0,n.Lk)("span",null,"修复建议")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",Lt,(0,s.v_)($t.selectedSubmission.remediation),1)]}),_:1})):(0,n.Q3)("",!0),$t.selectedSubmission.reviewComment?((0,n.uX)(),(0,n.Wv)(ui,{key:7,class:"detail-card",shadow:"never"},{header:(0,n.k6)(function(){return t[90]||(t[90]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-chat-line-square"}),(0,n.Lk)("span",null,"审核意见")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",gt,(0,s.v_)($t.selectedSubmission.reviewComment),1)]}),_:1})):(0,n.Q3)("",!0),$t.hasBlockchainInfo($t.selectedSubmission)?((0,n.uX)(),(0,n.Wv)(ui,{key:8,class:"detail-card blockchain-info-card",shadow:"never"},{header:(0,n.k6)(function(){return[(0,n.Lk)("div",_t,[t[93]||(t[93]=(0,n.Lk)("div",{class:"blockchain-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,n.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),t[94]||(t[94]=(0,n.Lk)("span",null,"白帽确认信息 - 区块链记录",-1)),(0,n.bF)(ni,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,n.k6)(function(){return[((0,n.uX)(),(0,n.CE)("svg",yt,t[91]||(t[91]=[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),t[92]||(t[92]=(0,n.eW)(" 链上可验证 "))]}),_:1,__:[92]})])]}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",wt,[$t.selectedSubmission.confirmationTransactionHash?((0,n.uX)(),(0,n.CE)("div",St,[(0,n.Lk)("div",Ct,[t[96]||(t[96]=(0,n.Lk)("div",{class:"title-icon confirm-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2l-359 493.4-183.1-252.4c-6-8.2-15.8-12.2-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"})])],-1)),t[97]||(t[97]=(0,n.Lk)("span",null,"用户确认操作",-1)),(0,n.bF)(ni,{type:"success",size:"mini"},{default:(0,n.k6)(function(){return t[95]||(t[95]=[(0,n.eW)("已确认")])}),_:1,__:[95]})]),(0,n.Lk)("div",xt,[(0,n.Lk)("div",Ft,[(0,n.Lk)("div",zt,[t[99]||(t[99]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",Tt,[(0,n.Lk)("span",Vt,(0,s.v_)($t.selectedSubmission.confirmationTransactionHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[11]||(t[11]=function(e){return $t.copyToClipboard($t.selectedSubmission.confirmationTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[98]||(t[98]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[98]})])])]),(0,n.Lk)("div",Ht,[(0,n.Lk)("div",Wt,[t[101]||(t[101]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",jt,[(0,n.Lk)("span",Bt,(0,s.v_)($t.selectedSubmission.confirmationBlockHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[12]||(t[12]=function(e){return $t.copyToClipboard($t.selectedSubmission.confirmationBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[100]||(t[100]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[100]})])])]),(0,n.Lk)("div",Mt,[(0,n.Lk)("div",At,[t[102]||(t[102]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",Rt,(0,s.v_)($t.formatTimestamp($t.selectedSubmission.confirmationTimestamp)),1)])])])])):(0,n.Q3)("",!0),$t.selectedSubmission.disputeTransactionHash?((0,n.uX)(),(0,n.CE)("div",Xt,[(0,n.Lk)("div",Dt,[t[104]||(t[104]=(0,n.Lk)("div",{class:"title-icon dispute-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])],-1)),t[105]||(t[105]=(0,n.Lk)("span",null,"用户申诉操作",-1)),(0,n.bF)(ni,{type:"warning",size:"mini"},{default:(0,n.k6)(function(){return t[103]||(t[103]=[(0,n.eW)("已申诉")])}),_:1,__:[103]})]),(0,n.Lk)("div",Et,[(0,n.Lk)("div",Qt,[(0,n.Lk)("div",Kt,[t[107]||(t[107]=(0,n.Lk)("label",null,"交易哈希",-1)),(0,n.Lk)("div",It,[(0,n.Lk)("span",Nt,(0,s.v_)($t.selectedSubmission.disputeTransactionHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[13]||(t[13]=function(e){return $t.copyToClipboard($t.selectedSubmission.disputeTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[106]||(t[106]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[106]})])])]),(0,n.Lk)("div",Ut,[(0,n.Lk)("div",Pt,[t[109]||(t[109]=(0,n.Lk)("label",null,"区块哈希",-1)),(0,n.Lk)("div",qt,[(0,n.Lk)("span",Yt,(0,s.v_)($t.selectedSubmission.disputeBlockHash),1),(0,n.bF)(si,{type:"text",size:"small",onClick:t[14]||(t[14]=function(e){return $t.copyToClipboard($t.selectedSubmission.disputeBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,n.k6)(function(){return t[108]||(t[108]=[(0,n.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[108]})])])]),(0,n.Lk)("div",Ot,[(0,n.Lk)("div",Jt,[t[110]||(t[110]=(0,n.Lk)("label",null,"记录时间",-1)),(0,n.Lk)("div",Gt,(0,s.v_)($t.formatTimestamp($t.selectedSubmission.disputeTimestamp)),1)])])])])):(0,n.Q3)("",!0),t[111]||(t[111]=(0,n.Lk)("div",{class:"blockchain-notice"},[(0,n.Lk)("div",{class:"notice-icon"},[(0,n.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,n.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,n.Lk)("div",{class:"notice-text"},[(0,n.Lk)("p",null,"以上信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0)]}),_:1},8,["modelValue","title","before-close"]),(0,n.bF)(fi,{modelValue:$t.confirmDialogVisible,"onUpdate:modelValue":t[16]||(t[16]=function(e){return $t.confirmDialogVisible=e}),vulnerability:$t.selectedVulnerability,onConfirmed:$t.handleConfirmed},null,8,["modelValue","vulnerability","onConfirmed"])])}var ei=i(24059),ti=i(698),ii=(i(76918),i(28706),i(23288),i(79432),i(26099),i(27495),i(38781),i(68156),i(25440),i(42762),i(12040)),ni=i(39053),si=i(18057),ai=i(30578),li=i(36149),ci=i(80401),oi=(i(76031),i(95113)),ri={key:0,class:"confirm-dialog"},ui={class:"reward-amount"},di={class:"review-content"},vi={key:0,class:"dispute-reason"},ki={class:"dialog-footer"};const fi={__name:"VulnerabilityConfirmDialog",props:{modelValue:{type:Boolean,default:!1},vulnerability:{type:Object,default:null}},emits:["update:modelValue","confirmed"],setup:function(e,t){var i=t.emit,a=e,l=i,c=(0,ii.KR)(!1),o=(0,ii.KR)("confirm"),r=(0,ii.KR)(""),u=(0,ii.KR)(!1),d=(0,ii.KR)(""),v=(0,ii.KR)(null),k=(0,n.EW)(function(){var e;if(null===(e=a.vulnerability)||void 0===e||!e.userConfirmDeadline)return!1;var t=new Date(a.vulnerability.userConfirmDeadline),i=new Date,n=t-i;return n>0&&n<864e5});(0,n.wB)(function(){return a.modelValue},function(e){c.value=e,e&&a.vulnerability&&(o.value="confirm",r.value="",p())}),(0,n.wB)(c,function(e){l("update:modelValue",e),e||h()});var f=function(){c.value=!1},m=function(){var e=(0,ti.A)((0,ei.A)().m(function e(){var t,i,n,s,c;return(0,ei.A)().w(function(e){while(1)switch(e.n){case 0:if("dispute"!==o.value||r.value.trim()){e.n=1;break}return si.nk.warning("请输入争议理由"),e.a(2);case 1:return u.value=!0,e.p=2,t={action:o.value},"dispute"===o.value&&(t.disputeReason=r.value.trim()),e.n=3,oi.A.post("/vulnerability-workflow/user-confirm/".concat(a.vulnerability.id),t);case 3:i=e.v,i.data.success&&(n=i.data.message,i.data.data.blockchain&&(n+="\n区块链交易哈希: ".concat(i.data.data.blockchain.transactionHash)),si.nk.success(n),l("confirmed"),f()),e.n=5;break;case 4:e.p=4,c=e.v,console.error("提交失败:",c),si.nk.error((null===(s=c.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.message)||"提交失败");case 5:return e.p=5,u.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[2,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),p=function(){var e;if(null!==(e=a.vulnerability)&&void 0!==e&&e.userConfirmDeadline){var t=function(){var e=new Date(a.vulnerability.userConfirmDeadline),t=new Date,i=e-t;if(i<=0)return d.value="已超时",void h();var n=Math.floor(i/864e5),s=Math.floor(i%864e5/36e5),l=Math.floor(i%36e5/6e4);d.value=n>0?"".concat(n,"天").concat(s,"小时"):s>0?"".concat(s,"小时").concat(l,"分钟"):"".concat(l,"分钟")};t(),v.value=setInterval(t,6e4)}},h=function(){v.value&&(clearInterval(v.value),v.value=null)},b=function(e){var t={critical:"danger",high:"warning",medium:"primary",low:"info",info:"success"};return t[e]||"info"},L=function(e){var t={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"信息"};return t[e]||e},g=function(e){return e?new Date(e).toLocaleString("zh-CN"):"-"};return(0,n.hi)(function(){h()}),function(t,i){var a=(0,n.g2)("el-descriptions-item"),l=(0,n.g2)("el-tag"),v=(0,n.g2)("el-descriptions"),p=(0,n.g2)("el-card"),h=(0,n.g2)("el-radio"),_=(0,n.g2)("el-radio-group"),y=(0,n.g2)("el-input"),w=(0,n.g2)("el-form-item"),S=(0,n.g2)("el-alert"),C=(0,n.g2)("el-button"),x=(0,n.g2)("el-dialog");return(0,n.uX)(),(0,n.Wv)(x,{modelValue:c.value,"onUpdate:modelValue":i[2]||(i[2]=function(e){return c.value=e}),title:"确认企业审核结果",width:"60%","close-on-click-modal":!1,onClose:f},{footer:(0,n.k6)(function(){return[(0,n.Lk)("div",ki,[(0,n.bF)(C,{onClick:f},{default:(0,n.k6)(function(){return i[8]||(i[8]=[(0,n.eW)("取消")])}),_:1,__:[8]}),(0,n.bF)(C,{type:"primary",onClick:m,loading:u.value,disabled:"dispute"===o.value&&!r.value.trim()},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)("confirm"===o.value?"确认并获得奖励":"提交争议"),1)]}),_:1},8,["loading","disabled"])])]}),default:(0,n.k6)(function(){return[e.vulnerability?((0,n.uX)(),(0,n.CE)("div",ri,[(0,n.bF)(p,{class:"info-card",shadow:"never"},{header:(0,n.k6)(function(){return i[3]||(i[3]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-document"}),(0,n.Lk)("span",null,"漏洞信息")],-1)])}),default:(0,n.k6)(function(){return[(0,n.bF)(v,{column:2,border:""},{default:(0,n.k6)(function(){return[(0,n.bF)(a,{label:"漏洞标题"},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)(e.vulnerability.title),1)]}),_:1}),(0,n.bF)(a,{label:"严重程度"},{default:(0,n.k6)(function(){return[(0,n.bF)(l,{type:b(e.vulnerability.severity)},{default:(0,n.k6)(function(){return[(0,n.eW)((0,s.v_)(L(e.vulnerability.severity)),1)]}),_:1},8,["type"])]}),_:1}),(0,n.bF)(a,{label:"奖励金额"},{default:(0,n.k6)(function(){return[(0,n.Lk)("span",ui,"¥"+(0,s.v_)(e.vulnerability.reward||0),1)]}),_:1}),(0,n.bF)(a,{label:"确认截止时间"},{default:(0,n.k6)(function(){return[(0,n.Lk)("span",{class:(0,s.C4)({"deadline-warning":k.value})},(0,s.v_)(g(e.vulnerability.userConfirmDeadline)),3)]}),_:1})]}),_:1})]}),_:1}),(0,n.bF)(p,{class:"review-card",shadow:"never"},{header:(0,n.k6)(function(){return i[4]||(i[4]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-chat-dot-round"}),(0,n.Lk)("span",null,"企业审核意见")],-1)])}),default:(0,n.k6)(function(){return[(0,n.Lk)("div",di,(0,s.v_)(e.vulnerability.enterpriseReviewComment||"企业未提供审核意见"),1)]}),_:1}),(0,n.bF)(p,{class:"action-card",shadow:"never"},{header:(0,n.k6)(function(){return i[5]||(i[5]=[(0,n.Lk)("div",{class:"card-header"},[(0,n.Lk)("i",{class:"el-icon-check"}),(0,n.Lk)("span",null,"请选择您的操作")],-1)])}),default:(0,n.k6)(function(){return[(0,n.bF)(_,{modelValue:o.value,"onUpdate:modelValue":i[0]||(i[0]=function(e){return o.value=e}),class:"action-group"},{default:(0,n.k6)(function(){return[(0,n.bF)(h,{label:"confirm",class:"action-option"},{default:(0,n.k6)(function(){return i[6]||(i[6]=[(0,n.Lk)("div",{class:"option-content"},[(0,n.Lk)("div",{class:"option-title"},[(0,n.Lk)("i",{class:"el-icon-circle-check",style:{color:"#67c23a"}}),(0,n.eW)(" 确认审核结果 ")]),(0,n.Lk)("div",{class:"option-desc"}," 同意企业的审核结果，立即获得奖励并生成证书 ")],-1)])}),_:1,__:[6]}),(0,n.bF)(h,{label:"dispute",class:"action-option"},{default:(0,n.k6)(function(){return i[7]||(i[7]=[(0,n.Lk)("div",{class:"option-content"},[(0,n.Lk)("div",{class:"option-title"},[(0,n.Lk)("i",{class:"el-icon-warning",style:{color:"#e6a23c"}}),(0,n.eW)(" 提出争议 ")]),(0,n.Lk)("div",{class:"option-desc"}," 对企业的审核结果有异议，提交给管理员进行第三方审核 ")],-1)])}),_:1,__:[7]})]}),_:1},8,["modelValue"]),"dispute"===o.value?((0,n.uX)(),(0,n.CE)("div",vi,[(0,n.bF)(w,{label:"争议理由",required:""},{default:(0,n.k6)(function(){return[(0,n.bF)(y,{modelValue:r.value,"onUpdate:modelValue":i[1]||(i[1]=function(e){return r.value=e}),type:"textarea",rows:4,placeholder:"请详细说明您对企业审核结果的异议...",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]}),_:1})])):(0,n.Q3)("",!0)]}),_:1}),"confirm"===o.value?((0,n.uX)(),(0,n.Wv)(S,{key:0,title:"确认后将立即处理奖励发放和证书生成，请仔细核对信息",type:"success",closable:!1,"show-icon":""})):(0,n.Q3)("",!0),"dispute"===o.value?((0,n.uX)(),(0,n.Wv)(S,{key:1,title:"提交争议后将由管理员进行第三方审核，审核结果为最终结果",type:"warning",closable:!1,"show-icon":""})):(0,n.Q3)("",!0),k.value?((0,n.uX)(),(0,n.Wv)(S,{key:2,title:"距离自动确认还有 ".concat(d.value,"，请及时处理"),type:"error",closable:!1,"show-icon":""},null,8,["title"])):(0,n.Q3)("",!0)])):(0,n.Q3)("",!0)]}),_:1},8,["modelValue"])}}};var mi=i(1169);const pi=(0,mi.A)(fi,[["__scopeId","data-v-705d046b"]]),hi=pi,bi={name:"ProjectDetail",components:{TheHeader:ci.A,VulnerabilityConfirmDialog:hi},setup:function(){var e=(0,ni.lq)(),t=(0,ii.KR)(!0),i=(0,ii.KR)(null),s=(0,ii.KR)([]),a=(0,ii.KR)(!1),l=(0,ii.KR)(0),c=(0,ii.KR)(1),o=(0,ii.KR)(10),r=(0,ii.KR)(""),u=(0,ii.KR)(""),d=(0,ii.KR)(!1),v=(0,ii.KR)(null),k=(0,ii.KR)(!1),f=(0,ii.KR)(null),m=(0,ii.KR)(JSON.parse(localStorage.getItem("user")||"null")),p=function(){var n=(0,ti.A)((0,ei.A)().m(function n(){var s,a;return(0,ei.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,li.A.get("/security-tasks/".concat(e.params.id));case 1:s=n.v,s.data.success?i.value=s.data.data:si.nk.error(s.data.message||"获取项目详情失败"),n.n=3;break;case 2:n.p=2,a=n.v,console.error("获取项目详情失败:",a),si.nk.error("获取项目详情失败，请稍后重试");case 3:return n.p=3,t.value=!1,n.f(3);case 4:return n.a(2)}},n,null,[[0,2,3,4]])}));return function(){return n.apply(this,arguments)}}(),h=function(e){e.target.src="/logo.png"},b=function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},L=function(){var t=(0,ti.A)((0,ei.A)().m(function t(){var i,n;return(0,ei.A)().w(function(t){while(1)switch(t.n){case 0:return a.value=!0,t.p=1,t.n=2,li.A.get("/security-tasks/".concat(e.params.id,"/submissions"),{params:{page:c.value,pageSize:o.value,status:r.value,severity:u.value}});case 2:i=t.v,i.data.success?(s.value=i.data.data.submissions,l.value=i.data.data.total):si.nk.error(i.data.message||"获取提交记录失败"),t.n=4;break;case 3:t.p=3,n=t.v,console.error("获取提交记录失败:",n),si.nk.error("获取提交记录失败，请稍后重试");case 4:return t.p=4,a.value=!1,t.f(4);case 5:return t.a(2)}},t,null,[[1,3,4,5]])}));return function(){return t.apply(this,arguments)}}(),g=function(e){v.value=e,d.value=!0},_=function(){d.value=!1,v.value=null},y=function(e){c.value=e,L()},w=function(){var e=(0,ti.A)((0,ei.A)().m(function e(t){var i;return(0,ei.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,navigator.clipboard.writeText(t);case 1:si.nk.success("已复制到剪贴板"),e.n=3;break;case 2:e.p=2,e.v,i=document.createElement("textarea"),i.value=t,document.body.appendChild(i),i.select();try{document.execCommand("copy"),si.nk.success("已复制到剪贴板")}catch(n){si.nk.error("复制失败，请手动复制")}document.body.removeChild(i);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),S=function(e){return!!e&&(!(!e.confirmationTransactionHash&&!e.disputeTransactionHash)||!!e.transactionHash)},C=function(e){return!!e&&!!e.submissionTransactionHash},x=function(e){return!!e&&!!e.approvalTransactionHash},F=function(e){return!!e&&!!e.adminReviewTransactionHash},z=function(e){return e&&e.status?"enterprise_confirmed"===e.status?"success":"enterprise_rejected"===e.status?"danger":"info":""},T=function(e){return e&&e.status?"enterprise_confirmed"===e.status?"已确认":"enterprise_rejected"===e.status?"已拒绝":"待审批":"未审批"},V=function(e){return e&&e.status?"admin_confirmed"===e.status?"success":"admin_rejected"===e.status?"danger":"info":""},H=function(e){return e&&e.status?"admin_confirmed"===e.status?"已通过":"admin_rejected"===e.status?"已拒绝":"待审核":"未审核"},W=function(e){if(!e)return"未知";try{var t=new Date(1e3*e);return t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(i){return"格式错误"}},j=function(e){f.value=e,k.value=!0},B=function(){var e=(0,ti.A)((0,ei.A)().m(function e(t,i){var n,s,a,l,c,o,r,u,d,v;return(0,ei.A)().w(function(e){while(1)switch(e.n){case 0:if("confirm"!==i){e.n=5;break}return e.p=1,e.n=2,li.A.post("/vulnerability-workflow/user-confirm/".concat(t.id),{action:"confirm"});case 2:n=e.v,n.data.success&&(s=n.data.message,n.data.data.blockchain&&(s+="\n区块链交易哈希: ".concat(n.data.data.blockchain.transactionHash)),si.nk.success(s),L()),e.n=4;break;case 3:e.p=3,d=e.v,console.error("确认失败:",d),si.nk.error((null===(a=d.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"确认失败");case 4:e.n=10;break;case 5:if("dispute"!==i){e.n=10;break}return e.p=6,e.n=7,ai.s.prompt("请详细说明您对企业审核结果的异议：","提交申诉",{confirmButtonText:"提交申诉",cancelButtonText:"取消",inputType:"textarea",inputValidator:function(e){return e&&""!==e.trim()?!(e.trim().length<10)||"申诉理由至少需要10个字符":"请输入申诉理由"}});case 7:return l=e.v,c=l.value,e.n=8,li.A.post("/vulnerability-workflow/user-confirm/".concat(t.id),{action:"dispute",disputeReason:c.trim()});case 8:o=e.v,o.data.success&&(r=o.data.message,o.data.data.blockchain&&(r+="\n区块链交易哈希: ".concat(o.data.data.blockchain.transactionHash)),si.nk.success(r),L()),e.n=10;break;case 9:e.p=9,v=e.v,"cancel"!==v&&(console.error("申诉失败:",v),si.nk.error((null===(u=v.response)||void 0===u||null===(u=u.data)||void 0===u?void 0:u.message)||"申诉失败"));case 10:return e.a(2)}},e,null,[[6,9],[1,3]])}));return function(t,i){return e.apply(this,arguments)}}(),M=function(){L()},A=function(e){var t=new Date(e),i=new Date,n=t-i;if(n<=0)return"已结束";var s=Math.floor(n/864e5),a=Math.floor(n%864e5/36e5);return s>0?"".concat(s,"天").concat(a,"小时"):"".concat(a,"小时")},R=function(e){var t={active:"进行中",upcoming:"即将开始",ended:"已结束",pending:"待企业审核",enterprise_confirmed:"待用户确认",enterprise_rejected:"企业拒绝",user_confirmed:"用户确认",dispute:"争议中",admin_confirmed:"管理员确认",admin_rejected:"管理员拒绝",auto_confirmed:"自动确认",fixed:"已修复",duplicate:"重复漏洞"};return t[e]||e},X=function(e){var t={active:"success",upcoming:"warning",ended:"info",pending:"warning",enterprise_confirmed:"primary",enterprise_rejected:"danger",user_confirmed:"success",dispute:"warning",admin_confirmed:"success",admin_rejected:"danger",auto_confirmed:"success",fixed:"info",duplicate:"info"};return t[e]||"info"},D=function(e){var t={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"信息"};return t[e]||e},E=function(e){var t={critical:"danger",high:"danger",medium:"warning",low:"success",info:"info"};return t[e]||"info"},Q=function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," ").concat(String(t.getHours()).padStart(2,"0"),":").concat(String(t.getMinutes()).padStart(2,"0"))},K=function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))},I=function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," ").concat(String(t.getHours()).padStart(2,"0"),":").concat(String(t.getMinutes()).padStart(2,"0"))},N=function(e){var t={critical:"严重",high:"高危",medium:"中危",low:"低危"};return t[e]||e},U=function(e){var t={pending:"待审核",confirmed:"已确认",rejected:"已拒绝",fixed:"已修复"};return t[e]||e},P=function(e){var t={pending:"warning",confirmed:"success",rejected:"danger",fixed:"info"};return t[e]||"info"};return(0,n.sV)(function(){p(),L()}),{loading:t,project:i,submissions:s,submissionsLoading:a,submissionTotal:l,submissionCurrentPage:c,submissionPageSize:o,submissionFilter:r,severityFilter:u,submissionDetailVisible:d,selectedSubmission:v,fetchSubmissions:L,viewSubmissionDetail:g,closeSubmissionDetail:_,handleSubmissionPageChange:y,copyToClipboard:w,hasBlockchainInfo:S,hasSubmissionBlockchainInfo:C,hasApprovalBlockchainInfo:x,hasAdminReviewBlockchainInfo:F,getApprovalStatusType:z,getApprovalStatusText:T,getAdminReviewStatusType:V,getAdminReviewStatusText:H,formatTimestamp:W,confirmDialogVisible:k,selectedVulnerability:f,currentUser:m,showConfirmDialog:j,handleUserAction:B,handleConfirmed:M,handleImageError:h,formatNumber:b,formatTimeLeft:A,formatDate:K,formatTime:I,formatDateTime:Q,getStatusText:R,getStatusType:X,getSeverityText:D,getSeverityType:E,getLevelText:N,getSubmissionStatusText:U,getSubmissionStatusType:P}}},Li=(0,mi.A)(bi,[["render",$t],["__scopeId","data-v-03c77d18"]]),gi=Li}}]);