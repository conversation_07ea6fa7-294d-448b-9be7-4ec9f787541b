"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[304],{48077:(e,a,r)=>{r.d(a,{A:()=>u});var n=r(95976),s=r(10160);const o={__name:"CopyrightFooter",props:{isAdmin:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!1}},setup:function(e){return function(a,r){return(0,n.uX)(),(0,n.CE)("div",{class:(0,s.C4)(["copyright-footer",{"admin-style":e.isAdmin,"fixed-style":e.isFixed}])},r[0]||(r[0]=[(0,n.Fv)('<div class="footer-content" data-v-7e9937ff><p class="copyright" data-v-7e9937ff>Copyright © 2025 衍盾星云 版权所有</p><div class="footer-links" data-v-7e9937ff><a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" data-v-7e9937ff> 渝ICP备2025053738号-1 </a><span class="separator" data-v-7e9937ff>|</span><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50011702501094" target="_blank" rel="noopener noreferrer" data-v-7e9937ff><img src="/assets/images/备案图标.png" alt="公安备案" class="beian-icon" data-v-7e9937ff> 渝公网安备50011702501094号 </a></div></div>',1)]),2)}}};var t=r(1169);const l=(0,t.A)(o,[["__scopeId","data-v-7e9937ff"]]),u=l},96304:(e,a,r)=>{r.r(a),r.d(a,{default:()=>_});var n=r(24059),s=r(698),o=(r(44114),r(95976)),t=r(10160),l=r(29746),u=r(12040),i=r(39053),d=r(18057),c=r(36149),f=r(20907),p=r(48077),g={class:"login-card"},m={class:"login-footer"},v="/assets/images/bg1.png";const b={__name:"AdminLogin",setup:function(e){var a=(0,i.rd)(),r=(0,u.KR)(),b=(0,u.KR)(!1),k=(0,u.Kh)({username:"",password:""}),h={username:[{required:!0,message:"请输入管理员账号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},_=function(){var e=(0,s.A)((0,n.A)().m(function e(){var s,o,t;return(0,n.A)().w(function(e){while(1)switch(e.n){case 0:if(r.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,r.value.validate();case 2:return b.value=!0,e.n=3,c.A.post("/auth/admin-login",{username:k.username,password:k.password});case 3:s=e.v,s.data.success?(f.A.saveLoginInfo(s.data.token,s.data.user,!0),d.nk.success("登录成功！"),a.push("/admin/dashboard")):d.nk.error(s.data.message||"登录失败"),e.n=5;break;case 4:e.p=4,t=e.v,console.error("管理员登录失败:",t),d.nk.error((null===(o=t.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.message)||"登录失败，请稍后重试");case 5:return e.p=5,b.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}();return function(e,a){var n=(0,o.g2)("el-input"),s=(0,o.g2)("el-form-item"),u=(0,o.g2)("el-button"),i=(0,o.g2)("el-form"),d=(0,o.g2)("router-link");return(0,o.uX)(),(0,o.CE)("div",{class:"admin-login-container",style:(0,t.Tr)({backgroundImage:"url(".concat(v,")")})},[(0,o.Lk)("div",g,[a[4]||(a[4]=(0,o.Lk)("div",{class:"login-header"},[(0,o.Lk)("div",{class:"logo"},[(0,o.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云",class:"login-logo"}),(0,o.Lk)("h1",null,"管理员登录")])],-1)),(0,o.bF)(i,{ref_key:"loginFormRef",ref:r,model:k,rules:h,class:"login-form",onSubmit:(0,l.D$)(_,["prevent"])},{default:(0,o.k6)(function(){return[(0,o.bF)(s,{prop:"username"},{default:(0,o.k6)(function(){return[(0,o.bF)(n,{modelValue:k.username,"onUpdate:modelValue":a[0]||(a[0]=function(e){return k.username=e}),placeholder:"请输入管理员账号",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]}),_:1}),(0,o.bF)(s,{prop:"password"},{default:(0,o.k6)(function(){return[(0,o.bF)(n,{modelValue:k.password,"onUpdate:modelValue":a[1]||(a[1]=function(e){return k.password=e}),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:(0,l.jR)(_,["enter"])},null,8,["modelValue"])]}),_:1}),(0,o.bF)(s,null,{default:(0,o.k6)(function(){return[(0,o.bF)(u,{type:"primary",size:"large",class:"login-btn",loading:b.value,onClick:_},{default:(0,o.k6)(function(){return[(0,o.eW)((0,t.v_)(b.value?"登录中...":"登录"),1)]}),_:1},8,["loading"])]}),_:1})]}),_:1},8,["model"]),(0,o.Lk)("div",m,[a[3]||(a[3]=(0,o.Lk)("p",null,"仅限管理员账户登录",-1)),(0,o.bF)(d,{to:"/",class:"back-home"},{default:(0,o.k6)(function(){return a[2]||(a[2]=[(0,o.eW)("返回首页")])}),_:1,__:[2]})])]),(0,o.bF)(p.A,{"is-fixed":!0})],4)}}};var k=r(1169);const h=(0,k.A)(b,[["__scopeId","data-v-642d2c82"]]),_=h}}]);