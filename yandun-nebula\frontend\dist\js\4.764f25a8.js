"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[4],{49004:(e,t,n)=>{n.r(t),n.d(t,{default:()=>te});n(62010);var a=n(95976),l=n(29746),o=n(10160),r={class:"community-container"},u={class:"community-content"},s={class:"community-header"},i={class:"search-bar"},c={class:"community-main"},d={class:"sidebar"},g={class:"section"},v={class:"category-list"},p=["onClick"],f={class:"section"},k={class:"tag-cloud"},m={class:"main-content"},y={class:"filters"},h={class:"sort-options"},b=["onClick"],C={class:"post-list"},w={key:0,class:"empty-state"},L={key:1,class:"post-items"},_=["onClick"],F={class:"post-avatar"},P=["src","alt"],K={class:"post-content"},V={class:"post-title"},A={key:0,class:"post-badge sticky"},X={key:1,class:"post-badge highlight"},E={class:"post-category"},R={class:"post-tags"},I={class:"post-info"},W={class:"post-author"},D={class:"post-time"},S={class:"post-stats"},T={class:"stat-item"},z={class:"stat-item"},U={class:"stat-item"},M={key:2,class:"pagination"},q={class:"dialog-footer"};function x(e,t,x,H,Q,N){var O=(0,a.g2)("TheHeader"),j=(0,a.g2)("el-input"),B=(0,a.g2)("el-button"),Y=(0,a.g2)("el-tag"),G=(0,a.g2)("el-empty"),J=(0,a.g2)("el-pagination"),Z=(0,a.g2)("el-form-item"),$=(0,a.g2)("el-option"),ee=(0,a.g2)("el-select"),te=(0,a.g2)("el-form"),ne=(0,a.g2)("el-dialog"),ae=(0,a.g2)("AppFooter"),le=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(O),(0,a.Lk)("div",u,[(0,a.Lk)("div",s,[t[10]||(t[10]=(0,a.Lk)("h1",{class:"community-title"},"交流中心",-1)),(0,a.Lk)("div",i,[(0,a.bF)(j,{modelValue:H.searchKeyword,"onUpdate:modelValue":t[0]||(t[0]=function(e){return H.searchKeyword=e}),placeholder:"搜索帖子","prefix-icon":"el-icon-search",onKeyup:(0,l.jR)(H.searchPosts,["enter"])},null,8,["modelValue","onKeyup"])]),H.isLoggedIn?((0,a.uX)(),(0,a.Wv)(B,{key:0,type:"primary",onClick:H.showNewPostDialog},{default:(0,a.k6)(function(){return t[9]||(t[9]=[(0,a.eW)("发布帖子")])}),_:1,__:[9]},8,["onClick"])):(0,a.Q3)("",!0)]),(0,a.Lk)("div",c,[(0,a.Lk)("div",d,[(0,a.Lk)("div",g,[t[12]||(t[12]=(0,a.Lk)("h3",null,"分类",-1)),(0,a.Lk)("div",v,[(0,a.Lk)("div",{class:(0,o.C4)(["category-item",{active:!H.selectedCategory}]),onClick:t[1]||(t[1]=function(e){return H.selectCategory(null)})},t[11]||(t[11]=[(0,a.Lk)("i",{class:"el-icon-menu"},null,-1),(0,a.Lk)("span",null,"全部分类",-1)]),2),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.categories,function(e){return(0,a.uX)(),(0,a.CE)("div",{key:e.id,class:(0,o.C4)(["category-item",{active:H.selectedCategory===e.id}]),onClick:function(t){return H.selectCategory(e.id)}},[(0,a.Lk)("i",{class:(0,o.C4)(e.icon)},null,2),(0,a.Lk)("span",null,(0,o.v_)(e.name),1)],10,p)}),128))])]),(0,a.Lk)("div",f,[t[13]||(t[13]=(0,a.Lk)("h3",null,"热门标签",-1)),(0,a.Lk)("div",k,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.tags,function(e){return(0,a.uX)(),(0,a.Wv)(Y,{key:e.id,size:"small",effect:H.selectedTag===e.name?"dark":"plain",onClick:function(t){return H.selectTag(e.name)}},{default:(0,a.k6)(function(){return[(0,a.eW)((0,o.v_)(e.name)+" ("+(0,o.v_)(e.count)+") ",1)]}),_:2},1032,["effect","onClick"])}),128))])])]),(0,a.Lk)("div",m,[(0,a.Lk)("div",y,[(0,a.Lk)("div",h,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.sortOptions,function(e){return(0,a.uX)(),(0,a.CE)("span",{key:e.value,class:(0,o.C4)({active:H.currentSort===e.value}),onClick:function(t){return H.sortPosts(e.value)}},(0,o.v_)(e.label),11,b)}),128))])]),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",C,[0!==H.posts.length||H.loading?((0,a.uX)(),(0,a.CE)("div",L,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.posts,function(e){var l,r,u,s;return(0,a.uX)(),(0,a.CE)("div",{key:e.id,class:(0,o.C4)(["post-item",{"is-sticky":e.isSticky,"is-highlighted":e.isHighlighted}]),onClick:function(t){return H.viewPostDetail(e.id)}},[(0,a.Lk)("div",F,[(0,a.Lk)("img",{src:(null===(l=e.user)||void 0===l?void 0:l.avatar)||n(33153),alt:null===(r=e.user)||void 0===r?void 0:r.username},null,8,P)]),(0,a.Lk)("div",K,[(0,a.Lk)("div",V,[e.isSticky?((0,a.uX)(),(0,a.CE)("span",A,"置顶")):(0,a.Q3)("",!0),e.isHighlighted?((0,a.uX)(),(0,a.CE)("span",X,"精华")):(0,a.Q3)("",!0),(0,a.Lk)("h3",null,(0,o.v_)(e.title),1)]),(0,a.Lk)("div",E,[(0,a.bF)(Y,{size:"small",style:(0,o.Tr)({backgroundColor:H.getCategoryColor(null===(u=e.category)||void 0===u?void 0:u.id)})},{default:(0,a.k6)(function(){var t,n;return[(0,a.Lk)("i",{class:(0,o.C4)(null===(t=e.category)||void 0===t?void 0:t.icon)},null,2),(0,a.eW)(" "+(0,o.v_)(null===(n=e.category)||void 0===n?void 0:n.name),1)]}),_:2},1032,["style"]),(0,a.Lk)("div",R,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(e.tags,function(e){return(0,a.uX)(),(0,a.Wv)(Y,{key:e.id,size:"mini",effect:"plain"},{default:(0,a.k6)(function(){return[(0,a.eW)((0,o.v_)(e.name),1)]}),_:2},1024)}),128))])]),(0,a.Lk)("div",I,[(0,a.Lk)("span",W,(0,o.v_)(null===(s=e.user)||void 0===s?void 0:s.username),1),(0,a.Lk)("span",D,(0,o.v_)(H.formatTime(e.createdAt)),1)])]),(0,a.Lk)("div",S,[(0,a.Lk)("div",T,[t[14]||(t[14]=(0,a.Lk)("i",{class:"el-icon-view"},null,-1)),(0,a.Lk)("span",null,(0,o.v_)(e.views),1)]),(0,a.Lk)("div",z,[t[15]||(t[15]=(0,a.Lk)("i",{class:"el-icon-chat-dot-square"},null,-1)),(0,a.Lk)("span",null,(0,o.v_)(e.commentCount),1)]),(0,a.Lk)("div",U,[t[16]||(t[16]=(0,a.Lk)("i",{class:"el-icon-star-off"},null,-1)),(0,a.Lk)("span",null,(0,o.v_)(e.likes),1)])])],10,_)}),128))])):((0,a.uX)(),(0,a.CE)("div",w,[(0,a.bF)(G,{description:"暂无帖子"})])),H.totalPages>1?((0,a.uX)(),(0,a.CE)("div",M,[(0,a.bF)(J,{background:"",layout:"prev, pager, next","page-size":H.pageSize,total:H.total,modelValue:H.currentPage,"onUpdate:modelValue":t[2]||(t[2]=function(e){return H.currentPage=e}),onCurrentChange:H.handlePageChange},null,8,["page-size","total","modelValue","onCurrentChange"])])):(0,a.Q3)("",!0)])),[[le,H.loading]])])])]),(0,a.bF)(ne,{title:"发布新帖子",modelValue:H.newPostDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=function(e){return H.newPostDialogVisible=e}),width:"60%"},{footer:(0,a.k6)(function(){return[(0,a.Lk)("span",q,[(0,a.bF)(B,{onClick:t[7]||(t[7]=function(e){return H.newPostDialogVisible=!1})},{default:(0,a.k6)(function(){return t[17]||(t[17]=[(0,a.eW)("取 消")])}),_:1,__:[17]}),(0,a.bF)(B,{type:"primary",onClick:H.submitPost,loading:H.submitting},{default:(0,a.k6)(function(){return t[18]||(t[18]=[(0,a.eW)("发 布")])}),_:1,__:[18]},8,["onClick","loading"])])]}),default:(0,a.k6)(function(){return[(0,a.bF)(te,{model:H.newPost,ref:"newPostForm",rules:H.postRules,"label-width":"80px"},{default:(0,a.k6)(function(){return[(0,a.bF)(Z,{label:"标题",prop:"title"},{default:(0,a.k6)(function(){return[(0,a.bF)(j,{modelValue:H.newPost.title,"onUpdate:modelValue":t[3]||(t[3]=function(e){return H.newPost.title=e}),placeholder:"请输入帖子标题"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(Z,{label:"分类",prop:"categoryId"},{default:(0,a.k6)(function(){return[(0,a.bF)(ee,{modelValue:H.newPost.categoryId,"onUpdate:modelValue":t[4]||(t[4]=function(e){return H.newPost.categoryId=e}),placeholder:"请选择分类"},{default:(0,a.k6)(function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.categories,function(e){return(0,a.uX)(),(0,a.Wv)($,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])}),128))]}),_:1},8,["modelValue"])]}),_:1}),(0,a.bF)(Z,{label:"标签"},{default:(0,a.k6)(function(){return[(0,a.bF)(ee,{modelValue:H.newPost.tags,"onUpdate:modelValue":t[5]||(t[5]=function(e){return H.newPost.tags=e}),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或创建标签"},{default:(0,a.k6)(function(){return[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(H.tags,function(e){return(0,a.uX)(),(0,a.Wv)($,{key:e.id,label:e.name,value:e.name},null,8,["label","value"])}),128))]}),_:1},8,["modelValue"])]}),_:1}),(0,a.bF)(Z,{label:"内容",prop:"content"},{default:(0,a.k6)(function(){return[(0,a.bF)(j,{type:"textarea",modelValue:H.newPost.content,"onUpdate:modelValue":t[6]||(t[6]=function(e){return H.newPost.content=e}),rows:10,placeholder:"请输入帖子内容"},null,8,["modelValue"])]}),_:1})]}),_:1},8,["model","rules"])]}),_:1},8,["modelValue"]),(0,a.bF)(ae)])}var H=n(24059),Q=n(698),N=(n(28706),n(44114),n(23288),n(68156),n(12040)),O=n(39053),j=n(36149),B=n(80401),Y=n(74010),G=n(18057),J=n(20907);const Z={name:"Community",components:{TheHeader:B.A,AppFooter:Y.A},setup:function(){var e=(0,O.rd)(),t=(0,N.KR)(!1),n=(0,N.KR)(!1),l=(0,N.KR)([]),o=(0,N.KR)([]),r=(0,N.KR)([]),u=(0,N.KR)(null),s=(0,N.KR)(null),i=(0,N.KR)(""),c=(0,N.KR)("latest"),d=(0,N.KR)(1),g=(0,N.KR)(10),v=(0,N.KR)(0),p=(0,N.KR)(!1),f=(0,N.KR)(null),k=(0,a.EW)(function(){return J.A.isLoggedIn()}),m=(0,a.EW)(function(){return Math.ceil(v.value/g.value)}),y=[{label:"最新",value:"latest"},{label:"热门",value:"popular"},{label:"评论最多",value:"comments"},{label:"点赞最多",value:"likes"}],h=(0,N.Kh)({title:"",content:"",categoryId:"",tags:[]}),b={title:[{required:!0,message:"请输入帖子标题",trigger:"blur"},{min:5,max:100,message:"标题长度在5到100个字符之间",trigger:"blur"}],categoryId:[{required:!0,message:"请选择分类",trigger:"change"}],content:[{required:!0,message:"请输入帖子内容",trigger:"blur"},{min:10,message:"内容至少10个字符",trigger:"blur"}]},C=function(){var e=(0,Q.A)((0,H.A)().m(function e(){var n,a,o;return(0,H.A)().w(function(e){while(1)switch(e.n){case 0:return t.value=!0,e.p=1,n={page:d.value,limit:g.value,orderBy:c.value},u.value&&(n.category=u.value),s.value&&(n.tag=s.value),i.value&&(n.keyword=i.value),e.n=2,j.A.get("/community/posts",{params:n});case 2:a=e.v,l.value=a.data.data,v.value=a.data.pagination.total,e.n=4;break;case 3:e.p=3,o=e.v,console.error("获取帖子列表失败:",o),G.nk.error("获取帖子列表失败，请稍后再试");case 4:return e.p=4,t.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),w=function(){var e=(0,Q.A)((0,H.A)().m(function e(){var t,n;return(0,H.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,j.A.get("/community/categories");case 1:t=e.v,o.value=t.data.data,e.n=3;break;case 2:e.p=2,n=e.v,console.error("获取分类列表失败:",n),G.nk.error("获取分类列表失败，请稍后再试");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),L=function(){var e=(0,Q.A)((0,H.A)().m(function e(){var t,n;return(0,H.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,j.A.get("/community/tags",{params:{limit:20}});case 1:t=e.v,r.value=t.data.data,e.n=3;break;case 2:e.p=2,n=e.v,console.error("获取标签列表失败:",n),G.nk.error("获取标签列表失败，请稍后再试");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),_=function(e){u.value=e,d.value=1,C()},F=function(e){s.value===e?s.value=null:s.value=e,d.value=1,C()},P=function(){d.value=1,C()},K=function(e){c.value=e,d.value=1,C()},V=function(t){e.push("/community/posts/".concat(t))},A=function(e){d.value=e,C()},X=function(){if(!k.value)return G.nk.warning("请先登录后再发布帖子"),void e.push({path:"/login",query:{redirect:e.currentRoute.fullPath}});p.value=!0},E=function(){var t=(0,Q.A)((0,H.A)().m(function t(){var a,l,o,r;return(0,H.A)().w(function(t){while(1)switch(t.n){case 0:if(console.log("开始提交帖子..."),console.log("表单引用:",f.value),console.log("帖子数据:",h),console.log("用户登录状态:",k.value),f.value){t.n=1;break}return console.error("表单引用不存在"),G.nk.error("表单初始化失败，请刷新页面重试"),t.a(2);case 1:return t.p=1,console.log("开始表单验证..."),t.n=2,f.value.validate();case 2:if(a=t.v,console.log("表单验证结果:",a),!a){t.n=8;break}return n.value=!0,t.p=3,console.log("发送请求到服务器..."),t.n=4,j.A.post("/community/posts",h);case 4:l=t.v,console.log("服务器响应:",l),G.nk.success("帖子发布成功"),p.value=!1,h.title="",h.content="",h.categoryId="",h.tags=[],f.value.resetFields(),C(),l.data&&l.data.data&&l.data.data.id&&e.push("/community/posts/".concat(l.data.data.id)),t.n=6;break;case 5:t.p=5,o=t.v,console.error("发布帖子失败:",o),G.nk.error("发布帖子失败，请稍后再试");case 6:return t.p=6,n.value=!1,t.f(6);case 7:t.n=9;break;case 8:console.log("表单验证未通过"),G.nk.warning("请检查表单内容");case 9:t.n=11;break;case 10:t.p=10,r=t.v,console.error("表单验证失败:",r),G.nk.error("表单验证失败，请检查表单内容");case 11:return t.a(2)}},t,null,[[3,5,6,7],[1,10]])}));return function(){return t.apply(this,arguments)}}(),R=function(e){var t=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#3498db","#9b59b6","#1abc9c","#e74c3c","#34495e"];return e?t[e%t.length]:t[0]},I=function(e){if(!e)return"";var t=new Date,n=new Date(e),a=Math.floor((t-n)/1e3);return a<60?"刚刚":a<3600?Math.floor(a/60)+"分钟前":a<86400?Math.floor(a/3600)+"小时前":a<2592e3?Math.floor(a/86400)+"天前":"".concat(n.getFullYear(),"-").concat(String(n.getMonth()+1).padStart(2,"0"),"-").concat(String(n.getDate()).padStart(2,"0"))};return(0,a.sV)(function(){C(),w(),L()}),{loading:t,submitting:n,posts:l,categories:o,tags:r,selectedCategory:u,selectedTag:s,searchKeyword:i,currentSort:c,currentPage:d,pageSize:g,total:v,totalPages:m,sortOptions:y,newPost:h,postRules:b,newPostDialogVisible:p,newPostForm:f,isLoggedIn:k,selectCategory:_,selectTag:F,searchPosts:P,sortPosts:K,viewPostDetail:V,handlePageChange:A,showNewPostDialog:X,submitPost:E,getCategoryColor:R,formatTime:I}}};var $=n(1169);const ee=(0,$.A)(Z,[["render",x],["__scopeId","data-v-745b8a91"]]),te=ee}}]);