"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[675],{6675:(e,n,t)=>{t.r(n),t.d(n,{default:()=>x});var a=t(24059),u=t(698),l=(t(23288),t(95976)),r=t(10160),c=t(12040),o=t(18057),i=t(30578),s=t(17383),d=t(36149),f={class:"announcement-management"},k={class:"page-header"},b={class:"header-actions"},p={class:"announcement-list"},m={class:"card-header"},v={class:"announcement-info"},_={class:"announcement-meta"},h={class:"publish-time"},y={class:"author"},g={key:0,class:"blockchain-info"},F={class:"card-actions"},L={class:"announcement-content"},w={key:0,class:"blockchain-details"},V={class:"hash-container"},A={class:"hash-container"};const C={__name:"AnnouncementManagement",setup:function(e){var n=(0,c.KR)(!1),t=(0,c.KR)(!1),C=(0,c.KR)(!1),W=(0,c.KR)(),z=(0,c.KR)([]),x=(0,c.KR)(null),R=(0,c.Kh)({id:null,title:"",type:"",content:"",status:"draft",relatedLink:"",cloudLink:""}),K={title:[{required:!0,message:"请输入公告标题",trigger:"blur"}],type:[{required:!0,message:"请选择公告类型",trigger:"change"}],content:[{required:!0,message:"请输入公告内容",trigger:"blur"}]},U=function(){var e=(0,u.A)((0,a.A)().m(function e(){var n,t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,d.A.get("/admin/announcements");case 1:n=e.v,n.data.success&&(z.value=n.data.data.announcements||[]),e.n=3;break;case 2:e.p=2,t=e.v,console.error("加载公告列表失败:",t),o.nk.error("加载公告列表失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),X=function(){C.value=!1,Q(),n.value=!0},E=function(e){C.value=!0,R.id=e.id,R.title=e.title,R.type=e.type,R.content=e.content,R.status=e.status,R.relatedLink=e.relatedLink||"",R.cloudLink=e.cloudLink||"",n.value=!0},Q=function(){R.id=null,R.title="",R.type="",R.content="",R.status="draft",R.relatedLink="",R.cloudLink=""},q=function(){var e=(0,u.A)((0,a.A)().m(function e(){var t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:if(W.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,W.value.validate();case 2:if(!C.value){e.n=4;break}return e.n=3,d.A.put("/admin/announcements/".concat(R.id),R);case 3:o.nk.success("公告更新成功"),e.n=6;break;case 4:return e.n=5,d.A.post("/admin/announcements",R);case 5:o.nk.success("公告创建成功");case 6:n.value=!1,U(),e.n=8;break;case 7:e.p=7,t=e.v,console.error("提交失败:",t),o.nk.error("操作失败");case 8:return e.a(2)}},e,null,[[1,7]])}));return function(){return e.apply(this,arguments)}}(),H=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,d.A.put("/admin/announcements/".concat(n,"/publish"));case 1:o.nk.success("公告已发布"),U(),e.n=3;break;case 2:e.p=2,t=e.v,console.error("发布公告失败:",t),o.nk.error("发布失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(n){return e.apply(this,arguments)}}(),T=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,d.A.put("/admin/announcements/".concat(n),{status:"draft"});case 1:o.nk.success("公告已下线"),U(),e.n=3;break;case 2:e.p=2,t=e.v,console.error("下线公告失败:",t),o.nk.error("下线失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(n){return e.apply(this,arguments)}}(),B=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,i.s.confirm("确定要删除该公告吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:return e.n=2,d.A["delete"]("/admin/announcements/".concat(n));case 2:o.nk.success("删除成功"),U(),e.n=4;break;case 3:e.p=3,t=e.v,"cancel"!==t&&(console.error("删除公告失败:",t),o.nk.error("删除失败"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(n){return e.apply(this,arguments)}}(),D=function(e){var n={system:"系统公告",activity:"活动公告",maintenance:"维护公告",important:"重要通知"};return n[e]||e},I=function(e){var n={system:"primary",activity:"success",maintenance:"warning",important:"danger"};return n[e]||""},N=function(e){var n={draft:"草稿",published:"已发布"};return n[e]||e},S=function(e){var n={draft:"info",published:"success"};return n[e]||""},M=function(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},$=function(e,n){return e?e.length<=n?e:e.substring(0,n)+"...":""},j=function(e,n){return e?e.length<=n?e:e.substring(0,n)+"...":""},G=function(e){x.value=e,t.value=!0},J=function(e){if(!e)return"未知";var n=new Date(1e3*e);return n.toLocaleString("zh-CN")};return(0,l.sV)(function(){U()}),function(e,a){var u=(0,l.g2)("el-button"),o=(0,l.g2)("el-tag"),i=(0,l.g2)("el-icon"),d=(0,l.g2)("el-card"),U=(0,l.g2)("el-input"),Q=(0,l.g2)("el-form-item"),O=(0,l.g2)("el-option"),P=(0,l.g2)("el-select"),Y=(0,l.g2)("el-radio"),Z=(0,l.g2)("el-radio-group"),ee=(0,l.g2)("el-form"),ne=(0,l.g2)("el-dialog"),te=(0,l.g2)("el-alert"),ae=(0,l.g2)("el-text"),ue=(0,l.g2)("el-descriptions-item"),le=(0,l.g2)("el-descriptions");return(0,l.uX)(),(0,l.CE)("div",f,[(0,l.Lk)("div",k,[a[12]||(a[12]=(0,l.Lk)("h1",null,"公告管理",-1)),(0,l.Lk)("div",b,[(0,l.bF)(u,{type:"primary",onClick:X},{default:(0,l.k6)(function(){return a[10]||(a[10]=[(0,l.eW)("发布公告")])}),_:1,__:[10]}),(0,l.bF)(u,{onClick:a[0]||(a[0]=function(n){return e.$router.go(-1)})},{default:(0,l.k6)(function(){return a[11]||(a[11]=[(0,l.eW)("返回")])}),_:1,__:[11]})])]),(0,l.Lk)("div",p,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(z.value,function(e){return(0,l.uX)(),(0,l.Wv)(d,{key:e.id,class:"announcement-card",shadow:"hover"},{header:(0,l.k6)(function(){var n;return[(0,l.Lk)("div",m,[(0,l.Lk)("div",v,[(0,l.Lk)("h3",null,(0,r.v_)(j(e.title,15)),1),(0,l.Lk)("div",_,[(0,l.bF)(o,{type:I(e.type),size:"small"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(D(e.type)),1)]}),_:2},1032,["type"]),(0,l.bF)(o,{type:S(e.status),size:"small"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(N(e.status)),1)]}),_:2},1032,["type"]),(0,l.Lk)("span",h,(0,r.v_)(M(e.createdAt)),1),(0,l.Lk)("span",y,"作者: "+(0,r.v_)((null===(n=e.author)||void 0===n?void 0:n.username)||"未知"),1),e.transactionHash?((0,l.uX)(),(0,l.CE)("div",g,[(0,l.bF)(o,{type:"success",size:"small",effect:"light",onClick:function(n){return G(e)},class:"blockchain-tag"},{default:(0,l.k6)(function(){return[(0,l.bF)(i,null,{default:(0,l.k6)(function(){return[(0,l.bF)((0,c.R1)(s.Link))]}),_:1}),a[13]||(a[13]=(0,l.eW)(" 已上链 "))]}),_:2,__:[13]},1032,["onClick"])])):(0,l.Q3)("",!0)])]),(0,l.Lk)("div",F,["draft"===e.status?((0,l.uX)(),(0,l.Wv)(u,{key:0,type:"success",size:"small",onClick:function(n){return H(e.id)}},{default:(0,l.k6)(function(){return a[14]||(a[14]=[(0,l.eW)(" 发布 ")])}),_:2,__:[14]},1032,["onClick"])):(0,l.Q3)("",!0),"published"===e.status?((0,l.uX)(),(0,l.Wv)(u,{key:1,type:"warning",size:"small",onClick:function(n){return T(e.id)}},{default:(0,l.k6)(function(){return a[15]||(a[15]=[(0,l.eW)(" 下线 ")])}),_:2,__:[15]},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)(u,{type:"primary",size:"small",onClick:function(n){return E(e)}},{default:(0,l.k6)(function(){return a[16]||(a[16]=[(0,l.eW)(" 编辑 ")])}),_:2,__:[16]},1032,["onClick"]),(0,l.bF)(u,{type:"danger",size:"small",onClick:function(n){return B(e.id)}},{default:(0,l.k6)(function(){return a[17]||(a[17]=[(0,l.eW)(" 删除 ")])}),_:2,__:[17]},1032,["onClick"])])])]}),default:(0,l.k6)(function(){return[(0,l.Lk)("div",L,[(0,l.Lk)("p",null,(0,r.v_)($(e.content,20)),1)])]}),_:2},1024)}),128))]),(0,l.bF)(ne,{modelValue:n.value,"onUpdate:modelValue":a[8]||(a[8]=function(e){return n.value=e}),title:C.value?"编辑公告":"发布公告",width:"600px"},{footer:(0,l.k6)(function(){return[(0,l.bF)(u,{onClick:a[7]||(a[7]=function(e){return n.value=!1})},{default:(0,l.k6)(function(){return a[20]||(a[20]=[(0,l.eW)("取消")])}),_:1,__:[20]}),(0,l.bF)(u,{type:"primary",onClick:q},{default:(0,l.k6)(function(){return a[21]||(a[21]=[(0,l.eW)("确定")])}),_:1,__:[21]})]}),default:(0,l.k6)(function(){return[(0,l.bF)(ee,{ref_key:"formRef",ref:W,model:R,rules:K,"label-width":"80px"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{label:"公告标题",prop:"title"},{default:(0,l.k6)(function(){return[(0,l.bF)(U,{modelValue:R.title,"onUpdate:modelValue":a[1]||(a[1]=function(e){return R.title=e}),placeholder:"请输入公告标题"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(Q,{label:"公告类型",prop:"type"},{default:(0,l.k6)(function(){return[(0,l.bF)(P,{modelValue:R.type,"onUpdate:modelValue":a[2]||(a[2]=function(e){return R.type=e}),placeholder:"请选择公告类型"},{default:(0,l.k6)(function(){return[(0,l.bF)(O,{label:"系统公告",value:"system"}),(0,l.bF)(O,{label:"活动公告",value:"activity"}),(0,l.bF)(O,{label:"维护公告",value:"maintenance"}),(0,l.bF)(O,{label:"重要通知",value:"important"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(Q,{label:"公告内容",prop:"content"},{default:(0,l.k6)(function(){return[(0,l.bF)(U,{modelValue:R.content,"onUpdate:modelValue":a[3]||(a[3]=function(e){return R.content=e}),type:"textarea",rows:6,placeholder:"请输入公告内容"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(Q,{label:"相关链接"},{default:(0,l.k6)(function(){return[(0,l.bF)(U,{modelValue:R.relatedLink,"onUpdate:modelValue":a[4]||(a[4]=function(e){return R.relatedLink=e}),placeholder:"请输入相关链接（可选）"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(Q,{label:"网盘链接"},{default:(0,l.k6)(function(){return[(0,l.bF)(U,{modelValue:R.cloudLink,"onUpdate:modelValue":a[5]||(a[5]=function(e){return R.cloudLink=e}),placeholder:"请输入网盘链接（可选）"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(Q,{label:"发布状态",prop:"status"},{default:(0,l.k6)(function(){return[(0,l.bF)(Z,{modelValue:R.status,"onUpdate:modelValue":a[6]||(a[6]=function(e){return R.status=e})},{default:(0,l.k6)(function(){return[(0,l.bF)(Y,{label:"draft"},{default:(0,l.k6)(function(){return a[18]||(a[18]=[(0,l.eW)("保存为草稿")])}),_:1,__:[18]}),(0,l.bF)(Y,{label:"published"},{default:(0,l.k6)(function(){return a[19]||(a[19]=[(0,l.eW)("立即发布")])}),_:1,__:[19]})]}),_:1},8,["modelValue"])]}),_:1})]}),_:1},8,["model"])]}),_:1},8,["modelValue","title"]),(0,l.bF)(ne,{modelValue:t.value,"onUpdate:modelValue":a[9]||(a[9]=function(e){return t.value=e}),title:"区块链信息",width:"600px"},{default:(0,l.k6)(function(){return[x.value?((0,l.uX)(),(0,l.CE)("div",w,[(0,l.bF)(te,{title:"此公告已成功上链",type:"success",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:(0,l.k6)(function(){return a[22]||(a[22]=[(0,l.eW)(" 区块链技术确保了公告内容的不可篡改性和透明度 ")])}),_:1}),(0,l.bF)(le,{column:1,border:"",size:"large"},{default:(0,l.k6)(function(){return[(0,l.bF)(ue,{label:"公告标题","label-class-name":"blockchain-label"},{default:(0,l.k6)(function(){return[(0,l.bF)(ae,{type:"primary",size:"large"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(x.value.title),1)]}),_:1})]}),_:1}),(0,l.bF)(ue,{label:"交易哈希","label-class-name":"blockchain-label"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",V,[(0,l.bF)(ae,{copyable:"",class:"hash-text"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(x.value.transactionHash),1)]}),_:1})])]}),_:1}),(0,l.bF)(ue,{label:"区块哈希","label-class-name":"blockchain-label"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",A,[(0,l.bF)(ae,{copyable:"",class:"hash-text"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(x.value.blockHash),1)]}),_:1})])]}),_:1}),(0,l.bF)(ue,{label:"上链时间","label-class-name":"blockchain-label"},{default:(0,l.k6)(function(){return[(0,l.bF)(ae,{type:"success",size:"large"},{default:(0,l.k6)(function(){return[(0,l.bF)(i,null,{default:(0,l.k6)(function(){return[(0,l.bF)((0,c.R1)(s.Clock))]}),_:1}),(0,l.eW)(" "+(0,r.v_)(J(x.value.blockchainTimestamp)),1)]}),_:1})]}),_:1})]}),_:1})])):(0,l.Q3)("",!0)]}),_:1},8,["modelValue"])])}}};var W=t(1169);const z=(0,W.A)(C,[["__scopeId","data-v-1bc7e5a0"]]),x=z}}]);