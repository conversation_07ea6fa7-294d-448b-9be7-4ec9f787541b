"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[252],{92252:(e,n,t)=>{t.r(n),t.d(n,{default:()=>X});var a=t(95976),r=t(10160),c={class:"participate-container"},l={class:"header"},s={class:"container header-content"},i={class:"nav"},o={class:"nav-list"},u={class:"nav-item"},k={class:"nav-item active"},d={class:"nav-item"},f={class:"nav-item"},p={class:"nav-item"},v={class:"nav-item"},m={class:"nav-item"},_={class:"participate-content container"},g={class:"project-info"},L={class:"project-title"},b={class:"company-name"},h={class:"confirm-section"},j={class:"action-buttons"};function F(e,n,t,F,W,y){var C=(0,a.g2)("router-link"),w=(0,a.g2)("el-checkbox"),A=(0,a.g2)("el-button"),K=(0,a.g2)("el-card"),R=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",c,[(0,a.Lk)("header",l,[(0,a.Lk)("div",s,[n[8]||(n[8]=(0,a.Lk)("div",{class:"logo"},[(0,a.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云",class:"logo-img"}),(0,a.Lk)("span",{class:"logo-text"},"衍盾星云")],-1)),(0,a.Lk)("nav",i,[(0,a.Lk)("ul",o,[(0,a.Lk)("li",u,[(0,a.bF)(C,{to:"/"},{default:(0,a.k6)(function(){return n[1]||(n[1]=[(0,a.eW)("首页")])}),_:1,__:[1]})]),(0,a.Lk)("li",k,[(0,a.bF)(C,{to:"/projects"},{default:(0,a.k6)(function(){return n[2]||(n[2]=[(0,a.eW)("项目大厅")])}),_:1,__:[2]})]),(0,a.Lk)("li",d,[(0,a.bF)(C,{to:"/enterprise"},{default:(0,a.k6)(function(){return n[3]||(n[3]=[(0,a.eW)("企业服务")])}),_:1,__:[3]})]),(0,a.Lk)("li",f,[(0,a.bF)(C,{to:"/ranking"},{default:(0,a.k6)(function(){return n[4]||(n[4]=[(0,a.eW)("风云排行")])}),_:1,__:[4]})]),(0,a.Lk)("li",p,[(0,a.bF)(C,{to:"/activities"},{default:(0,a.k6)(function(){return n[5]||(n[5]=[(0,a.eW)("公告活动")])}),_:1,__:[5]})]),(0,a.Lk)("li",v,[(0,a.bF)(C,{to:"/community"},{default:(0,a.k6)(function(){return n[6]||(n[6]=[(0,a.eW)("社区中心")])}),_:1,__:[6]})]),(0,a.Lk)("li",m,[(0,a.bF)(C,{to:"/help"},{default:(0,a.k6)(function(){return n[7]||(n[7]=[(0,a.eW)("帮助中心")])}),_:1,__:[7]})])])])])]),(0,a.Lk)("div",_,[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(K,null,{default:(0,a.k6)(function(){return[F.project?((0,a.uX)(),(0,a.CE)(a.FK,{key:0},[(0,a.Lk)("div",g,[(0,a.Lk)("h1",L,(0,r.v_)(F.project.title),1),(0,a.Lk)("p",b,(0,r.v_)(F.project.companyName),1)]),n[12]||(n[12]=(0,a.Lk)("div",{class:"agreement-section"},[(0,a.Lk)("h2",{class:"section-title"},"参与协议"),(0,a.Lk)("div",{class:"agreement-content"},[(0,a.Lk)("p",null,"在参与本项目之前，请仔细阅读以下协议："),(0,a.Lk)("ol",null,[(0,a.Lk)("li",null,"您必须遵守项目方提供的测试范围和规则。"),(0,a.Lk)("li",null,"您提交的漏洞报告必须真实、准确、完整。"),(0,a.Lk)("li",null,"您不得利用发现的漏洞进行任何破坏性操作。"),(0,a.Lk)("li",null,"您不得将项目信息和发现的漏洞泄露给第三方。"),(0,a.Lk)("li",null,"您同意项目方对漏洞的评级和奖励决定。")])])],-1)),(0,a.Lk)("div",h,[(0,a.bF)(w,{modelValue:F.agreement,"onUpdate:modelValue":n[0]||(n[0]=function(e){return F.agreement=e}),class:"agreement-checkbox"},{default:(0,a.k6)(function(){return n[9]||(n[9]=[(0,a.eW)(" 我已阅读并同意上述协议内容 ")])}),_:1,__:[9]},8,["modelValue"]),(0,a.Lk)("div",j,[(0,a.bF)(A,{onClick:F.goBack},{default:(0,a.k6)(function(){return n[10]||(n[10]=[(0,a.eW)("返回")])}),_:1,__:[10]},8,["onClick"]),(0,a.bF)(A,{type:"primary",disabled:!F.agreement,onClick:F.handleConfirm,loading:F.submitting},{default:(0,a.k6)(function(){return n[11]||(n[11]=[(0,a.eW)(" 确认参与 ")])}),_:1,__:[11]},8,["disabled","onClick","loading"])])])],64)):(0,a.Q3)("",!0)]}),_:1})),[[R,F.loading]])])])}var W=t(24059),y=t(698),C=(t(44114),t(12040)),w=t(39053),A=t(18057),K=t(36149);const R={name:"ProjectParticipate",setup:function(){var e=(0,w.lq)(),n=(0,w.rd)(),t=(0,C.KR)(!0),r=(0,C.KR)(!1),c=(0,C.KR)(null),l=(0,C.KR)(!1),s=function(){var n=(0,y.A)((0,W.A)().m(function n(){var a,r;return(0,W.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,K.A.get("/projects/".concat(e.params.id));case 1:a=n.v,a.data.success?c.value=a.data.data:A.nk.error(a.data.message||"获取项目信息失败"),n.n=3;break;case 2:n.p=2,r=n.v,console.error("获取项目信息失败:",r),A.nk.error("获取项目信息失败，请稍后重试");case 3:return n.p=3,t.value=!1,n.f(3);case 4:return n.a(2)}},n,null,[[0,2,3,4]])}));return function(){return n.apply(this,arguments)}}(),i=function(){var t=(0,y.A)((0,W.A)().m(function t(){var a,c;return(0,W.A)().w(function(t){while(1)switch(t.n){case 0:if(l.value){t.n=1;break}return A.nk.warning("请先同意参与协议"),t.a(2);case 1:return r.value=!0,t.p=2,t.n=3,K.A.post("/projects/".concat(e.params.id,"/participate"));case 3:a=t.v,a.data.success?(A.nk.success("参与成功！"),n.push("/projects/".concat(e.params.id))):A.nk.error(a.data.message||"参与失败"),t.n=5;break;case 4:t.p=4,c=t.v,console.error("参与项目失败:",c),A.nk.error("参与失败，请稍后重试");case 5:return t.p=5,r.value=!1,t.f(5);case 6:return t.a(2)}},t,null,[[2,4,5,6]])}));return function(){return t.apply(this,arguments)}}(),o=function(){n.back()};return(0,a.sV)(function(){s()}),{loading:t,submitting:r,project:c,agreement:l,handleConfirm:i,goBack:o}}};var V=t(1169);const x=(0,V.A)(R,[["render",F],["__scopeId","data-v-642cf962"]]),X=x}}]);