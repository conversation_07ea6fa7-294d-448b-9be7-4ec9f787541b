<template>
  <div class="activity-detail-container">
    <TheHeader />
    
    <div class="activity-detail-content" v-loading="loading">
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/activities' }">活动中心</el-breadcrumb-item>
          <el-breadcrumb-item>活动详情</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <div v-if="activity" class="activity-content">
        <!-- 活动头部 -->
        <div class="activity-header">
          <!-- <div class="activity-banner">
            <img :src="activity.bannerImage || require('@/assets/logo.png')" alt="活动横幅" class="banner-image">

          </div> -->
          
          <div class="activity-info">
            <div class="title-section">
              <h1 class="activity-title">{{ activity.title }}</h1>
              <!-- 区块链信息标签 -->
              <div v-if="activity.transactionHash" class="blockchain-badge">
                <el-icon><Check /></el-icon>
                <span>区块链认证</span>
              </div>
            </div>
            <div class="activity-meta">
              <div class="meta-item">
                <i class="el-icon-time"></i>
                <span>发布时间：{{ formatDateTime(activity.startTime) }}</span>
              </div>
              <div class="meta-item" v-if="activity.location">
                <i class="el-icon-location"></i>
                <span>{{ activity.location }}</span>
              </div>
            </div>
          </div>
        </div>



        <!-- 区块链认证信息 -->
        <div class="blockchain-verification" v-if="activity.transactionHash">
          <div class="verification-header">
            <div class="verification-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="verification-content">
              <h3 class="verification-title">区块链认证</h3>
              <p class="verification-desc">此内容已通过区块链技术认证，确保信息的真实性和不可篡改性</p>
            </div>
            <div class="verification-status">
              <el-tag type="success" size="large" effect="dark">
                <el-icon><Check /></el-icon>
                已认证
              </el-tag>
            </div>
          </div>

          <div class="verification-details">
            <div class="detail-row">
              <div class="detail-item">
                <div class="detail-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="detail-content">
                  <span class="detail-label">交易哈希</span>
                  <div class="detail-value">
                    <el-text class="hash-text" copyable>{{ activity.transactionHash }}</el-text>
                  </div>
                </div>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <div class="detail-icon">
                  <el-icon><Key /></el-icon>
                </div>
                <div class="detail-content">
                  <span class="detail-label">区块哈希</span>
                  <div class="detail-value">
                    <el-text class="hash-text" copyable>{{ activity.blockHash }}</el-text>
                  </div>
                </div>
              </div>

              <div class="detail-item">
                <div class="detail-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="detail-content">
                  <span class="detail-label">认证时间</span>
                  <div class="detail-value">
                    <span class="time-text">{{ formatBlockchainTime(activity.blockchainTimestamp) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 活动描述 -->
        <div class="activity-section">
          <h2 class="section-title">{{ activity.type === 'announcement' ? '公告内容' : '活动介绍' }}</h2>
          <div class="section-content" v-html="activity.description"></div>
        </div>

        <!-- 相关链接 -->
        <div class="activity-section" v-if="activity.relatedLink">
          <h2 class="section-title">相关链接</h2>
          <div class="section-content">
            <div class="related-link-display">
              <i class="el-icon-link"></i>
              {{ activity.relatedLink }}
            </div>
          </div>
        </div>

        <!-- 网盘链接 -->
        <div class="activity-section" v-if="activity.cloudLink">
          <h2 class="section-title">网盘链接</h2>
          <div class="section-content">
            <div class="cloud-link-display">
              <i class="el-icon-link"></i>
              {{ activity.cloudLink }}
            </div>
          </div>
        </div>

        <!-- 活动规则 -->
        <div class="activity-section" v-if="activity.rules">
          <h2 class="section-title">活动规则</h2>
          <div class="section-content" v-html="activity.rules"></div>
        </div>

        <!-- 奖励设置 -->
        <div class="activity-section" v-if="activity.rewards">
          <h2 class="section-title">奖励设置</h2>
          <div class="section-content">
            <div class="rewards-list">
              <div v-for="(reward, index) in activity.rewards" :key="index" class="reward-item">
                <div class="reward-rank">{{ getRankText(index + 1) }}</div>
                <div class="reward-amount">¥{{ formatNumber(reward) }}</div>
              </div>
            </div>
          </div>
        </div>



        <!-- 操作按钮 -->
        <div class="activity-actions">
          <el-button size="large" @click="goBack">
            <i class="el-icon-back"></i> 返回列表
          </el-button>
        </div>
      </div>
    </div>
        <!-- 页脚 -->
    <AppFooter />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Clock, Check, Document,Key } from '@element-plus/icons-vue';
import api from '@/utils/api';
import AppFooter from '@/components/AppFooter.vue';
import TheHeader from '@/components/TheHeader.vue';

export default {
  name: 'ActivityDetail',
  components: {
    TheHeader,
    Clock,
    Check,
    AppFooter,
    Document,
    Key
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(true);
    const activity = ref(null);

    // 获取活动详情
    const fetchActivityDetail = async () => {
      try {
        const id = route.params.id;

        // 判断是公告还是活动
        if (id.startsWith('announcement_')) {
          const announcementId = id.replace('announcement_', '');
          const response = await fetch(`/api/announcements/${announcementId}`);

          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              activity.value = {
                ...result.data,
                id: id,
                type: 'announcement',
                description: result.data.content,
                startTime: result.data.publishedAt,
                endTime: result.data.publishedAt,
                views: result.data.viewCount,
                bannerImage: require('@/assets/logo.png'),
                coverImage: require('@/assets/logo.png'),
                status: 'ongoing',
                relatedLink: result.data.relatedLink,
                cloudLink: result.data.cloudLink
              };
            } else {
              ElMessage.error('获取公告详情失败');
            }
          } else {
            ElMessage.error('获取公告详情失败');
          }
        } else if (id.startsWith('activity_')) {
          const activityId = id.replace('activity_', '');
          const response = await fetch(`/api/activities/${activityId}`);

          if (response.ok) {
            const result = await response.json();
            if (result.code === 0) {
              activity.value = {
                ...result.data,
                id: id,
                type: 'event',
                title: result.data.name,
                startTime: result.data.startDate,
                endTime: result.data.endDate,
                status: result.data.status,
                views: 0,
                bannerImage: require('@/assets/logo.png'),
                coverImage: require('@/assets/logo.png'),
                participantCount: 0
              };
            } else {
              ElMessage.error('获取活动详情失败');
            }
          } else {
            ElMessage.error('获取活动详情失败');
          }
        } else {
          // 兼容旧的ID格式
          const response = await api.get(`/api/activities/${id}`);
          if (response.data.success) {
            activity.value = response.data.data;
          } else {
            ElMessage.error(response.data.message || '获取活动详情失败');
          }
        }

        if (!activity.value) {
          ElMessage.error('未找到相关内容');
        }
      } catch (error) {
        console.error('获取活动详情失败:', error);
        ElMessage.error('获取活动详情失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 格式化数字
    const formatNumber = (num) => {
      if (!num) return '0';
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    };

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    };

    // 格式化日期时间
    const formatDateTime = (datetime) => {
      if (!datetime) return '';
      const d = new Date(datetime);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
    };

    // 格式化区块链时间戳
    const formatBlockchainTime = (timestamp) => {
      if (!timestamp) return '未知';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN');
    };





    // 获取排名文本
    const getRankText = (rank) => {
      const rankMap = {
        1: '第一名',
        2: '第二名',
        3: '第三名'
      };
      return rankMap[rank] || `第${rank}名`;
    };



    // 返回列表
    const goBack = () => {
      router.push('/activities');
    };

    onMounted(() => {
      fetchActivityDetail();
    });

    return {
      loading,
      activity,
      formatNumber,
      formatDate,
      formatDateTime,
      formatBlockchainTime,
      getRankText,
      goBack
    };
  }
};
</script>

<style scoped>
.activity-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.activity-detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px 40px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.activity-content {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 活动头部 */
.activity-header {
  position: relative;
}

.activity-banner {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}





.activity-info {
  padding: 30px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.activity-title {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.blockchain-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  animation: glow 3s ease-in-out infinite alternate;
}

.blockchain-badge .el-icon {
  font-size: 16px;
}

@keyframes glow {
  from {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
  to {
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.6);
  }
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.meta-item i {
  color: #409eff;
}

/* 活动内容区域 */
.activity-section {
  margin: 0 30px 30px 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #ebeef5;
  min-height: auto;
  height: auto;
}

.activity-section:last-of-type {
  border-bottom: none;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 12px;
  border-left: 4px solid #409eff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  line-height: 1.8;
  color: #606266;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  min-height: auto;
  max-width: 100%;
  overflow-wrap: break-word;
}

/* 确保HTML内容也能正确换行 */
.section-content p {
  margin: 0 0 16px 0;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
}

.section-content p:last-child {
  margin-bottom: 0;
}

/* 奖励列表 */
.rewards-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.reward-rank {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.reward-amount {
  font-size: 20px;
  font-weight: 700;
  color: #e6a23c;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e4e7ed;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -25px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e4e7ed;
  border: 3px solid #fff;
  box-shadow: 0 0 0 2px #e4e7ed;
  transition: all 0.3s;
}

.timeline-item.active .timeline-dot {
  background: #409eff;
  box-shadow: 0 0 0 2px #409eff;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 14px;
  color: #909399;
}

/* 操作按钮 */
.activity-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 30px;
  background-color: #f8f9fa;
}

/* 相关链接样式 */
.related-link-display {
  display: inline-flex;
  align-items: center;
  color: #606266;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  word-break: break-all;
}

.related-link-display i {
  margin-right: 8px;
  color: #909399;
}

/* 网盘链接样式 */
.cloud-link-display {
  display: inline-flex;
  align-items: center;
  color: #606266;
  padding: 12px 20px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  font-size: 14px;
  word-break: break-all;
}

.cloud-link-display i {
  margin-right: 8px;
  font-size: 16px;
  color: #909399;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .activity-detail-content {
    padding: 80px 10px 20px;
  }

  .activity-info {
    padding: 20px;
  }

  .activity-title {
    font-size: 22px;
  }

  .activity-meta {
    flex-direction: column;
    gap: 10px;
  }

  .activity-section {
    margin: 0 20px 20px 20px;
  }

  .activity-actions {
    flex-direction: column;
    padding: 20px;
  }

  .activity-actions .el-button {
    width: 100%;
  }
}

/* 区块链认证样式 */
.blockchain-verification {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  margin: 30px 0;
  position: relative;
  overflow: hidden;
}

.blockchain-verification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6, #10b981);
  background-size: 300% 100%;
  animation: rainbow-flow 4s ease-in-out infinite;
}

@keyframes rainbow-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.verification-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.verification-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.verification-content {
  flex: 1;
}

.verification-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.verification-desc {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.verification-status {
  display: flex;
  align-items: center;
}

.verification-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.detail-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.detail-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  min-width: 0;
}

.detail-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.detail-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 500;
}

.hash-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  word-break: break-all;
  line-height: 1.4;
  background: #f8fafc;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.time-text {
  font-weight: 600;
  color: #059669;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .detail-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .detail-item {
    padding: 12px;
  }

  .blockchain-verification {
    padding: 16px;
    margin: 20px 0;
  }
}
</style>
