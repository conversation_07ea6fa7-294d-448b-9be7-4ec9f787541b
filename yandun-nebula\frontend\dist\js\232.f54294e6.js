"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[232],{93232:(n,l,a)=>{a.r(l),a.d(l,{default:()=>nn});var s=a(24059),t=a(698),u=(a(44114),a(95976)),e=a(12040),i=a(10160),d=a(18057),o=a(17383),v=a(36149),c={class:"admin-dashboard"},r={class:"stats-section"},k={class:"stats-grid"},L={class:"stat-card"},p={class:"stat-icon users"},f={class:"stat-info"},_={class:"stat-detail"},b={class:"today-new"},h={class:"stat-card"},m={class:"stat-icon projects"},F={class:"stat-info"},g={class:"stat-detail"},w={class:"today-new"},R={class:"stat-card"},y={class:"stat-icon vulnerabilities"},C={class:"stat-info"},j={class:"stat-detail"},A={class:"stat-card"},B={class:"stat-icon posts"},$={class:"stat-info"},D={class:"stat-detail"},N={class:"today-new"},U={class:"stat-card"},E={class:"stat-icon announcements"},H={class:"stat-info"},I={class:"stat-detail"},K={class:"management-section"},V={class:"management-grid"},W={class:"card-icon"},X={class:"card-content"},q={class:"card-stats"},x={class:"card-icon"},z={class:"card-content"},G={class:"card-stats"},J={class:"card-icon"},M={class:"card-content"},O={class:"card-stats"},P={class:"card-icon"},Q={class:"card-content"},S={class:"card-stats"};const T={__name:"Dashboard",setup:function(n){var l=(0,e.KR)({}),a=function(){var n=(0,t.A)((0,s.A)().m(function n(){var a,t;return(0,s.A)().w(function(n){while(1)switch(n.n){case 0:return n.p=0,n.n=1,v.A.get("/admin/dashboard/stats");case 1:a=n.v,a.data.success&&(l.value=a.data.data),n.n=3;break;case 2:n.p=2,t=n.v,console.error("加载统计数据失败:",t),d.nk.error("加载统计数据失败");case 3:return n.a(2)}},n,null,[[0,2]])}));return function(){return n.apply(this,arguments)}}();return(0,u.sV)(function(){a()}),function(n,a){var s,t,d,v,T,Y,Z,nn,ln,an,sn,tn,un,en,dn,on,vn,cn,rn,kn,Ln,pn,fn,_n=(0,u.g2)("el-icon");return(0,u.uX)(),(0,u.CE)("div",c,[(0,u.Lk)("div",r,[a[9]||(a[9]=(0,u.Lk)("h2",{class:"section-title"},"数据概览",-1)),(0,u.Lk)("div",k,[(0,u.Lk)("div",L,[(0,u.Lk)("div",p,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.User))]}),_:1})]),(0,u.Lk)("div",f,[(0,u.Lk)("h3",null,(0,i.v_)((null===(s=l.value.users)||void 0===s?void 0:s.total)||0),1),a[4]||(a[4]=(0,u.Lk)("p",null,"总用户数",-1)),(0,u.Lk)("div",_,[(0,u.Lk)("span",null,"白帽: "+(0,i.v_)((null===(t=l.value.users)||void 0===t?void 0:t.whiteHat)||0),1),(0,u.Lk)("span",null,"企业: "+(0,i.v_)((null===(d=l.value.users)||void 0===d?void 0:d.enterprise)||0),1),(0,u.Lk)("span",b,"今日新增: "+(0,i.v_)((null===(v=l.value.users)||void 0===v?void 0:v.todayNew)||0),1)])])]),(0,u.Lk)("div",h,[(0,u.Lk)("div",m,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.Briefcase))]}),_:1})]),(0,u.Lk)("div",F,[(0,u.Lk)("h3",null,(0,i.v_)((null===(T=l.value.projects)||void 0===T?void 0:T.total)||0),1),a[5]||(a[5]=(0,u.Lk)("p",null,"悬赏项目",-1)),(0,u.Lk)("div",g,[(0,u.Lk)("span",null,"进行中: "+(0,i.v_)((null===(Y=l.value.projects)||void 0===Y?void 0:Y.active)||0),1),(0,u.Lk)("span",null,"已完成: "+(0,i.v_)((null===(Z=l.value.projects)||void 0===Z?void 0:Z.completed)||0),1),(0,u.Lk)("span",w,"今日新增: "+(0,i.v_)((null===(nn=l.value.projects)||void 0===nn?void 0:nn.todayNew)||0),1)])])]),(0,u.Lk)("div",R,[(0,u.Lk)("div",y,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.Warning))]}),_:1})]),(0,u.Lk)("div",C,[(0,u.Lk)("h3",null,(0,i.v_)((null===(ln=l.value.vulnerabilities)||void 0===ln?void 0:ln.total)||0),1),a[6]||(a[6]=(0,u.Lk)("p",null,"漏洞报告",-1)),(0,u.Lk)("div",j,[(0,u.Lk)("span",null,"待审核: "+(0,i.v_)((null===(an=l.value.vulnerabilities)||void 0===an?void 0:an.pending)||0),1),(0,u.Lk)("span",null,"已确认: "+(0,i.v_)(((null===(sn=l.value.vulnerabilities)||void 0===sn?void 0:sn.total)||0)-((null===(tn=l.value.vulnerabilities)||void 0===tn?void 0:tn.pending)||0)),1)])])]),(0,u.Lk)("div",A,[(0,u.Lk)("div",B,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.ChatDotRound))]}),_:1})]),(0,u.Lk)("div",$,[(0,u.Lk)("h3",null,(0,i.v_)((null===(un=l.value.posts)||void 0===un?void 0:un.total)||0),1),a[7]||(a[7]=(0,u.Lk)("p",null,"社区帖子",-1)),(0,u.Lk)("div",D,[(0,u.Lk)("span",null,"已发布: "+(0,i.v_)((null===(en=l.value.posts)||void 0===en?void 0:en.published)||0),1),(0,u.Lk)("span",N,"今日新增: "+(0,i.v_)((null===(dn=l.value.posts)||void 0===dn?void 0:dn.todayNew)||0),1)])])]),(0,u.Lk)("div",U,[(0,u.Lk)("div",E,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.Bell))]}),_:1})]),(0,u.Lk)("div",H,[(0,u.Lk)("h3",null,(0,i.v_)((null===(on=l.value.announcements)||void 0===on?void 0:on.total)||0),1),a[8]||(a[8]=(0,u.Lk)("p",null,"系统公告",-1)),(0,u.Lk)("div",I,[(0,u.Lk)("span",null,"已发布: "+(0,i.v_)((null===(vn=l.value.announcements)||void 0===vn?void 0:vn.published)||0),1),(0,u.Lk)("span",null,"草稿: "+(0,i.v_)(((null===(cn=l.value.announcements)||void 0===cn?void 0:cn.total)||0)-((null===(rn=l.value.announcements)||void 0===rn?void 0:rn.published)||0)),1)])])])])]),(0,u.Lk)("div",K,[a[18]||(a[18]=(0,u.Lk)("h2",null,"管理功能",-1)),(0,u.Lk)("div",V,[(0,u.Lk)("div",{class:"management-card primary",onClick:a[0]||(a[0]=function(l){return n.$router.push("/admin/users")})},[(0,u.Lk)("div",W,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.User))]}),_:1})]),(0,u.Lk)("div",X,[a[10]||(a[10]=(0,u.Lk)("h3",null,"用户管理",-1)),a[11]||(a[11]=(0,u.Lk)("p",null,"管理平台用户账户",-1)),(0,u.Lk)("div",q,[(0,u.Lk)("span",null,(0,i.v_)((null===(kn=l.value.users)||void 0===kn?void 0:kn.total)||0)+" 用户",1)])])]),(0,u.Lk)("div",{class:"management-card secondary",onClick:a[1]||(a[1]=function(l){return n.$router.push("/admin/projects")})},[(0,u.Lk)("div",x,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.Briefcase))]}),_:1})]),(0,u.Lk)("div",z,[a[12]||(a[12]=(0,u.Lk)("h3",null,"项目管理",-1)),a[13]||(a[13]=(0,u.Lk)("p",null,"审核和管理悬赏项目",-1)),(0,u.Lk)("div",G,[(0,u.Lk)("span",null,(0,i.v_)((null===(Ln=l.value.projects)||void 0===Ln?void 0:Ln.total)||0)+" 项目",1)])])]),(0,u.Lk)("div",{class:"management-card success",onClick:a[2]||(a[2]=function(l){return n.$router.push("/admin/posts")})},[(0,u.Lk)("div",J,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.ChatDotRound))]}),_:1})]),(0,u.Lk)("div",M,[a[14]||(a[14]=(0,u.Lk)("h3",null,"内容管理",-1)),a[15]||(a[15]=(0,u.Lk)("p",null,"管理社区帖子内容",-1)),(0,u.Lk)("div",O,[(0,u.Lk)("span",null,(0,i.v_)((null===(pn=l.value.posts)||void 0===pn?void 0:pn.total)||0)+" 帖子",1)])])]),(0,u.Lk)("div",{class:"management-card warning",onClick:a[3]||(a[3]=function(l){return n.$router.push("/admin/announcements")})},[(0,u.Lk)("div",P,[(0,u.bF)(_n,null,{default:(0,u.k6)(function(){return[(0,u.bF)((0,e.R1)(o.Bell))]}),_:1})]),(0,u.Lk)("div",Q,[a[16]||(a[16]=(0,u.Lk)("h3",null,"公告管理",-1)),a[17]||(a[17]=(0,u.Lk)("p",null,"发布和管理系统公告",-1)),(0,u.Lk)("div",S,[(0,u.Lk)("span",null,(0,i.v_)((null===(fn=l.value.announcements)||void 0===fn?void 0:fn.total)||0)+" 公告",1)])])])])])])}}};var Y=a(1169);const Z=(0,Y.A)(T,[["__scopeId","data-v-5e84fe96"]]),nn=Z}}]);