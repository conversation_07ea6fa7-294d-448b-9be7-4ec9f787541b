"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[957],{65957:(e,t,a)=>{a.r(t),a.d(t,{default:()=>x});a(52675),a(89463);var r=a(95976),l={class:"publish-task-container"},n={class:"container main-content"},u={class:"form-section"},s={class:"form-section"},o={class:"budget-container"},i={class:"total-budget"},d={class:"budget-range"},c={class:"budget-levels"},m={class:"budget-tips"},g={class:"form-section"},p={class:"date-container"},k={class:"form-section"},b={class:"form-actions"};function f(e,t,a,f,F,h){var V=(0,r.g2)("TheHeader"),w=(0,r.g2)("el-input"),v=(0,r.g2)("el-form-item"),_=(0,r.g2)("el-input-number"),L=(0,r.g2)("el-alert"),D=(0,r.g2)("el-date-picker"),B=(0,r.g2)("el-button"),M=(0,r.g2)("el-form"),R=(0,r.g2)("el-card");return(0,r.uX)(),(0,r.CE)("div",l,[(0,r.bF)(V),t[28]||(t[28]=(0,r.Lk)("div",{class:"page-header"},[(0,r.Lk)("div",{class:"container"},[(0,r.Lk)("h1",{class:"page-title"},"发布任务"),(0,r.Lk)("p",{class:"page-subtitle"},"创建安全测试任务，让白帽子帮您发现安全漏洞")])],-1)),(0,r.Lk)("div",n,[(0,r.bF)(R,{class:"form-card"},{header:(0,r.k6)(function(){return t[12]||(t[12]=[(0,r.Lk)("div",{class:"card-header"},[(0,r.Lk)("h2",null,"任务信息"),(0,r.Lk)("p",null,"请填写任务详情，带 * 的字段为必填项")],-1)])}),default:(0,r.k6)(function(){return[(0,r.bF)(M,{model:f.taskForm,rules:f.rules,ref:"taskFormRef","label-position":"top"},{default:(0,r.k6)(function(){return[(0,r.Lk)("div",u,[t[13]||(t[13]=(0,r.Lk)("h3",{class:"section-title"},"基本信息",-1)),(0,r.bF)(v,{label:"任务标题",prop:"title"},{default:(0,r.k6)(function(){return[(0,r.bF)(w,{modelValue:f.taskForm.title,"onUpdate:modelValue":t[0]||(t[0]=function(e){return f.taskForm.title=e}),placeholder:"请输入任务标题，如：XX公司网站安全测试",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]}),_:1}),(0,r.bF)(v,{label:"任务描述",prop:"description"},{default:(0,r.k6)(function(){return[(0,r.bF)(w,{type:"textarea",modelValue:f.taskForm.description,"onUpdate:modelValue":t[1]||(t[1]=function(e){return f.taskForm.description=e}),rows:"6",placeholder:"请详细描述任务需求、目标和范围，以便白帽子更好地理解任务",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])]}),_:1})]),(0,r.Lk)("div",s,[t[20]||(t[20]=(0,r.Lk)("h3",{class:"section-title"},"预算设置",-1)),(0,r.Lk)("div",o,[(0,r.Lk)("div",i,[(0,r.bF)(v,{label:"总预算范围",prop:"totalBudget"},{default:(0,r.k6)(function(){return[(0,r.Lk)("div",d,[(0,r.bF)(_,{modelValue:f.taskForm.totalBudgetMin,"onUpdate:modelValue":t[2]||(t[2]=function(e){return f.taskForm.totalBudgetMin=e}),min:5e3,step:1e3,"step-strictly":"",onChange:f.updateTotalBudget},null,8,["modelValue","onChange"]),t[14]||(t[14]=(0,r.Lk)("span",{class:"budget-separator"},"~",-1)),(0,r.bF)(_,{modelValue:f.taskForm.totalBudgetMax,"onUpdate:modelValue":t[3]||(t[3]=function(e){return f.taskForm.totalBudgetMax=e}),min:f.taskForm.totalBudgetMin+1e3,step:1e3,"step-strictly":"",onChange:f.validateTotalBudget},null,8,["modelValue","min","onChange"]),t[15]||(t[15]=(0,r.Lk)("span",{class:"budget-unit"},"元",-1))])]}),_:1})]),(0,r.Lk)("div",c,[(0,r.bF)(v,{label:"低危漏洞奖励",prop:"lowVulReward"},{default:(0,r.k6)(function(){return[(0,r.bF)(_,{modelValue:f.taskForm.lowVulReward,"onUpdate:modelValue":t[4]||(t[4]=function(e){return f.taskForm.lowVulReward=e}),min:100,max:500,step:100},null,8,["modelValue"]),t[16]||(t[16]=(0,r.Lk)("span",{class:"budget-unit"},"元/个",-1))]}),_:1,__:[16]}),(0,r.bF)(v,{label:"中危漏洞奖励",prop:"mediumVulReward"},{default:(0,r.k6)(function(){return[(0,r.bF)(_,{modelValue:f.taskForm.mediumVulReward,"onUpdate:modelValue":t[5]||(t[5]=function(e){return f.taskForm.mediumVulReward=e}),min:500,max:2e3,step:100},null,8,["modelValue"]),t[17]||(t[17]=(0,r.Lk)("span",{class:"budget-unit"},"元/个",-1))]}),_:1,__:[17]}),(0,r.bF)(v,{label:"高危漏洞奖励",prop:"highVulReward"},{default:(0,r.k6)(function(){return[(0,r.bF)(_,{modelValue:f.taskForm.highVulReward,"onUpdate:modelValue":t[6]||(t[6]=function(e){return f.taskForm.highVulReward=e}),min:2e3,max:5e3,step:500},null,8,["modelValue"]),t[18]||(t[18]=(0,r.Lk)("span",{class:"budget-unit"},"元/个",-1))]}),_:1,__:[18]}),(0,r.bF)(v,{label:"严重漏洞奖励",prop:"criticalVulReward"},{default:(0,r.k6)(function(){return[(0,r.bF)(_,{modelValue:f.taskForm.criticalVulReward,"onUpdate:modelValue":t[7]||(t[7]=function(e){return f.taskForm.criticalVulReward=e}),min:5e3,max:2e4,step:1e3},null,8,["modelValue"]),t[19]||(t[19]=(0,r.Lk)("span",{class:"budget-unit"},"元/个",-1))]}),_:1,__:[19]})])]),(0,r.Lk)("div",m,[(0,r.bF)(L,{title:"预算提示",type:"info",description:"根据行业经验，建议严重漏洞奖励不低于5000元，高危漏洞不低于2000元，中危漏洞不低于500元，低危漏洞不低于100元。合理的奖励机制可以吸引更多优秀白帽子参与。",closable:!1,"show-icon":""})])]),(0,r.Lk)("div",g,[t[21]||(t[21]=(0,r.Lk)("h3",{class:"section-title"},"时间设置",-1)),(0,r.Lk)("div",p,[(0,r.bF)(v,{label:"开始日期",prop:"startDate"},{default:(0,r.k6)(function(){return[(0,r.bF)(D,{modelValue:f.taskForm.startDate,"onUpdate:modelValue":t[8]||(t[8]=function(e){return f.taskForm.startDate=e}),type:"date",placeholder:"选择开始日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":f.disablePastDates},null,8,["modelValue","disabled-date"])]}),_:1}),(0,r.bF)(v,{label:"结束日期",prop:"endDate"},{default:(0,r.k6)(function(){return[(0,r.bF)(D,{modelValue:f.taskForm.endDate,"onUpdate:modelValue":t[9]||(t[9]=function(e){return f.taskForm.endDate=e}),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":f.disableInvalidEndDates},null,8,["modelValue","disabled-date"])]}),_:1})]),t[22]||(t[22]=(0,r.Lk)("div",{class:"date-tips"},[(0,r.Lk)("p",null,"建议任务周期至少为7天，以便白帽子有充足的时间进行测试")],-1))]),(0,r.Lk)("div",k,[t[25]||(t[25]=(0,r.Lk)("h3",{class:"section-title"},"测试范围与规则",-1)),(0,r.bF)(v,{label:"测试范围",prop:"scope"},{default:(0,r.k6)(function(){return[(0,r.bF)(w,{type:"textarea",modelValue:f.taskForm.scope,"onUpdate:modelValue":t[10]||(t[10]=function(e){return f.taskForm.scope=e}),rows:"4",placeholder:"请输入测试范围，如网站URL、APP包名、IP地址段等，每行一个"},null,8,["modelValue"]),t[23]||(t[23]=(0,r.Lk)("div",{class:"form-tip"},"例如：https://example.com, *.example.com, ***********/24",-1))]}),_:1,__:[23]}),(0,r.bF)(v,{label:"测试规则",prop:"rules"},{default:(0,r.k6)(function(){return[(0,r.bF)(w,{type:"textarea",modelValue:f.taskForm.rules,"onUpdate:modelValue":t[11]||(t[11]=function(e){return f.taskForm.rules=e}),rows:"4",placeholder:"请输入测试规则和注意事项，如禁止使用的测试方法、特殊要求等"},null,8,["modelValue"]),t[24]||(t[24]=(0,r.Lk)("div",{class:"form-tip"},"例如：禁止进行DDoS测试，禁止对生产环境进行破坏性测试，禁止获取/修改真实用户数据等",-1))]}),_:1,__:[24]})]),(0,r.Lk)("div",b,[(0,r.bF)(v,null,{default:(0,r.k6)(function(){return[(0,r.bF)(B,{type:"primary",onClick:f.submitForm,loading:f.submitting,size:"large"},{default:(0,r.k6)(function(){return t[26]||(t[26]=[(0,r.eW)("发布任务")])}),_:1,__:[26]},8,["onClick","loading"]),(0,r.bF)(B,{onClick:f.resetForm,size:"large"},{default:(0,r.k6)(function(){return t[27]||(t[27]=[(0,r.eW)("重置")])}),_:1,__:[27]},8,["onClick"])]}),_:1})])]}),_:1},8,["model","rules"])]}),_:1})])])}var F=a(24059),h=a(698),V=(a(74423),a(44114),a(59089),a(23288),a(21699),a(12040)),w=a(39053),v=a(18057),_=a(30578),L=a(36149),D=a(20907),B=a(80401);const M={name:"PublishTask",components:{TheHeader:B.A},setup:function(){var e=(0,w.rd)(),t=(0,V.KR)(null),a=(0,V.KR)(!1),r=(0,V.Kh)({title:"",description:"",totalBudgetMin:1e4,totalBudgetMax:2e4,lowVulReward:300,mediumVulReward:1e3,highVulReward:3e3,criticalVulReward:1e4,startDate:"",endDate:"",scope:"",rules:""}),l={title:[{required:!0,message:"请输入任务标题",trigger:"blur"},{min:5,max:100,message:"长度在5到100个字符之间",trigger:"blur"}],description:[{required:!0,message:"请输入任务描述",trigger:"blur"},{min:10,message:"描述不能少于10个字符",trigger:"blur"}],totalBudgetMin:[{required:!0,message:"请设置最低总预算",trigger:"blur"},{type:"number",min:5e3,message:"最低总预算不能低于5000元",trigger:"blur"}],totalBudgetMax:[{required:!0,message:"请设置最高总预算",trigger:"blur"},{type:"number",min:5e3,message:"最高总预算不能低于5000元",trigger:"blur"}],lowVulReward:[{required:!0,message:"请设置低危漏洞奖励",trigger:"blur"}],mediumVulReward:[{required:!0,message:"请设置中危漏洞奖励",trigger:"blur"}],highVulReward:[{required:!0,message:"请设置高危漏洞奖励",trigger:"blur"}],criticalVulReward:[{required:!0,message:"请设置严重漏洞奖励",trigger:"blur"}],startDate:[{required:!0,message:"请选择开始日期",trigger:"change"}],endDate:[{required:!0,message:"请选择结束日期",trigger:"change"}],scope:[{required:!0,message:"请输入测试范围",trigger:"blur"}],rules:[{required:!0,message:"请输入测试规则",trigger:"blur"}]},n=function(e){return e.getTime()<Date.now()-864e5},u=function(e){if(!r.startDate)return n(e);var t=new Date(r.startDate);return e.getTime()<t.getTime()},s=function(){r.totalBudgetMax<r.totalBudgetMin+1e3&&(r.totalBudgetMax=r.totalBudgetMin+1e3)},o=function(){r.totalBudgetMax<=r.totalBudgetMin&&(v.nk.warning("最高预算必须大于最低预算"),r.totalBudgetMax=r.totalBudgetMin+1e3)},i=function(){var l=(0,h.A)((0,F.A)().m(function l(){return(0,F.A)().w(function(l){while(1)switch(l.n){case 0:if(t.value){l.n=1;break}return l.a(2);case 1:return l.n=2,t.value.validate(function(){var t=(0,h.A)((0,F.A)().m(function t(l){var n,u,s,o;return(0,F.A)().w(function(t){while(1)switch(t.n){case 0:if(!l){t.n=7;break}if(a.value=!0,t.p=1,n=D.A.getCurrentUser(),n){t.n=2;break}return(0,v.nk)({type:"error",message:"请先登录"}),e.push("/login"),t.a(2);case 2:return t.n=3,L.A.post("/security-tasks",r);case 3:u=t.v,u.data.success?_.s.alert('您的安全测试任务已成功发布，白帽子可以开始提交漏洞报告了。任务已显示在项目大厅中，您可以在"我的账户"中查看任务详情。',"任务发布成功",{confirmButtonText:"查看项目大厅",type:"success",callback:function(){e.push("/projects")}}):(0,v.nk)({type:"error",message:u.data.message||"发布任务失败"}),t.n=5;break;case 4:t.p=4,o=t.v,console.error("发布任务失败:",o),o.response&&o.response.data?(s=o.response.data,s.message&&s.message.includes("余额不足")?_.s.confirm("".concat(s.message,"，是否前往充值？"),"余额不足",{confirmButtonText:"前往充值",cancelButtonText:"取消",type:"warning"}).then(function(){e.push("/user/profile?tab=account")})["catch"](function(){}):(0,v.nk)({type:"error",message:s.message||"发布任务失败，请稍后重试"})):(0,v.nk)({type:"error",message:"发布任务失败，请稍后重试"});case 5:return t.p=5,a.value=!1,t.f(5);case 6:t.n=8;break;case 7:return(0,v.nk)({type:"warning",message:"请完善表单信息"}),t.a(2,!1);case 8:return t.a(2)}},t,null,[[1,4,5,6]])}));return function(e){return t.apply(this,arguments)}}());case 2:return l.a(2)}},l)}));return function(){return l.apply(this,arguments)}}(),d=function(){t.value&&t.value.resetFields()};return{taskFormRef:t,taskForm:r,rules:l,submitting:a,disablePastDates:n,disableInvalidEndDates:u,updateTotalBudget:s,validateTotalBudget:o,submitForm:i,resetForm:d}}};var R=a(1169);const y=(0,R.A)(M,[["render",f],["__scopeId","data-v-f7370f0a"]]),x=y}}]);