{"ast": null, "code": "import _regenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport { createRouter, createWebHistory } from 'vue-router';\nimport Home from '../views/Home.vue';\nimport Login from '../views/Login.vue';\nimport Register from '../views/Register.vue';\nimport NotFound from '../views/NotFound.vue';\nimport UserProfile from '../views/UserProfile.vue';\nimport Projects from '../views/Projects.vue';\nimport auth from '../utils/auth'; // Added import for auth utility\nimport AI from '../views/AI.vue';\nimport Ranking from '../views/Ranking.vue';\nvar routes = [{\n  path: '/',\n  name: 'Home',\n  component: Home,\n  meta: {\n    title: '首页 - 衍盾星云'\n  }\n}, {\n  path: '/login',\n  name: 'Login',\n  component: Login,\n  meta: {\n    title: '登录 - 衍盾星云'\n  }\n}, {\n  path: '/register',\n  name: 'Register',\n  component: Register,\n  meta: {\n    title: '注册 - 衍盾星云'\n  }\n}, {\n  path: '/user/profile',\n  name: 'UserProfile',\n  component: UserProfile,\n  meta: {\n    title: '个人中心 - 衍盾星云',\n    requiresAuth: true\n  }\n},\n// 添加项目大厅路由\n{\n  path: '/projects',\n  name: 'Projects',\n  component: Projects,\n  meta: {\n    title: '项目大厅 - 衍盾星云'\n  }\n},\n// 添加项目详情页路由\n{\n  path: '/projects/:id',\n  name: 'ProjectDetail',\n  component: function component() {\n    return import('../views/ProjectDetail.vue');\n  },\n  meta: {\n    title: '项目详情 - 衍盾星云'\n  }\n},\n// 添加项目参与页路由\n{\n  path: '/projects/:id/participate',\n  name: 'ProjectParticipate',\n  component: function component() {\n    return import('../views/ProjectParticipate.vue');\n  },\n  meta: {\n    title: '参与项目 - 衍盾星云',\n    requiresAuth: true\n  }\n}, {\n  path: '/user/submissions',\n  name: 'UserSubmissions',\n  component: UserProfile,\n  props: {\n    activeTab: 'submissions'\n  },\n  meta: {\n    title: '我的提交 - 衍盾星云',\n    requiresAuth: true\n  }\n}, {\n  path: '/user/rewards',\n  name: 'UserRewards',\n  component: UserProfile,\n  props: {\n    activeTab: 'rewards'\n  },\n  meta: {\n    title: '我的奖励 - 衍盾星云',\n    requiresAuth: true\n  }\n}, {\n  path: '/submit',\n  name: 'SubmitVulnerability',\n  component: function component() {\n    return import('../views/SubmitVulnerability.vue');\n  },\n  meta: {\n    title: '提交漏洞 - 衍盾星云',\n    requiresAuth: true,\n    requiresUserType: 'whiteHat'\n  }\n}, {\n  path: '/enterprise/publish',\n  name: 'EnterprisePublish',\n  component: function component() {\n    return import('../views/enterprise/PublishTask.vue');\n  },\n  meta: {\n    title: '任务发布 - 衍盾星云',\n    requiresAuth: true,\n    requiresUserType: 'enterprise'\n  }\n}, {\n  path: '/enterprise/review',\n  name: 'EnterpriseReview',\n  component: function component() {\n    return import('../views/enterprise/ReviewTask.vue');\n  },\n  meta: {\n    title: '任务审核 - 衍盾星云',\n    requiresAuth: true,\n    requiresUserType: 'enterprise'\n  }\n}, {\n  path: '/ai',\n  name: 'AI',\n  component: AI,\n  meta: {\n    title: 'AI问答 - 衍盾星云'\n  }\n}, {\n  path: '/query',\n  name: 'QueryCenter',\n  component: function component() {\n    return import('../views/QueryCenter.vue');\n  },\n  meta: {\n    title: '查询中心 - 衍盾星云'\n  }\n}, {\n  path: '/ranking',\n  name: 'Ranking',\n  component: Ranking,\n  meta: {\n    title: '排行榜 - 衍盾星云'\n  }\n}, {\n  path: '/activities',\n  name: 'Activities',\n  component: function component() {\n    return import('../views/Activities.vue');\n  },\n  meta: {\n    title: '活动中心 - 衍盾星云'\n  }\n},\n// 添加活动详情页路由\n{\n  path: '/activities/:id',\n  name: 'ActivityDetail',\n  component: function component() {\n    return import('../views/ActivityDetail.vue');\n  },\n  meta: {\n    title: '活动详情 - 衍盾星云'\n  }\n},\n// 添加社区相关路由\n{\n  path: '/community',\n  name: 'Community',\n  component: function component() {\n    return import('../views/Community.vue');\n  },\n  meta: {\n    title: '交流中心 - 衍盾星云'\n  }\n}, {\n  path: '/community/posts/:id',\n  name: 'PostDetail',\n  component: function component() {\n    return import('../views/PostDetail.vue');\n  },\n  meta: {\n    title: '帖子详情 - 衍盾星云'\n  }\n}, {\n  path: '/help',\n  name: 'Help',\n  component: function component() {\n    return import('../views/Help.vue');\n  },\n  meta: {\n    title: '帮助中心 - 衍盾星云'\n  }\n},\n// 管理员相关路由\n{\n  path: '/admin/login',\n  name: 'AdminLogin',\n  component: function component() {\n    return import('../views/AdminLogin.vue');\n  },\n  meta: {\n    title: '管理员登录 - 衍盾星云'\n  }\n}, {\n  path: '/admin',\n  component: function component() {\n    return import('../layouts/AdminLayout.vue');\n  },\n  meta: {\n    requiresAuth: true,\n    requiresUserType: 'admin'\n  },\n  children: [{\n    path: 'dashboard',\n    name: 'AdminDashboard',\n    component: function component() {\n      return import('../views/admin/Dashboard.vue');\n    },\n    meta: {\n      title: '管理后台 - 衍盾星云'\n    }\n  }, {\n    path: 'users',\n    name: 'AdminUsers',\n    component: function component() {\n      return import('../views/admin/UserManagement.vue');\n    },\n    meta: {\n      title: '用户管理 - 衍盾星云'\n    }\n  }, {\n    path: 'posts',\n    name: 'AdminPosts',\n    component: function component() {\n      return import('../views/admin/PostManagement.vue');\n    },\n    meta: {\n      title: '帖子管理 - 衍盾星云'\n    }\n  }, {\n    path: 'projects',\n    name: 'AdminProjects',\n    component: function component() {\n      return import('../views/admin/ProjectManagement.vue');\n    },\n    meta: {\n      title: '项目管理 - 衍盾星云'\n    }\n  }, {\n    path: 'announcements',\n    name: 'AdminAnnouncements',\n    component: function component() {\n      return import('../views/admin/AnnouncementManagement.vue');\n    },\n    meta: {\n      title: '公告管理 - 衍盾星云'\n    }\n  }, {\n    path: 'disputes',\n    name: 'AdminDisputes',\n    component: function component() {\n      return import('../views/admin/DisputeManagement.vue');\n    },\n    meta: {\n      title: '争议审核 - 衍盾星云'\n    }\n  }, {\n    path: 'settings',\n    name: 'AdminSettings',\n    component: function component() {\n      return import('../views/admin/SystemSettings.vue');\n    },\n    meta: {\n      title: '系统设置 - 衍盾星云'\n    }\n  }]\n}, {\n  path: '/:pathMatch(.*)*',\n  name: 'NotFound',\n  component: NotFound,\n  meta: {\n    title: '页面未找到 - 衍盾星云'\n  }\n}];\nvar router = createRouter({\n  // 使用 Hash 模式（不需要服务器特殊配置）\n  history: createWebHashHistory(),\n  // 如果服务器配置好了，可以使用 History 模式\n  // history: createWebHistory(),\n  routes: routes,\n  scrollBehavior: function scrollBehavior() {\n    return {\n      top: 0\n    };\n  }\n});\n\n// 全局导航守卫\nrouter.beforeEach(/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(to, from, next) {\n    var currentUser;\n    return _regenerator().w(function (_context) {\n      while (1) switch (_context.n) {\n        case 0:\n          // 设置页面标题\n          document.title = to.meta.title || '衍盾星云 - 基于区块链的漏洞悬赏智能响应平台';\n\n          // 检查是否需要登录权限\n          if (!to.meta.requiresAuth) {\n            _context.n = 3;\n            break;\n          }\n          if (auth.isLoggedIn()) {\n            _context.n = 1;\n            break;\n          }\n          // 未登录，重定向到登录页\n          next({\n            path: '/login',\n            query: {\n              redirect: to.fullPath\n            }\n          });\n          return _context.a(2);\n        case 1:\n          // 异步验证登录状态，但不阻塞路由跳转\n          auth.validateLoginStatus()[\"catch\"](function (error) {\n            console.error('路由守卫验证登录状态失败:', error);\n          });\n\n          // 检查用户类型权限\n          if (!to.meta.requiresUserType) {\n            _context.n = 2;\n            break;\n          }\n          currentUser = auth.getCurrentUser();\n          if (!(!currentUser || currentUser.userType !== to.meta.requiresUserType)) {\n            _context.n = 2;\n            break;\n          }\n          // 用户类型不匹配，重定向到首页\n          next({\n            path: '/'\n          });\n          return _context.a(2);\n        case 2:\n          next();\n          _context.n = 4;\n          break;\n        case 3:\n          next();\n        case 4:\n          return _context.a(2);\n      }\n    }, _callee);\n  }));\n  return function (_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}());\nexport default router;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}