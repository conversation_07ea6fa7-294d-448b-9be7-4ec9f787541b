"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[193],{12193:(e,n,t)=>{t.r(n),t.d(n,{default:()=>S});var l=t(24059),u=t(698),a=t(95976),r=t(10160),o=t(12040),i=t(18057),s=t(30578),c={class:"system-settings"},d={class:"page-header"},m={class:"settings-container"},f={class:"monitor-panel"},p={class:"monitor-grid"},k={class:"monitor-card"},b={class:"monitor-item"},_={class:"monitor-item"},v={class:"monitor-item"},V={class:"monitor-card"},h={class:"monitor-item"},F={class:"monitor-item"},L={class:"monitor-card"},w={class:"monitor-item"},g={class:"monitor-item"},y={class:"monitor-item"},x={class:"monitor-actions"};const U={__name:"SystemSettings",setup:function(e){var n=(0,o.KR)("basic"),t=(0,o.Kh)({siteName:"衍盾星云",siteDescription:"基于区块链的漏洞悬赏智能响应平台",contactEmail:"<EMAIL>",servicePhone:"************",siteEnabled:!0}),U=(0,o.Kh)({registrationEnabled:!0,emailVerification:!0,minPasswordLength:8,maxLoginAttempts:5,sessionTimeout:120}),A=(0,o.Kh)({smtpHost:"smtp.qq.com",smtpPort:587,fromEmail:"",emailPassword:"",useSSL:!0}),P=(0,o.Kh)({os:"Windows 10",nodeVersion:"v16.14.0",uptime:"2天 5小时 30分钟",dbSize:"125.6 MB",dbConnections:8,memoryUsage:"256 MB / 1 GB",cpuUsage:"15%",diskUsage:"45.2 GB / 100 GB"}),S=function(){var e=(0,u.A)((0,l.A)().m(function e(){return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:try{i.nk.success("基础设置保存成功")}catch(n){console.error("保存基础设置失败:",n),i.nk.error("保存失败")}case 1:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}(),C=function(){var e=(0,u.A)((0,l.A)().m(function e(){return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:try{i.nk.success("安全设置保存成功")}catch(n){console.error("保存安全设置失败:",n),i.nk.error("保存失败")}case 1:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}(),E=function(){var e=(0,u.A)((0,l.A)().m(function e(){return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:try{i.nk.success("邮件设置保存成功")}catch(n){console.error("保存邮件设置失败:",n),i.nk.error("保存失败")}case 1:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}(),T=function(){var e=(0,u.A)((0,l.A)().m(function e(){return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:try{i.nk.success("测试邮件发送成功")}catch(n){console.error("测试邮件失败:",n),i.nk.error("测试邮件失败")}case 1:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}(),B=function(){i.nk.success("系统信息已刷新")},W=function(){var e=(0,u.A)((0,l.A)().m(function e(){var n;return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要清理系统缓存吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:i.nk.success("缓存清理成功"),e.n=3;break;case 2:e.p=2,n=e.v,"cancel"!==n&&i.nk.error("缓存清理失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),K=function(){var e=(0,u.A)((0,l.A)().m(function e(){var n;return(0,l.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要重启服务吗？这将导致短暂的服务中断。","确认重启",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:i.nk.success("服务重启指令已发送"),e.n=3;break;case 2:e.p=2,n=e.v,"cancel"!==n&&i.nk.error("重启服务失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}();return(0,a.sV)(function(){}),function(e,l){var u=(0,a.g2)("el-button"),o=(0,a.g2)("el-input"),i=(0,a.g2)("el-form-item"),s=(0,a.g2)("el-switch"),M=(0,a.g2)("el-form"),N=(0,a.g2)("el-tab-pane"),R=(0,a.g2)("el-input-number"),D=(0,a.g2)("el-tabs");return(0,a.uX)(),(0,a.CE)("div",c,[(0,a.Lk)("div",d,[l[18]||(l[18]=(0,a.Lk)("h1",null,"系统设置",-1)),(0,a.bF)(u,{onClick:l[0]||(l[0]=function(n){return e.$router.go(-1)})},{default:(0,a.k6)(function(){return l[17]||(l[17]=[(0,a.eW)("返回")])}),_:1,__:[17]})]),(0,a.Lk)("div",m,[(0,a.bF)(D,{modelValue:n.value,"onUpdate:modelValue":l[16]||(l[16]=function(e){return n.value=e}),type:"card"},{default:(0,a.k6)(function(){return[(0,a.bF)(N,{label:"基础设置",name:"basic"},{default:(0,a.k6)(function(){return[(0,a.bF)(M,{ref:"basicFormRef",model:t,"label-width":"120px",class:"settings-form"},{default:(0,a.k6)(function(){return[(0,a.bF)(i,{label:"网站名称"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:t.siteName,"onUpdate:modelValue":l[1]||(l[1]=function(e){return t.siteName=e}),placeholder:"请输入网站名称"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"网站描述"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:t.siteDescription,"onUpdate:modelValue":l[2]||(l[2]=function(e){return t.siteDescription=e}),type:"textarea",rows:3,placeholder:"请输入网站描述"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"联系邮箱"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:t.contactEmail,"onUpdate:modelValue":l[3]||(l[3]=function(e){return t.contactEmail=e}),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"客服电话"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:t.servicePhone,"onUpdate:modelValue":l[4]||(l[4]=function(e){return t.servicePhone=e}),placeholder:"请输入客服电话"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"网站状态"},{default:(0,a.k6)(function(){return[(0,a.bF)(s,{modelValue:t.siteEnabled,"onUpdate:modelValue":l[5]||(l[5]=function(e){return t.siteEnabled=e}),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,null,{default:(0,a.k6)(function(){return[(0,a.bF)(u,{type:"primary",onClick:S},{default:(0,a.k6)(function(){return l[19]||(l[19]=[(0,a.eW)("保存设置")])}),_:1,__:[19]})]}),_:1})]}),_:1},8,["model"])]}),_:1}),(0,a.bF)(N,{label:"安全设置",name:"security"},{default:(0,a.k6)(function(){return[(0,a.bF)(M,{ref:"securityFormRef",model:U,"label-width":"120px",class:"settings-form"},{default:(0,a.k6)(function(){return[(0,a.bF)(i,{label:"注册开关"},{default:(0,a.k6)(function(){return[(0,a.bF)(s,{modelValue:U.registrationEnabled,"onUpdate:modelValue":l[6]||(l[6]=function(e){return U.registrationEnabled=e}),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"邮箱验证"},{default:(0,a.k6)(function(){return[(0,a.bF)(s,{modelValue:U.emailVerification,"onUpdate:modelValue":l[7]||(l[7]=function(e){return U.emailVerification=e}),"active-text":"必须","inactive-text":"可选"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"密码最小长度"},{default:(0,a.k6)(function(){return[(0,a.bF)(R,{modelValue:U.minPasswordLength,"onUpdate:modelValue":l[8]||(l[8]=function(e){return U.minPasswordLength=e}),min:6,max:20},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"登录失败限制"},{default:(0,a.k6)(function(){return[(0,a.bF)(R,{modelValue:U.maxLoginAttempts,"onUpdate:modelValue":l[9]||(l[9]=function(e){return U.maxLoginAttempts=e}),min:3,max:10},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"会话超时(分钟)"},{default:(0,a.k6)(function(){return[(0,a.bF)(R,{modelValue:U.sessionTimeout,"onUpdate:modelValue":l[10]||(l[10]=function(e){return U.sessionTimeout=e}),min:30,max:1440},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,null,{default:(0,a.k6)(function(){return[(0,a.bF)(u,{type:"primary",onClick:C},{default:(0,a.k6)(function(){return l[20]||(l[20]=[(0,a.eW)("保存设置")])}),_:1,__:[20]})]}),_:1})]}),_:1},8,["model"])]}),_:1}),(0,a.bF)(N,{label:"邮件设置",name:"email"},{default:(0,a.k6)(function(){return[(0,a.bF)(M,{ref:"emailFormRef",model:A,"label-width":"120px",class:"settings-form"},{default:(0,a.k6)(function(){return[(0,a.bF)(i,{label:"SMTP服务器"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:A.smtpHost,"onUpdate:modelValue":l[11]||(l[11]=function(e){return A.smtpHost=e}),placeholder:"请输入SMTP服务器地址"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"SMTP端口"},{default:(0,a.k6)(function(){return[(0,a.bF)(R,{modelValue:A.smtpPort,"onUpdate:modelValue":l[12]||(l[12]=function(e){return A.smtpPort=e}),min:1,max:65535},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"发送邮箱"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:A.fromEmail,"onUpdate:modelValue":l[13]||(l[13]=function(e){return A.fromEmail=e}),placeholder:"请输入发送邮箱"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"邮箱密码"},{default:(0,a.k6)(function(){return[(0,a.bF)(o,{modelValue:A.emailPassword,"onUpdate:modelValue":l[14]||(l[14]=function(e){return A.emailPassword=e}),type:"password",placeholder:"请输入邮箱密码","show-password":""},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,{label:"SSL加密"},{default:(0,a.k6)(function(){return[(0,a.bF)(s,{modelValue:A.useSSL,"onUpdate:modelValue":l[15]||(l[15]=function(e){return A.useSSL=e}),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]}),_:1}),(0,a.bF)(i,null,{default:(0,a.k6)(function(){return[(0,a.bF)(u,{type:"primary",onClick:E},{default:(0,a.k6)(function(){return l[21]||(l[21]=[(0,a.eW)("保存设置")])}),_:1,__:[21]}),(0,a.bF)(u,{onClick:T},{default:(0,a.k6)(function(){return l[22]||(l[22]=[(0,a.eW)("测试邮件")])}),_:1,__:[22]})]}),_:1})]}),_:1},8,["model"])]}),_:1}),(0,a.bF)(N,{label:"系统监控",name:"monitor"},{default:(0,a.k6)(function(){return[(0,a.Lk)("div",f,[(0,a.Lk)("div",p,[(0,a.Lk)("div",k,[l[26]||(l[26]=(0,a.Lk)("h3",null,"服务器信息",-1)),(0,a.Lk)("div",b,[l[23]||(l[23]=(0,a.Lk)("span",null,"操作系统:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.os),1)]),(0,a.Lk)("div",_,[l[24]||(l[24]=(0,a.Lk)("span",null,"Node.js版本:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.nodeVersion),1)]),(0,a.Lk)("div",v,[l[25]||(l[25]=(0,a.Lk)("span",null,"运行时间:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.uptime),1)])]),(0,a.Lk)("div",V,[l[29]||(l[29]=(0,a.Lk)("h3",null,"数据库状态",-1)),l[30]||(l[30]=(0,a.Lk)("div",{class:"monitor-item"},[(0,a.Lk)("span",null,"连接状态:"),(0,a.Lk)("span",{class:"status-online"},"正常")],-1)),(0,a.Lk)("div",h,[l[27]||(l[27]=(0,a.Lk)("span",null,"数据库大小:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.dbSize),1)]),(0,a.Lk)("div",F,[l[28]||(l[28]=(0,a.Lk)("span",null,"活跃连接:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.dbConnections),1)])]),(0,a.Lk)("div",L,[l[34]||(l[34]=(0,a.Lk)("h3",null,"性能指标",-1)),(0,a.Lk)("div",w,[l[31]||(l[31]=(0,a.Lk)("span",null,"内存使用:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.memoryUsage),1)]),(0,a.Lk)("div",g,[l[32]||(l[32]=(0,a.Lk)("span",null,"CPU使用率:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.cpuUsage),1)]),(0,a.Lk)("div",y,[l[33]||(l[33]=(0,a.Lk)("span",null,"磁盘使用:",-1)),(0,a.Lk)("span",null,(0,r.v_)(P.diskUsage),1)])])]),(0,a.Lk)("div",x,[(0,a.bF)(u,{type:"primary",onClick:B},{default:(0,a.k6)(function(){return l[35]||(l[35]=[(0,a.eW)("刷新信息")])}),_:1,__:[35]}),(0,a.bF)(u,{type:"warning",onClick:W},{default:(0,a.k6)(function(){return l[36]||(l[36]=[(0,a.eW)("清理缓存")])}),_:1,__:[36]}),(0,a.bF)(u,{type:"danger",onClick:K},{default:(0,a.k6)(function(){return l[37]||(l[37]=[(0,a.eW)("重启服务")])}),_:1,__:[37]})])])]}),_:1})]}),_:1},8,["modelValue"])])])}}};var A=t(1169);const P=(0,A.A)(U,[["__scopeId","data-v-3be2ea85"]]),S=P}}]);