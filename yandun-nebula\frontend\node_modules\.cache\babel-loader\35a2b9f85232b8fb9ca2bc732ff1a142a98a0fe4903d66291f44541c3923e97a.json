{"ast": null, "code": "import _regenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _toConsumableArray from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _asyncToGenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport { ref, computed, onMounted, watch } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { Link as LinkIcon } from '@element-plus/icons-vue';\nimport AppFooter from '@/components/AppFooter.vue';\nimport TheHeader from '@/components/TheHeader.vue';\nexport default {\n  name: 'Activities',\n  components: {\n    TheHeader: TheHeader,\n    AppFooter: AppFooter,\n    LinkIcon: LinkIcon\n  },\n  setup: function setup() {\n    var router = useRouter();\n    var activities = ref([]);\n    var loading = ref(false);\n    var activeTab = ref('all');\n    var searchKeyword = ref('');\n    var currentPage = ref(1);\n    var pageSize = ref(9);\n    var total = ref(0);\n\n    // 获取置顶/推荐活动作为轮播图展示\n    var featuredActivities = computed(function () {\n      return activities.value.filter(function (activity) {\n        return activity.featured || activity.isSticky;\n      }).slice(0, 5);\n    });\n\n    // 根据筛选条件过滤活动\n    var filteredActivities = computed(function () {\n      return activities.value; // 直接返回activities，因为过滤逻辑已经在fetchActivities中处理\n    });\n    var totalPages = computed(function () {\n      return Math.ceil(total.value / pageSize.value);\n    });\n    var fetchActivities = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var announcementResponse, announcements, announcementData, activityResponse, activityData, activityResult, combinedData, filteredData, keyword, startIndex, endIndex, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              loading.value = true;\n              _context.p = 1;\n              _context.n = 2;\n              return fetch('/api/announcements', {\n                method: 'GET',\n                headers: {\n                  'Content-Type': 'application/json'\n                }\n              });\n            case 2:\n              announcementResponse = _context.v;\n              announcements = [];\n              if (!announcementResponse.ok) {\n                _context.n = 4;\n                break;\n              }\n              _context.n = 3;\n              return announcementResponse.json();\n            case 3:\n              announcementData = _context.v;\n              if (announcementData.success) {\n                announcements = announcementData.data.announcements || [];\n              }\n            case 4:\n              _context.n = 5;\n              return fetch('/api/activities', {\n                method: 'GET',\n                headers: {\n                  'Content-Type': 'application/json'\n                }\n              });\n            case 5:\n              activityResponse = _context.v;\n              activityData = [];\n              if (!activityResponse.ok) {\n                _context.n = 7;\n                break;\n              }\n              _context.n = 6;\n              return activityResponse.json();\n            case 6:\n              activityResult = _context.v;\n              if (activityResult.code === 0) {\n                activityData = activityResult.data || [];\n              }\n            case 7:\n              // 合并公告和活动数据\n              combinedData = [].concat(_toConsumableArray(announcements.map(function (announcement) {\n                return {\n                  id: \"announcement_\".concat(announcement.id),\n                  title: announcement.title,\n                  description: announcement.content,\n                  status: 'ongoing',\n                  // 已发布的公告默认为进行中\n                  type: 'announcement',\n                  featured: announcement.isSticky,\n                  isSticky: announcement.isSticky,\n                  bannerImage: require('@/assets/logo.png'),\n                  coverImage: require('@/assets/logo.png'),\n                  startTime: new Date(announcement.publishedAt),\n                  endTime: new Date(announcement.publishedAt),\n                  views: announcement.viewCount || 0,\n                  priority: announcement.priority || 0,\n                  announcementType: announcement.type\n                };\n              })), _toConsumableArray(activityData.map(function (activity) {\n                return {\n                  id: \"activity_\".concat(activity.id),\n                  title: activity.name,\n                  description: activity.description,\n                  status: activity.status,\n                  type: 'event',\n                  featured: false,\n                  isSticky: false,\n                  bannerImage: require('@/assets/logo.png'),\n                  coverImage: require('@/assets/logo.png'),\n                  startTime: new Date(activity.startDate),\n                  endTime: new Date(activity.endDate),\n                  views: 0,\n                  location: '线上',\n                  participantCount: 0\n                };\n              }))); // 应用筛选条件\n              filteredData = combinedData; // 按类型筛选\n              if (activeTab.value !== 'all') {\n                if (activeTab.value === 'event') {\n                  // 活动类型\n                  filteredData = filteredData.filter(function (item) {\n                    return item.type === 'event';\n                  });\n                } else {\n                  // 公告类型（system, maintenance, important）\n                  filteredData = filteredData.filter(function (item) {\n                    return item.type === 'announcement' && item.announcementType === activeTab.value;\n                  });\n                }\n              }\n\n              // 按关键词搜索\n              if (searchKeyword.value) {\n                keyword = searchKeyword.value.toLowerCase();\n                filteredData = filteredData.filter(function (item) {\n                  return item.title.toLowerCase().includes(keyword) || item.description.toLowerCase().includes(keyword);\n                });\n              }\n\n              // 排序：置顶优先，然后按优先级和时间排序\n              filteredData.sort(function (a, b) {\n                if (a.isSticky !== b.isSticky) {\n                  return b.isSticky - a.isSticky;\n                }\n                if (a.priority !== b.priority) {\n                  return b.priority - a.priority;\n                }\n                return new Date(b.startTime) - new Date(a.startTime);\n              });\n\n              // 分页\n              startIndex = (currentPage.value - 1) * pageSize.value;\n              endIndex = startIndex + pageSize.value;\n              activities.value = filteredData.slice(startIndex, endIndex);\n              total.value = filteredData.length;\n              _context.n = 9;\n              break;\n            case 8:\n              _context.p = 8;\n              _t = _context.v;\n              console.error('获取活动列表失败:', _t);\n              ElMessage.error('获取活动列表失败，请稍后再试');\n              activities.value = [];\n              total.value = 0;\n            case 9:\n              _context.p = 9;\n              loading.value = false;\n              return _context.f(9);\n            case 10:\n              return _context.a(2);\n          }\n        }, _callee, null, [[1, 8, 9, 10]]);\n      }));\n      return function fetchActivities() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    var formatDate = function formatDate(date) {\n      if (!date) return '';\n      var d = new Date(date);\n      return \"\".concat(d.getFullYear(), \"-\").concat(String(d.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(d.getDate()).padStart(2, '0'));\n    };\n    var getTypeClass = function getTypeClass(type, announcementType) {\n      if (type === 'event') {\n        return 'type-event';\n      } else if (type === 'announcement') {\n        // 根据公告类型返回不同的样式类\n        switch (announcementType) {\n          case 'system':\n            return 'type-system';\n          case 'event':\n            return 'type-event-announcement';\n          case 'maintenance':\n            return 'type-maintenance';\n          case 'important':\n            return 'type-important';\n          default:\n            return 'type-announcement';\n        }\n      }\n      return '';\n    };\n    var getTypeText = function getTypeText(type, announcementType) {\n      if (type === 'event') {\n        return '活动';\n      } else if (type === 'announcement') {\n        // 根据公告类型返回具体的分类名称\n        switch (announcementType) {\n          case 'system':\n            return '系统公告';\n          case 'activity':\n            return '活动公告';\n          case 'maintenance':\n            return '维护公告';\n          case 'important':\n            return '重要通知';\n          default:\n            return '公告';\n        }\n      }\n      return '';\n    };\n    var viewActivityDetail = function viewActivityDetail(id) {\n      // 这里应该导航到活动详情页面\n      router.push(\"/activities/\".concat(id));\n    };\n    var handleTabChange = function handleTabChange() {\n      currentPage.value = 1;\n      applyFilters();\n    };\n    var applyFilters = function applyFilters() {\n      currentPage.value = 1;\n      fetchActivities();\n    };\n    var handlePageChange = function handlePageChange(page) {\n      currentPage.value = page;\n      fetchActivities();\n    };\n\n    // 监听筛选条件变化\n    watch([activeTab, searchKeyword], function () {\n      applyFilters();\n    });\n    onMounted(function () {\n      fetchActivities();\n    });\n    return {\n      activities: activities,\n      loading: loading,\n      activeTab: activeTab,\n      searchKeyword: searchKeyword,\n      currentPage: currentPage,\n      pageSize: pageSize,\n      total: total,\n      totalPages: totalPages,\n      featuredActivities: featuredActivities,\n      filteredActivities: filteredActivities,\n      formatDate: formatDate,\n      getTypeClass: getTypeClass,\n      getTypeText: getTypeText,\n      viewActivityDetail: viewActivityDetail,\n      handleTabChange: handleTabChange,\n      applyFilters: applyFilters,\n      handlePageChange: handlePageChange\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}