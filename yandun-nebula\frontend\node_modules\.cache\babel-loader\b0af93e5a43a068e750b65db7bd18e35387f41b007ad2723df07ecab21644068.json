{"ast": null, "code": "import \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"activity-detail-container\"\n};\nvar _hoisted_2 = {\n  \"class\": \"activity-detail-content\"\n};\nvar _hoisted_3 = {\n  \"class\": \"breadcrumb\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  \"class\": \"activity-content\"\n};\nvar _hoisted_5 = {\n  \"class\": \"activity-header\"\n};\nvar _hoisted_6 = {\n  \"class\": \"activity-info\"\n};\nvar _hoisted_7 = {\n  \"class\": \"title-section\"\n};\nvar _hoisted_8 = {\n  \"class\": \"activity-title\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  \"class\": \"blockchain-badge\"\n};\nvar _hoisted_10 = {\n  \"class\": \"activity-meta\"\n};\nvar _hoisted_11 = {\n  \"class\": \"meta-item\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  \"class\": \"meta-item\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  \"class\": \"blockchain-verification\"\n};\nvar _hoisted_14 = {\n  \"class\": \"verification-header\"\n};\nvar _hoisted_15 = {\n  \"class\": \"verification-icon\"\n};\nvar _hoisted_16 = {\n  \"class\": \"verification-status\"\n};\nvar _hoisted_17 = {\n  \"class\": \"verification-details\"\n};\nvar _hoisted_18 = {\n  \"class\": \"detail-row\"\n};\nvar _hoisted_19 = {\n  \"class\": \"detail-item\"\n};\nvar _hoisted_20 = {\n  \"class\": \"detail-icon\"\n};\nvar _hoisted_21 = {\n  \"class\": \"detail-content\"\n};\nvar _hoisted_22 = {\n  \"class\": \"detail-value\"\n};\nvar _hoisted_23 = {\n  \"class\": \"detail-row\"\n};\nvar _hoisted_24 = {\n  \"class\": \"detail-item\"\n};\nvar _hoisted_25 = {\n  \"class\": \"detail-icon\"\n};\nvar _hoisted_26 = {\n  \"class\": \"detail-content\"\n};\nvar _hoisted_27 = {\n  \"class\": \"detail-value\"\n};\nvar _hoisted_28 = {\n  \"class\": \"detail-item\"\n};\nvar _hoisted_29 = {\n  \"class\": \"detail-icon\"\n};\nvar _hoisted_30 = {\n  \"class\": \"detail-content\"\n};\nvar _hoisted_31 = {\n  \"class\": \"detail-value\"\n};\nvar _hoisted_32 = {\n  \"class\": \"time-text\"\n};\nvar _hoisted_33 = {\n  \"class\": \"activity-section\"\n};\nvar _hoisted_34 = {\n  \"class\": \"section-title\"\n};\nvar _hoisted_35 = [\"innerHTML\"];\nvar _hoisted_36 = {\n  key: 1,\n  \"class\": \"activity-section\"\n};\nvar _hoisted_37 = {\n  \"class\": \"section-content\"\n};\nvar _hoisted_38 = {\n  \"class\": \"related-link-display\"\n};\nvar _hoisted_39 = {\n  key: 2,\n  \"class\": \"activity-section\"\n};\nvar _hoisted_40 = {\n  \"class\": \"section-content\"\n};\nvar _hoisted_41 = {\n  \"class\": \"cloud-link-display\"\n};\nvar _hoisted_42 = {\n  key: 3,\n  \"class\": \"activity-section\"\n};\nvar _hoisted_43 = [\"innerHTML\"];\nvar _hoisted_44 = {\n  key: 4,\n  \"class\": \"activity-section\"\n};\nvar _hoisted_45 = {\n  \"class\": \"section-content\"\n};\nvar _hoisted_46 = {\n  \"class\": \"rewards-list\"\n};\nvar _hoisted_47 = {\n  \"class\": \"reward-rank\"\n};\nvar _hoisted_48 = {\n  \"class\": \"reward-amount\"\n};\nvar _hoisted_49 = {\n  \"class\": \"activity-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_TheHeader = _resolveComponent(\"TheHeader\");\n  var _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  var _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  var _component_Check = _resolveComponent(\"Check\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_tag = _resolveComponent(\"el-tag\");\n  var _component_Document = _resolveComponent(\"Document\");\n  var _component_el_text = _resolveComponent(\"el-text\");\n  var _component_Key = _resolveComponent(\"Key\");\n  var _component_Clock = _resolveComponent(\"Clock\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_AppFooter = _resolveComponent(\"AppFooter\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_TheHeader), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_breadcrumb, {\n    separator: \"/\"\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_el_breadcrumb_item, {\n        to: {\n          path: '/'\n        }\n      }, {\n        \"default\": _withCtx(function () {\n          return _cache[0] || (_cache[0] = [_createTextVNode(\"首页\")]);\n        }),\n        _: 1,\n        __: [0]\n      }), _createVNode(_component_el_breadcrumb_item, {\n        to: {\n          path: '/activities'\n        }\n      }, {\n        \"default\": _withCtx(function () {\n          return _cache[1] || (_cache[1] = [_createTextVNode(\"活动中心\")]);\n        }),\n        _: 1,\n        __: [1]\n      }), _createVNode(_component_el_breadcrumb_item, null, {\n        \"default\": _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"活动详情\")]);\n        }),\n        _: 1,\n        __: [2]\n      })];\n    }),\n    _: 1\n  })]), $setup.activity ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h1\", _hoisted_8, _toDisplayString($setup.activity.title), 1), $setup.activity.transactionHash ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_icon, null, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_Check)];\n    }),\n    _: 1\n  }), _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"区块链认证\", -1))])) : _createCommentVNode(\"\", true)]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[4] || (_cache[4] = _createElementVNode(\"i\", {\n    \"class\": \"el-icon-time\"\n  }, null, -1)), _createElementVNode(\"span\", null, \"发布时间：\" + _toDisplayString($setup.formatDateTime($setup.activity.startTime)), 1)]), $setup.activity.location ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_cache[5] || (_cache[5] = _createElementVNode(\"i\", {\n    \"class\": \"el-icon-location\"\n  }, null, -1)), _createElementVNode(\"span\", null, _toDisplayString($setup.activity.location), 1)])) : _createCommentVNode(\"\", true)])])]), $setup.activity.transactionHash ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_Check)];\n    }),\n    _: 1\n  })]), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    \"class\": \"verification-content\"\n  }, [_createElementVNode(\"h3\", {\n    \"class\": \"verification-title\"\n  }, \"区块链认证\"), _createElementVNode(\"p\", {\n    \"class\": \"verification-desc\"\n  }, \"此内容已通过区块链技术认证，确保信息的真实性和不可篡改性\")], -1)), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_tag, {\n    type: \"success\",\n    size: \"large\",\n    effect: \"dark\"\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_el_icon, null, {\n        \"default\": _withCtx(function () {\n          return [_createVNode(_component_Check)];\n        }),\n        _: 1\n      }), _cache[6] || (_cache[6] = _createTextVNode(\" 已认证 \"))];\n    }),\n    _: 1,\n    __: [6]\n  })])]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_icon, null, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_Document)];\n    }),\n    _: 1\n  })]), _createElementVNode(\"div\", _hoisted_21, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n    \"class\": \"detail-label\"\n  }, \"交易哈希\", -1)), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_text, {\n    \"class\": \"hash-text\",\n    copyable: \"\"\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createTextVNode(_toDisplayString($setup.activity.transactionHash), 1)];\n    }),\n    _: 1\n  })])])])]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_el_icon, null, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_Key)];\n    }),\n    _: 1\n  })]), _createElementVNode(\"div\", _hoisted_26, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n    \"class\": \"detail-label\"\n  }, \"区块哈希\", -1)), _createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_text, {\n    \"class\": \"hash-text\",\n    copyable: \"\"\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createTextVNode(_toDisplayString($setup.activity.blockHash), 1)];\n    }),\n    _: 1\n  })])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_icon, null, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_Clock)];\n    }),\n    _: 1\n  })]), _createElementVNode(\"div\", _hoisted_30, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n    \"class\": \"detail-label\"\n  }, \"认证时间\", -1)), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"span\", _hoisted_32, _toDisplayString($setup.formatBlockchainTime($setup.activity.blockchainTimestamp)), 1)])])])])])])) : _createCommentVNode(\"\", true), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"h2\", _hoisted_34, _toDisplayString($setup.activity.type === 'announcement' ? '公告内容' : '活动介绍'), 1), _createElementVNode(\"div\", {\n    \"class\": \"section-content\",\n    innerHTML: $setup.activity.description\n  }, null, 8, _hoisted_35)]), $setup.activity.relatedLink ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_cache[12] || (_cache[12] = _createElementVNode(\"h2\", {\n    \"class\": \"section-title\"\n  }, \"相关链接\", -1)), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[11] || (_cache[11] = _createElementVNode(\"i\", {\n    \"class\": \"el-icon-link\"\n  }, null, -1)), _createTextVNode(\" \" + _toDisplayString($setup.activity.relatedLink), 1)])])])) : _createCommentVNode(\"\", true), $setup.activity.cloudLink ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_cache[14] || (_cache[14] = _createElementVNode(\"h2\", {\n    \"class\": \"section-title\"\n  }, \"网盘链接\", -1)), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[13] || (_cache[13] = _createElementVNode(\"i\", {\n    \"class\": \"el-icon-link\"\n  }, null, -1)), _createTextVNode(\" \" + _toDisplayString($setup.activity.cloudLink), 1)])])])) : _createCommentVNode(\"\", true), $setup.activity.rules ? (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_cache[15] || (_cache[15] = _createElementVNode(\"h2\", {\n    \"class\": \"section-title\"\n  }, \"活动规则\", -1)), _createElementVNode(\"div\", {\n    \"class\": \"section-content\",\n    innerHTML: $setup.activity.rules\n  }, null, 8, _hoisted_43)])) : _createCommentVNode(\"\", true), $setup.activity.rewards ? (_openBlock(), _createElementBlock(\"div\", _hoisted_44, [_cache[16] || (_cache[16] = _createElementVNode(\"h2\", {\n    \"class\": \"section-title\"\n  }, \"奖励设置\", -1)), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.activity.rewards, function (reward, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      \"class\": \"reward-item\"\n    }, [_createElementVNode(\"div\", _hoisted_47, _toDisplayString($setup.getRankText(index + 1)), 1), _createElementVNode(\"div\", _hoisted_48, \"¥\" + _toDisplayString($setup.formatNumber(reward)), 1)]);\n  }), 128))])])])) : _createCommentVNode(\"\", true), _createElementVNode(\"div\", _hoisted_49, [_createVNode(_component_el_button, {\n    size: \"large\",\n    onClick: $setup.goBack\n  }, {\n    \"default\": _withCtx(function () {\n      return _cache[17] || (_cache[17] = [_createElementVNode(\"i\", {\n        \"class\": \"el-icon-back\"\n      }, null, -1), _createTextVNode(\" 返回列表 \")]);\n    }),\n    _: 1,\n    __: [17]\n  }, 8, [\"onClick\"])])])) : _createCommentVNode(\"\", true)])), [[_directive_loading, $setup.loading]]), _createVNode(_component_AppFooter)]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}