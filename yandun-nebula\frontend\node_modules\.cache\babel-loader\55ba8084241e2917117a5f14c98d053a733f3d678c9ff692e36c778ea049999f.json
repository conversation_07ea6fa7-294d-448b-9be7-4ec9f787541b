{"ast": null, "code": "import _regenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _objectSpread from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.starts-with.js\";\nimport { ref, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { Clock, Check, Document, Key } from '@element-plus/icons-vue';\nimport api from '@/utils/api';\nimport AppFooter from '@/components/AppFooter.vue';\nimport TheHeader from '@/components/TheHeader.vue';\nexport default {\n  name: 'ActivityDetail',\n  components: {\n    TheHeader: TheHeader,\n    Clock: Clock,\n    Check: Check,\n    AppFooter: AppFooter,\n    Document: Document,\n    Key: Key\n  },\n  setup: function setup() {\n    var route = useRoute();\n    var router = useRouter();\n    var loading = ref(true);\n    var activity = ref(null);\n\n    // 获取活动详情\n    var fetchActivityDetail = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var id, announcementId, response, result, activityId, _response, _result, _response2, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              _context.p = 0;\n              id = route.params.id; // 判断是公告还是活动\n              if (!id.startsWith('announcement_')) {\n                _context.n = 5;\n                break;\n              }\n              announcementId = id.replace('announcement_', '');\n              _context.n = 1;\n              return fetch(\"/api/announcements/\".concat(announcementId));\n            case 1:\n              response = _context.v;\n              if (!response.ok) {\n                _context.n = 3;\n                break;\n              }\n              _context.n = 2;\n              return response.json();\n            case 2:\n              result = _context.v;\n              if (result.success) {\n                activity.value = _objectSpread(_objectSpread({}, result.data), {}, {\n                  id: id,\n                  type: 'announcement',\n                  description: result.data.content,\n                  startTime: result.data.publishedAt,\n                  endTime: result.data.publishedAt,\n                  views: result.data.viewCount,\n                  bannerImage: require('@/assets/logo.png'),\n                  coverImage: require('@/assets/logo.png'),\n                  status: 'ongoing',\n                  relatedLink: result.data.relatedLink,\n                  cloudLink: result.data.cloudLink\n                });\n              } else {\n                ElMessage.error('获取公告详情失败');\n              }\n              _context.n = 4;\n              break;\n            case 3:\n              ElMessage.error('获取公告详情失败');\n            case 4:\n              _context.n = 12;\n              break;\n            case 5:\n              if (!id.startsWith('activity_')) {\n                _context.n = 10;\n                break;\n              }\n              activityId = id.replace('activity_', '');\n              _context.n = 6;\n              return fetch(\"/api/activities/\".concat(activityId));\n            case 6:\n              _response = _context.v;\n              if (!_response.ok) {\n                _context.n = 8;\n                break;\n              }\n              _context.n = 7;\n              return _response.json();\n            case 7:\n              _result = _context.v;\n              if (_result.code === 0) {\n                activity.value = _objectSpread(_objectSpread({}, _result.data), {}, {\n                  id: id,\n                  type: 'event',\n                  title: _result.data.name,\n                  startTime: _result.data.startDate,\n                  endTime: _result.data.endDate,\n                  status: _result.data.status,\n                  views: 0,\n                  bannerImage: require('@/assets/logo.png'),\n                  coverImage: require('@/assets/logo.png'),\n                  participantCount: 0\n                });\n              } else {\n                ElMessage.error('获取活动详情失败');\n              }\n              _context.n = 9;\n              break;\n            case 8:\n              ElMessage.error('获取活动详情失败');\n            case 9:\n              _context.n = 12;\n              break;\n            case 10:\n              _context.n = 11;\n              return api.get(\"/api/activities/\".concat(id));\n            case 11:\n              _response2 = _context.v;\n              if (_response2.data.success) {\n                activity.value = _response2.data.data;\n              } else {\n                ElMessage.error(_response2.data.message || '获取活动详情失败');\n              }\n            case 12:\n              if (!activity.value) {\n                ElMessage.error('未找到相关内容');\n              }\n              _context.n = 14;\n              break;\n            case 13:\n              _context.p = 13;\n              _t = _context.v;\n              console.error('获取活动详情失败:', _t);\n              ElMessage.error('获取活动详情失败，请稍后重试');\n            case 14:\n              _context.p = 14;\n              loading.value = false;\n              return _context.f(14);\n            case 15:\n              return _context.a(2);\n          }\n        }, _callee, null, [[0, 13, 14, 15]]);\n      }));\n      return function fetchActivityDetail() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n\n    // 格式化数字\n    var formatNumber = function formatNumber(num) {\n      if (!num) return '0';\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    };\n\n    // 格式化日期\n    var formatDate = function formatDate(date) {\n      if (!date) return '';\n      var d = new Date(date);\n      return \"\".concat(d.getFullYear(), \"-\").concat(String(d.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(d.getDate()).padStart(2, '0'));\n    };\n\n    // 格式化日期时间\n    var formatDateTime = function formatDateTime(datetime) {\n      if (!datetime) return '';\n      var d = new Date(datetime);\n      return \"\".concat(d.getFullYear(), \"-\").concat(String(d.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(d.getDate()).padStart(2, '0'), \" \").concat(String(d.getHours()).padStart(2, '0'), \":\").concat(String(d.getMinutes()).padStart(2, '0'));\n    };\n\n    // 格式化区块链时间戳\n    var formatBlockchainTime = function formatBlockchainTime(timestamp) {\n      if (!timestamp) return '未知';\n      var date = new Date(timestamp * 1000);\n      return date.toLocaleString('zh-CN');\n    };\n\n    // 获取排名文本\n    var getRankText = function getRankText(rank) {\n      var rankMap = {\n        1: '第一名',\n        2: '第二名',\n        3: '第三名'\n      };\n      return rankMap[rank] || \"\\u7B2C\".concat(rank, \"\\u540D\");\n    };\n\n    // 返回列表\n    var goBack = function goBack() {\n      router.push('/activities');\n    };\n    onMounted(function () {\n      fetchActivityDetail();\n    });\n    return {\n      loading: loading,\n      activity: activity,\n      formatNumber: formatNumber,\n      formatDate: formatDate,\n      formatDateTime: formatDateTime,\n      formatBlockchainTime: formatBlockchainTime,\n      getRankText: getRankText,\n      goBack: goBack\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}