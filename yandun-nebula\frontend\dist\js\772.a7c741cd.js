"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[772],{80772:(e,n,a)=>{a.r(n),a.d(n,{default:()=>B});a(52675),a(89463);var l=a(95976),r=a(10160),i={class:"submit-vulnerability-container"},t={class:"container main-content"},u={class:"card-header"},s={class:"submission-tips"},o={class:"form-section"},c={class:"form-grid"},d={key:0,class:"form-tip"},v={class:"form-section"},m={class:"form-grid"},f={class:"severity-radio-group"},b=["onClick"],p={class:"severity-info"},k={class:"severity-label"},g={class:"severity-desc"},y={class:"severity-radio"},h={key:0,class:"el-icon-check"},F={class:"url-container"},L={class:"url-item"},w={class:"url-actions"},A={class:"form-section"},_={class:"attachment-links"},V={class:"form-section form-actions"},C={key:0,class:"ai-result-card"},I={class:"ai-result-content"},R={class:"severity-result"},U={key:0,class:"ai-analysis"},K={key:1,class:"submit-notice"};function W(e,n,a,W,T,X){var x=(0,l.g2)("TheHeader"),E=(0,l.g2)("el-alert"),S=(0,l.g2)("el-option"),q=(0,l.g2)("el-select"),N=(0,l.g2)("el-form-item"),Q=(0,l.g2)("el-input"),z=(0,l.g2)("el-button"),P=(0,l.g2)("el-tag"),j=(0,l.g2)("el-tooltip"),D=(0,l.g2)("el-form"),O=(0,l.g2)("el-card");return(0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(x),n[28]||(n[28]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("div",{class:"container"},[(0,l.Lk)("h1",{class:"page-title"},"提交漏洞"),(0,l.Lk)("p",{class:"page-subtitle"},"请认真填写漏洞详情，帮助企业更好地理解和修复安全问题")])],-1)),(0,l.Lk)("div",t,[(0,l.bF)(O,{class:"form-card","body-style":{padding:"30px"}},{header:(0,l.k6)(function(){return[(0,l.Lk)("div",u,[n[10]||(n[10]=(0,l.Lk)("div",{class:"header-main"},[(0,l.Lk)("h2",null,"漏洞提交表单"),(0,l.Lk)("p",null,[(0,l.eW)("带 "),(0,l.Lk)("span",{class:"required"},"*"),(0,l.eW)(" 的字段为必填项")])],-1)),(0,l.Lk)("div",s,[(0,l.bF)(E,{title:"提交提示",type:"info",description:"为了更好地帮助企业理解和修复漏洞，请尽可能详细地描述漏洞信息，包括复现步骤和修复建议。",closable:!1,"show-icon":""})])])]}),default:(0,l.k6)(function(){return[(0,l.bF)(D,{ref:"vulnerabilityFormRef",model:W.vulnerabilityForm,rules:W.rules,"label-position":"top",class:"vulnerability-form"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",o,[n[11]||(n[11]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("h3",{class:"section-title"},[(0,l.Lk)("i",{class:"el-icon-office-building"}),(0,l.eW)(" 厂商信息 ")])],-1)),(0,l.Lk)("div",c,[(0,l.bF)(N,{label:"厂商名称",prop:"vendorName",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(q,{modelValue:W.vulnerabilityForm.vendorName,"onUpdate:modelValue":n[0]||(n[0]=function(e){return W.vulnerabilityForm.vendorName=e}),placeholder:"请输入厂商名并选择",filterable:"",remote:"","remote-method":W.searchVendors,loading:W.loadingVendors,class:"full-width",onChange:W.onVendorChange},{default:(0,l.k6)(function(){return[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(W.vendors,function(e){return(0,l.uX)(),(0,l.Wv)(S,{key:e.id,label:e.companyName||e.username,value:e.companyName||e.username,"data-vendor-id":e.id},null,8,["label","value","data-vendor-id"])}),128))]}),_:1},8,["modelValue","remote-method","loading","onChange"])]}),_:1}),W.vulnerabilityForm.vendorName?((0,l.uX)(),(0,l.Wv)(N,{key:0,label:"选择任务",prop:"taskId",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(q,{modelValue:W.vulnerabilityForm.taskId,"onUpdate:modelValue":n[1]||(n[1]=function(e){return W.vulnerabilityForm.taskId=e}),placeholder:"请选择该企业发布的任务",class:"full-width",loading:W.loadingTasks,disabled:!W.availableTasks.length},{default:(0,l.k6)(function(){return[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(W.availableTasks,function(e){return(0,l.uX)(),(0,l.Wv)(S,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])}),128)),W.availableTasks.length||W.loadingTasks?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(S,{key:0,label:"该企业暂无发布的任务",value:"",disabled:""}))]}),_:1},8,["modelValue","loading","disabled"]),W.availableTasks.length||W.loadingTasks?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",d," 该企业暂无发布的安全测试任务，无法提交漏洞 "))]}),_:1})):(0,l.Q3)("",!0),(0,l.bF)(N,{label:"域名或IP",prop:"domain",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.domain,"onUpdate:modelValue":n[2]||(n[2]=function(e){return W.vulnerabilityForm.domain=e}),placeholder:"输入所属域名或IP（域名请提供标准主域名）"},null,8,["modelValue"])]}),_:1})])]),(0,l.Lk)("div",v,[n[16]||(n[16]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("h3",{class:"section-title"},[(0,l.Lk)("i",{class:"el-icon-warning"}),(0,l.eW)(" 漏洞信息 ")])],-1)),(0,l.Lk)("div",m,[(0,l.bF)(N,{label:"漏洞类别",prop:"category",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(q,{modelValue:W.vulnerabilityForm.category,"onUpdate:modelValue":n[3]||(n[3]=function(e){return W.vulnerabilityForm.category=e}),placeholder:"请选择漏洞类别",class:"full-width"},{default:(0,l.k6)(function(){return[(0,l.bF)(S,{label:"Web漏洞",value:"web"}),(0,l.bF)(S,{label:"移动应用漏洞",value:"mobile"}),(0,l.bF)(S,{label:"IoT设备漏洞",value:"iot"}),(0,l.bF)(S,{label:"工控系统漏洞",value:"ics"}),(0,l.bF)(S,{label:"其他",value:"other"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"漏洞标题",prop:"title",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.title,"onUpdate:modelValue":n[4]||(n[4]=function(e){return W.vulnerabilityForm.title=e}),placeholder:"单位名称+漏洞类型，如：某单位存在SQL注入漏洞"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"漏洞类型",prop:"type",class:"grid-item"},{default:(0,l.k6)(function(){return[(0,l.bF)(q,{modelValue:W.vulnerabilityForm.type,"onUpdate:modelValue":n[5]||(n[5]=function(e){return W.vulnerabilityForm.type=e}),placeholder:"请选择漏洞类型",class:"full-width"},{default:(0,l.k6)(function(){return[(0,l.bF)(S,{label:"SQL注入",value:"sql_injection"}),(0,l.bF)(S,{label:"XSS跨站脚本",value:"xss"}),(0,l.bF)(S,{label:"CSRF跨站请求伪造",value:"csrf"}),(0,l.bF)(S,{label:"文件上传漏洞",value:"file_upload"}),(0,l.bF)(S,{label:"权限绕过",value:"auth_bypass"}),(0,l.bF)(S,{label:"信息泄露",value:"info_leak"}),(0,l.bF)(S,{label:"业务逻辑漏洞",value:"logic_flaw"}),(0,l.bF)(S,{label:"命令执行",value:"command_exec"}),(0,l.bF)(S,{label:"其他",value:"other"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"漏洞等级",prop:"severity",class:"full-width"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",f,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(W.severityOptions,function(e){return(0,l.uX)(),(0,l.CE)("div",{key:e.value,class:(0,r.C4)(["severity-option",{active:W.vulnerabilityForm.severity===e.value}]),onClick:function(n){return W.vulnerabilityForm.severity=e.value}},[(0,l.Lk)("div",{class:(0,r.C4)(["severity-icon",e.iconClass])},[(0,l.Lk)("i",{class:(0,r.C4)(e.icon)},null,2)],2),(0,l.Lk)("div",p,[(0,l.Lk)("div",k,(0,r.v_)(e.label),1),(0,l.Lk)("div",g,(0,r.v_)(e.description),1)]),(0,l.Lk)("div",y,[W.vulnerabilityForm.severity===e.value?((0,l.uX)(),(0,l.CE)("i",h)):(0,l.Q3)("",!0)])],10,b)}),128))])]}),_:1})]),(0,l.bF)(N,{label:"漏洞URL",prop:"urls"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",F,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(W.vulnerabilityForm.urls,function(e,a){return(0,l.uX)(),(0,l.CE)("div",{key:a,class:"url-item-wrapper"},[(0,l.Lk)("div",L,[n[13]||(n[13]=(0,l.Lk)("div",{class:"url-prefix"},[(0,l.Lk)("i",{class:"el-icon-link"})],-1)),(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.urls[a],"onUpdate:modelValue":function(e){return W.vulnerabilityForm.urls[a]=e},placeholder:"请输入完整的URL地址，如：https://example.com/vulnerable-page",class:"url-input",clearable:""},null,8,["modelValue","onUpdate:modelValue"]),(0,l.bF)(z,{type:"danger",size:"small",onClick:function(e){return W.removeUrl(a)},disabled:W.vulnerabilityForm.urls.length<=1,class:"remove-url-btn"},{default:(0,l.k6)(function(){return n[12]||(n[12]=[(0,l.eW)(" 删除 ")])}),_:2,__:[12]},1032,["onClick","disabled"])])])}),128)),(0,l.Lk)("div",w,[(0,l.bF)(z,{type:"primary",size:"small",onClick:W.addUrl,class:"add-url-btn",plain:""},{default:(0,l.k6)(function(){return n[14]||(n[14]=[(0,l.Lk)("i",{class:"el-icon-plus"},null,-1),(0,l.eW)(" 添加更多URL ")])}),_:1,__:[14]},8,["onClick"])])]),n[15]||(n[15]=(0,l.Lk)("div",{class:"url-tips"},[(0,l.Lk)("i",{class:"el-icon-info"}),(0,l.Lk)("span",null,"请提供存在漏洞的具体页面URL，支持添加多个相关地址")],-1))]}),_:1,__:[15]})]),(0,l.Lk)("div",A,[n[20]||(n[20]=(0,l.Lk)("div",{class:"section-header"},[(0,l.Lk)("h3",{class:"section-title"},[(0,l.Lk)("i",{class:"el-icon-document"}),(0,l.eW)(" 漏洞详情 ")])],-1)),(0,l.bF)(N,{label:"简要描述",prop:"summary"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.summary,"onUpdate:modelValue":n[6]||(n[6]=function(e){return W.vulnerabilityForm.summary=e}),type:"textarea",rows:3,placeholder:"简要描述漏洞概况以及影响，请勿在此填写漏洞URL等具体漏洞信息",maxlength:500,"show-word-limit":""},null,8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"详细细节",prop:"description"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.description,"onUpdate:modelValue":n[7]||(n[7]=function(e){return W.vulnerabilityForm.description=e}),type:"textarea",rows:8,placeholder:"请详细描述漏洞的具体情况，包括发现的过程、问题的表现等",maxlength:2e4,"show-word-limit":""},null,8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"复现步骤",prop:"reproductionSteps"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.reproductionSteps,"onUpdate:modelValue":n[8]||(n[8]=function(e){return W.vulnerabilityForm.reproductionSteps=e}),type:"textarea",rows:5,placeholder:"请提供详细的复现步骤，以便我们能够验证漏洞"},null,8,["modelValue"])]}),_:1}),(0,l.bF)(N,{label:"附件链接"},{default:(0,l.k6)(function(){return[(0,l.Lk)("div",_,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(W.vulnerabilityForm.attachmentLinks,function(e,a){return(0,l.uX)(),(0,l.CE)("div",{key:a,class:"link-item"},[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.attachmentLinks[a],"onUpdate:modelValue":function(e){return W.vulnerabilityForm.attachmentLinks[a]=e},placeholder:"请输入网盘分享链接（如百度网盘、阿里云盘等）",class:"link-input"},null,8,["modelValue","onUpdate:modelValue"]),W.vulnerabilityForm.attachmentLinks.length>1?((0,l.uX)(),(0,l.Wv)(z,{key:0,type:"danger",size:"small",onClick:function(e){return W.removeAttachmentLink(a)},class:"remove-btn"},{default:(0,l.k6)(function(){return n[17]||(n[17]=[(0,l.eW)(" 删除 ")])}),_:2,__:[17]},1032,["onClick"])):(0,l.Q3)("",!0)])}),128)),(0,l.bF)(z,{type:"primary",size:"small",onClick:W.addAttachmentLink,class:"add-btn"},{default:(0,l.k6)(function(){return n[18]||(n[18]=[(0,l.eW)(" 添加链接 ")])}),_:1,__:[18]},8,["onClick"])]),n[19]||(n[19]=(0,l.Lk)("div",{class:"el-upload__tip"}," 请提供相关截图、POC文件等的网盘分享链接，建议使用百度网盘、阿里云盘等 ",-1))]}),_:1,__:[19]}),(0,l.bF)(N,{label:"修复方案",prop:"remediation"},{default:(0,l.k6)(function(){return[(0,l.bF)(Q,{modelValue:W.vulnerabilityForm.remediation,"onUpdate:modelValue":n[9]||(n[9]=function(e){return W.vulnerabilityForm.remediation=e}),type:"textarea",rows:3,placeholder:"如果您有修复建议，请在此提供"},null,8,["modelValue"])]}),_:1})]),(0,l.Lk)("div",V,[W.aiVerificationResult?((0,l.uX)(),(0,l.CE)("div",C,[n[23]||(n[23]=(0,l.Lk)("div",{class:"ai-result-header"},[(0,l.Lk)("i",{class:"el-icon-cpu"}),(0,l.Lk)("span",null,"AI智能检验结果")],-1)),(0,l.Lk)("div",I,[(0,l.Lk)("div",R,[n[21]||(n[21]=(0,l.Lk)("label",null,"AI评估等级：",-1)),(0,l.bF)(P,{type:W.getAISeverityType(W.aiVerificationResult.severity),size:"medium",class:"severity-tag"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,r.v_)(W.getAISeverityText(W.aiVerificationResult.severity)),1)]}),_:1},8,["type"])]),W.aiVerificationResult.analysis?((0,l.uX)(),(0,l.CE)("div",U,[n[22]||(n[22]=(0,l.Lk)("label",null,"分析说明：",-1)),(0,l.Lk)("p",null,(0,r.v_)(W.aiVerificationResult.analysis),1)])):(0,l.Q3)("",!0)])])):(0,l.Q3)("",!0),W.aiVerificationResult?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",K,n[24]||(n[24]=[(0,l.Lk)("i",{class:"el-icon-info"},null,-1),(0,l.Lk)("span",null,"请先进行AI智能检验，评估漏洞等级后再提交",-1)]))),(0,l.bF)(N,null,{default:(0,l.k6)(function(){return[(0,l.bF)(z,{type:"warning",onClick:W.verifyWithAI,loading:W.aiVerifying,size:"large",class:"verify-btn",disabled:!W.vulnerabilityForm.summary||W.submitting},{default:(0,l.k6)(function(){return[n[25]||(n[25]=(0,l.Lk)("i",{class:"el-icon-cpu"},null,-1)),(0,l.eW)(" "+(0,r.v_)(W.aiVerifying?"AI检验中...":"AI智能检验"),1)]}),_:1,__:[25]},8,["onClick","loading","disabled"]),(0,l.bF)(j,{content:W.aiVerificationResult?"":"请先进行AI智能检验后再提交",placement:"top",disabled:!!W.aiVerificationResult},{default:(0,l.k6)(function(){return[(0,l.bF)(z,{type:"primary",loading:W.submitting,onClick:W.submitVulnerability,disabled:!W.aiVerificationResult||W.aiVerifying,size:"large",class:"submit-button"},{default:(0,l.k6)(function(){return n[26]||(n[26]=[(0,l.eW)(" 提交漏洞 ")])}),_:1,__:[26]},8,["loading","onClick","disabled"])]}),_:1},8,["content","disabled"]),(0,l.bF)(z,{onClick:W.resetForm,size:"large",class:"reset-button"},{default:(0,l.k6)(function(){return n[27]||(n[27]=[(0,l.eW)(" 清空 ")])}),_:1,__:[27]},8,["onClick"])]}),_:1})])]}),_:1},8,["model","rules"])]}),_:1})])])}var T=a(88844),X=a(24059),x=a(698),E=(a(16280),a(76918),a(2008),a(50113),a(44114),a(54554),a(18111),a(22489),a(20116),a(26099),a(42762),a(12040)),S=a(39053),q=a(18057),N=a(30578),Q=a(36149),z=a(20907),P=a(80401);const j={name:"SubmitVulnerability",components:{TheHeader:P.A},setup:function(){var e=(0,S.rd)(),n=(0,E.KR)(null),a=(0,E.KR)(!1),r=(0,E.KR)(!1),i=(0,E.KR)(null),t=(0,E.KR)([]),u=(0,E.KR)([]),s=(0,E.KR)(!1),o=(0,E.KR)(null),c=(0,E.KR)([{value:"critical",label:"严重",description:"系统完全被控制",icon:"el-icon-warning-outline",iconClass:"critical-icon"},{value:"high",label:"高危",description:"重要数据泄露",icon:"el-icon-warning",iconClass:"high-icon"},{value:"medium",label:"中危",description:"一定程度信息泄露",icon:"el-icon-info",iconClass:"medium-icon"},{value:"low",label:"低危",description:"影响较小需修复",icon:"el-icon-question",iconClass:"low-icon"},{value:"info",label:"提示",description:"配置建议最佳实践",icon:"el-icon-info",iconClass:"info-icon"}]),d=(0,E.KR)(!1),v=(0,E.KR)([]),m=(0,E.KR)([]),f=(0,E.KR)([]),b=(0,E.KR)(null),p=(0,E.KR)(!1),k=(0,E.KR)(null),g=(0,E.Kh)({vendorName:"",domain:"",category:"web",title:"",urls:[""],type:"",weight:"",severity:"medium",activityId:"",taskId:"",summary:"",description:"",reproductionSteps:"",remediation:"",attachmentLinks:[""]}),y={vendorName:[{required:!0,message:"请输入厂商名并选择",trigger:"change"}],taskId:[{required:!0,message:"请选择该企业发布的任务",trigger:"change",validator:function(e,n,a){g.vendorName?n?a():a(new Error("请选择该企业发布的任务")):a()}}],domain:[{required:!0,message:"请输入域名或IP",trigger:"blur"}],category:[{required:!0,message:"请选择漏洞类别",trigger:"change"}],title:[{required:!0,message:"请输入漏洞标题",trigger:"blur"},{min:5,max:100,message:"标题长度应为5-100个字符",trigger:"blur"}],urls:[{required:!0,message:"请至少输入一个漏洞URL",trigger:"blur"}],type:[{required:!0,message:"请选择漏洞类型",trigger:"change"}],weight:[{required:!0,message:"请选择漏洞权重",trigger:"change"}],severity:[{required:!0,message:"请选择漏洞等级",trigger:"change"}],summary:[{required:!0,message:"请输入简要描述",trigger:"blur"},{min:10,max:500,message:"简要描述应在10-500个字符之间",trigger:"blur"}],description:[{required:!0,message:"请输入详细细节",trigger:"blur"},{min:20,message:"详细细节应不少于20个字符",trigger:"blur"}],reproductionSteps:[{required:!0,message:"请提供复现步骤",trigger:"blur"},{min:20,message:"复现步骤应不少于20个字符",trigger:"blur"}]},h=function(){var e=(0,x.A)((0,X.A)().m(function e(){var n,a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:if(!z.A.isLoggedIn()){e.n=5;break}return b.value=z.A.getCurrentUser(),console.log("当前用户类型:",b.value.userType),e.p=1,e.n=2,z.A.validateLoginStatus();case 2:n=e.v,n||(b.value=null),e.n=4;break;case 3:e.p=3,a=e.v,console.error("验证登录状态失败:",a);case 4:e.n=6;break;case 5:b.value=null;case 6:return e.a(2)}},e,null,[[1,3]])}));return function(){return e.apply(this,arguments)}}(),F=function(){z.A.logout(),b.value=null,p.value=!1,(0,q.nk)({type:"success",message:"退出登录成功"}),e.push("/")},L=function(){p.value=!p.value},w=function(e){k.value&&!k.value.contains(e.target)&&(p.value=!1)},A=function(){var e=(0,x.A)((0,X.A)().m(function e(n){var a,l;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:if(!n){e.n=6;break}return d.value=!0,e.p=1,e.n=2,Q.A.get("/users/enterprises",{params:{keyword:n}});case 2:a=e.v,0===a.data.code?t.value=a.data.data||[]:(t.value=[],console.warn("搜索厂商返回错误:",a.data.message)),e.n=4;break;case 3:e.p=3,l=e.v,console.error("搜索厂商失败:",l),t.value=[];case 4:return e.p=4,d.value=!1,e.f(4);case 5:e.n=7;break;case 6:t.value=[];case 7:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(n){return e.apply(this,arguments)}}(),_=function(){var e=(0,x.A)((0,X.A)().m(function e(n){var a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:if(g.taskId=null,u.value=[],n){e.n=1;break}return o.value=null,e.a(2);case 1:if(a=t.value.find(function(e){return(e.companyName||e.username)===n}),!a){e.n=2;break}return o.value=a.id,e.n=2,V(a.id);case 2:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}(),V=function(){var e=(0,x.A)((0,X.A)().m(function e(n){var a,l;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:return s.value=!0,e.p=1,e.n=2,Q.A.get("/security-tasks/vendor/".concat(n));case 2:a=e.v,a.data.success?u.value=a.data.data||[]:u.value=[],e.n=4;break;case 3:e.p=3,l=e.v,console.error("获取厂商任务失败:",l),u.value=[];case 4:return e.p=4,s.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(n){return e.apply(this,arguments)}}(),C=function(){g.urls.push("")},I=function(e){g.urls.splice(e,1)},R=function(){var e=(0,x.A)((0,X.A)().m(function e(){var n,a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Q.A.get("/projects");case 1:n=e.v,0===n.data.code?v.value=n.data.data||[]:(v.value=[],console.warn("获取项目列表返回错误:",n.data.message)),e.n=3;break;case 2:e.p=2,a=e.v,console.error("获取项目列表失败:",a),v.value=[];case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),U=function(){var e=(0,x.A)((0,X.A)().m(function e(){var n,a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Q.A.get("/activities");case 1:n=e.v,0===n.data.code?m.value=n.data.data||[]:(m.value=[],console.warn("获取活动列表返回错误:",n.data.message)),e.n=3;break;case 2:e.p=2,a=e.v,console.error("获取活动列表失败:",a),m.value=[];case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),K=function(){var e=(0,x.A)((0,X.A)().m(function e(){var n,a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Q.A.get("/tasks");case 1:n=e.v,0===n.data.code?f.value=n.data.data||[]:(f.value=[],console.warn("获取任务列表返回错误:",n.data.message)),e.n=3;break;case 2:e.p=2,a=e.v,console.error("获取任务列表失败:",a),f.value=[];case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),W=function(){g.attachmentLinks.push("")},P=function(e){g.attachmentLinks.length>1&&g.attachmentLinks.splice(e,1)},j=function(){var l=(0,x.A)((0,X.A)().m(function l(){return(0,X.A)().w(function(l){while(1)switch(l.n){case 0:if(n.value){l.n=1;break}return l.a(2);case 1:return l.n=2,n.value.validate(function(){var n=(0,x.A)((0,X.A)().m(function n(l){var r,t,u,s,o,c;return(0,X.A)().w(function(n){while(1)switch(n.n){case 0:if(!l){n.n=6;break}return n.p=1,a.value=!0,u=(0,T.A)((0,T.A)({},g),{},{urls:g.urls.filter(function(e){return e.trim()}),attachmentLinks:g.attachmentLinks.filter(function(e){return e.trim()}),aiSeverity:null===(r=i.value)||void 0===r?void 0:r.severity,aiAnalysis:null===(t=i.value)||void 0===t?void 0:t.analysis}),n.n=2,Q.A.post("/vulnerabilities",u);case 2:s=n.v,0===s.data.code?(q.nk.success("漏洞提交成功"),N.s.confirm("漏洞已成功提交，您可以继续提交新的漏洞或查看已提交的漏洞","提交成功",{confirmButtonText:"继续提交",cancelButtonText:"查看已提交",type:"success"}).then(function(){D()})["catch"](function(){e.push("/user/submissions")})):q.nk.error(s.data.message||"提交失败，请稍后重试"),n.n=4;break;case 3:n.p=3,c=n.v,console.error("提交漏洞失败:",c),q.nk.error((null===(o=c.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.message)||"提交失败，请稍后重试");case 4:return n.p=4,a.value=!1,n.f(4);case 5:n.n=7;break;case 6:q.nk.warning("请完善表单信息");case 7:return n.a(2)}},n,null,[[1,3,4,5]])}));return function(e){return n.apply(this,arguments)}}());case 2:return l.a(2)}},l)}));return function(){return l.apply(this,arguments)}}(),D=function(){n.value&&n.value.resetFields(),g.urls=[""],g.attachmentLinks=[""],i.value=null},O=function(){var e=(0,x.A)((0,X.A)().m(function e(){var n,a;return(0,X.A)().w(function(e){while(1)switch(e.n){case 0:if(g.summary){e.n=1;break}return q.nk.warning("请先填写简要描述"),e.a(2);case 1:return e.p=1,r.value=!0,e.n=2,Q.A.post("/vulnerabilities/ai-verify",{summary:g.summary,description:g.description,type:g.type});case 2:n=e.v,n.data.success?(i.value=n.data.data,q.nk.success("AI检验完成")):q.nk.error(n.data.message||"AI检验失败"),e.n=4;break;case 3:e.p=3,a=e.v,console.error("AI检验失败:",a),q.nk.error("AI检验失败，请稍后重试");case 4:return e.p=4,r.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),B=function(e){var n={critical:"danger",high:"danger",medium:"warning",low:"info",info:"success"};return n[e]||"info"},H=function(e){var n={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"提示"};return n[e]||"未知"};return(0,l.sV)(function(){h(),document.addEventListener("click",w),R(),U(),K()}),(0,l.xo)(function(){document.removeEventListener("click",w)}),{vulnerabilityFormRef:n,vulnerabilityForm:g,rules:y,severityOptions:c,vendors:t,loadingVendors:d,availableTasks:u,loadingTasks:s,selectedVendorId:o,onVendorChange:_,fetchVendorTasks:V,projects:v,activities:m,tasks:f,submitting:a,currentUser:b,showDropdown:p,toggleDropdown:L,userDropdown:k,handleLogout:F,searchVendors:A,addUrl:C,removeUrl:I,addAttachmentLink:W,removeAttachmentLink:P,submitVulnerability:j,resetForm:D,verifyWithAI:O,aiVerifying:r,aiVerificationResult:i,getAISeverityType:B,getAISeverityText:H}}};var D=a(1169);const O=(0,D.A)(j,[["render",W],["__scopeId","data-v-0a6f013a"]]),B=O}}]);