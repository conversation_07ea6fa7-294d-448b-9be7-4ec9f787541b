{"ast": null, "code": "import \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, with<PERSON><PERSON><PERSON> as _withKeys, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"activities-container\"\n};\nvar _hoisted_2 = {\n  \"class\": \"main-content\"\n};\nvar _hoisted_3 = {\n  \"class\": \"page-header\"\n};\nvar _hoisted_4 = {\n  \"class\": \"category-tabs\"\n};\nvar _hoisted_5 = {\n  \"class\": \"filter-section\"\n};\nvar _hoisted_6 = {\n  \"class\": \"search-box\"\n};\nvar _hoisted_7 = {\n  \"class\": \"activities-list\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  \"class\": \"activities-grid\"\n};\nvar _hoisted_9 = {\n  \"class\": \"activity-image\"\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = {\n  \"class\": \"activity-content\"\n};\nvar _hoisted_12 = {\n  \"class\": \"activity-header-tags\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  \"class\": \"blockchain-tag\"\n};\nvar _hoisted_14 = {\n  \"class\": \"activity-title\"\n};\nvar _hoisted_15 = {\n  \"class\": \"activity-time\"\n};\nvar _hoisted_16 = {\n  \"class\": \"activity-desc\"\n};\nvar _hoisted_17 = {\n  key: 0,\n  \"class\": \"activity-location\"\n};\nvar _hoisted_18 = {\n  \"class\": \"activity-footer\"\n};\nvar _hoisted_19 = {\n  key: 1,\n  \"class\": \"empty-state\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  \"class\": \"pagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_TheHeader = _resolveComponent(\"TheHeader\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_LinkIcon = _resolveComponent(\"LinkIcon\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_AppFooter = _resolveComponent(\"AppFooter\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_TheHeader), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createElementVNode(\"h1\", {\n    \"class\": \"page-title\"\n  }, \"公告与活动中心\", -1)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_tabs, {\n    modelValue: $setup.activeTab,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.activeTab = $event;\n    }),\n    onTabChange: $setup.handleTabChange\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_el_tab_pane, {\n        label: \"全部\",\n        name: \"all\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"系统公告\",\n        name: \"system\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"活动公告\",\n        name: \"event\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"维护公告\",\n        name: \"maintenance\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"重要通知\",\n        name: \"important\"\n      })];\n    }),\n    _: 1\n  }, 8, [\"modelValue\", \"onTabChange\"])])]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_input, {\n    modelValue: $setup.searchKeyword,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.searchKeyword = $event;\n    }),\n    placeholder: \"搜索活动...\",\n    \"prefix-icon\": \"el-icon-search\",\n    clearable: \"\",\n    onKeyup: _withKeys($setup.applyFilters, [\"enter\"])\n  }, {\n    append: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        onClick: $setup.applyFilters\n      }, {\n        \"default\": _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"搜索\")]);\n        }),\n        _: 1,\n        __: [4]\n      }, 8, [\"onClick\"])];\n    }),\n    _: 1\n  }, 8, [\"modelValue\", \"onKeyup\"])])]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_7, [$setup.filteredActivities.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.filteredActivities, function (activity) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      \"class\": \"activity-card\",\n      key: activity.id\n    }, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n      src: activity.coverImage || require('@/assets/logo.png'),\n      alt: \"活动封面\"\n    }, null, 8, _hoisted_10)]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"activity-type-tag\", $setup.getTypeClass(activity.type, activity.announcementType)])\n    }, _toDisplayString($setup.getTypeText(activity.type, activity.announcementType)), 3), activity.transactionHash ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n      \"default\": _withCtx(function () {\n        return [_createVNode(_component_LinkIcon)];\n      }),\n      _: 1\n    }), _cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"已上链\", -1))])) : _createCommentVNode(\"\", true)]), _createElementVNode(\"h3\", _hoisted_14, _toDisplayString(activity.title), 1), _createElementVNode(\"div\", _hoisted_15, [_cache[6] || (_cache[6] = _createElementVNode(\"i\", {\n      \"class\": \"el-icon-time\"\n    }, null, -1)), _createTextVNode(\" \" + _toDisplayString($setup.formatDate(activity.startTime)), 1)]), _createElementVNode(\"div\", _hoisted_16, _toDisplayString(activity.description), 1), activity.location ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_cache[7] || (_cache[7] = _createElementVNode(\"i\", {\n      \"class\": \"el-icon-location\"\n    }, null, -1)), _createTextVNode(\" \" + _toDisplayString(activity.location), 1)])) : _createCommentVNode(\"\", true), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: function onClick($event) {\n        return $setup.viewActivityDetail(activity.id);\n      }\n    }, {\n      \"default\": _withCtx(function () {\n        return _cache[8] || (_cache[8] = [_createTextVNode(\"查看详情\")]);\n      }),\n      _: 2,\n      __: [8]\n    }, 1032, [\"onClick\"])])])]);\n  }), 128))])) : !$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createVNode(_component_el_empty, {\n    description: \"暂无符合条件的活动，请更改筛选条件或稍后再试\"\n  })])) : _createCommentVNode(\"\", true)])), [[_directive_loading, $setup.loading]]), $setup.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_el_pagination, {\n    background: \"\",\n    layout: \"prev, pager, next\",\n    total: $setup.total,\n    \"page-size\": $setup.pageSize,\n    modelValue: $setup.currentPage,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.currentPage = $event;\n    }),\n    onCurrentChange: $setup.handlePageChange\n  }, null, 8, [\"total\", \"page-size\", \"modelValue\", \"onCurrentChange\"])])) : _createCommentVNode(\"\", true)]), _createVNode(_component_AppFooter)]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}