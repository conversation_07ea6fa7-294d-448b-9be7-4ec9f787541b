"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[435],{6435:(n,e,t)=>{t.r(e),t.d(e,{default:()=>w});var a=t(95976),u=t(10160),r=t(12040),i=t(39053),l=t(17383),s=(t(44114),t(18057)),d=t(20907),o={class:"admin-sidebar"},c={class:"sidebar-footer"},f={class:"user-info"},_={class:"user-details"},b={class:"username"};const k={__name:"AdminSidebar",setup:function(n){var e=(0,i.lq)(),t=(0,i.rd)(),k=d.A.getUserInfo()||{},p=(0,a.EW)(function(){return e.path}),v=function(){d.<PERSON>.logout(),s.nk.success("已退出登录"),t.push("/admin/login")};return function(n,e){var t=(0,a.g2)("el-icon"),i=(0,a.g2)("el-menu-item"),s=(0,a.g2)("el-sub-menu"),d=(0,a.g2)("el-menu"),m=(0,a.g2)("el-avatar"),F=(0,a.g2)("el-button");return(0,a.uX)(),(0,a.CE)("div",o,[e[11]||(e[11]=(0,a.Lk)("div",{class:"sidebar-header"},[(0,a.Lk)("div",{class:"logo"},[(0,a.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云"}),(0,a.Lk)("span",null,"管理后台")])],-1)),(0,a.bF)(d,{"default-active":p.value,class:"sidebar-menu",router:"","unique-opened":""},{default:(0,a.k6)(function(){return[(0,a.bF)(i,{index:"/admin/dashboard"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Odometer))]}),_:1}),e[0]||(e[0]=(0,a.Lk)("span",null,"控制台",-1))]}),_:1,__:[0]}),(0,a.bF)(s,{index:"users"},{title:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.User))]}),_:1}),e[1]||(e[1]=(0,a.Lk)("span",null,"用户管理",-1))]}),default:(0,a.k6)(function(){return[(0,a.bF)(i,{index:"/admin/users"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.UserFilled))]}),_:1}),e[2]||(e[2]=(0,a.Lk)("span",null,"用户列表",-1))]}),_:1,__:[2]})]}),_:1}),(0,a.bF)(s,{index:"content"},{title:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Document))]}),_:1}),e[3]||(e[3]=(0,a.Lk)("span",null,"内容管理",-1))]}),default:(0,a.k6)(function(){return[(0,a.bF)(i,{index:"/admin/posts"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.ChatDotRound))]}),_:1}),e[4]||(e[4]=(0,a.Lk)("span",null,"帖子管理",-1))]}),_:1,__:[4]}),(0,a.bF)(i,{index:"/admin/announcements"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Bell))]}),_:1}),e[5]||(e[5]=(0,a.Lk)("span",null,"公告管理",-1))]}),_:1,__:[5]})]}),_:1}),(0,a.bF)(s,{index:"projects"},{title:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Briefcase))]}),_:1}),e[6]||(e[6]=(0,a.Lk)("span",null,"项目管理",-1))]}),default:(0,a.k6)(function(){return[(0,a.bF)(i,{index:"/admin/projects"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.FolderOpened))]}),_:1}),e[7]||(e[7]=(0,a.Lk)("span",null,"项目列表",-1))]}),_:1,__:[7]}),(0,a.bF)(i,{index:"/admin/disputes"},{default:(0,a.k6)(function(){return[(0,a.bF)(t,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Warning))]}),_:1}),e[8]||(e[8]=(0,a.Lk)("span",null,"争议审核",-1))]}),_:1,__:[8]})]}),_:1})]}),_:1},8,["default-active"]),(0,a.Lk)("div",c,[(0,a.Lk)("div",f,[(0,a.bF)(m,{src:(0,r.R1)(k).avatar,size:32},{default:(0,a.k6)(function(){var n;return[(0,a.eW)((0,u.v_)(null===(n=(0,r.R1)(k).username)||void 0===n?void 0:n.charAt(0).toUpperCase()),1)]}),_:1},8,["src"]),(0,a.Lk)("div",_,[(0,a.Lk)("div",b,(0,u.v_)((0,r.R1)(k).username),1),e[9]||(e[9]=(0,a.Lk)("div",{class:"user-role"},"管理员",-1))])]),(0,a.bF)(F,{type:"danger",size:"small",onClick:v},{default:(0,a.k6)(function(){return e[10]||(e[10]=[(0,a.eW)(" 退出登录 ")])}),_:1,__:[10]})])])}}};var p=t(1169);const v=(0,p.A)(k,[["__scopeId","data-v-784255cd"]]),m=v;var F=t(48077),g={class:"admin-layout"},h={class:"admin-content"},L={class:"content-header"},y={class:"breadcrumb"},R={class:"header-actions"},C={class:"content-body"};const x={__name:"AdminLayout",setup:function(n){var e=(0,i.lq)(),t=(0,a.EW)(function(){var n={"/admin/dashboard":"","/admin/users":"用户管理","/admin/posts":"帖子管理","/admin/projects":"项目管理","/admin/disputes":"争议审核","/admin/announcements":"公告管理","/admin/settings":"系统设置"};return n[e.path]||""}),s=function(){window.location.reload()},d=function(){window.open("/","_blank")};return function(n,e){var i=(0,a.g2)("el-breadcrumb-item"),o=(0,a.g2)("el-breadcrumb"),c=(0,a.g2)("el-icon"),f=(0,a.g2)("el-button"),_=(0,a.g2)("router-view");return(0,a.uX)(),(0,a.CE)("div",g,[(0,a.bF)(m),(0,a.Lk)("div",h,[(0,a.Lk)("div",L,[(0,a.Lk)("div",y,[(0,a.bF)(o,{separator:"/"},{default:(0,a.k6)(function(){return[(0,a.bF)(i,{to:{path:"/admin/dashboard"}},{default:(0,a.k6)(function(){return e[0]||(e[0]=[(0,a.eW)("控制台")])}),_:1,__:[0]}),t.value?((0,a.uX)(),(0,a.Wv)(i,{key:0},{default:(0,a.k6)(function(){return[(0,a.eW)((0,u.v_)(t.value),1)]}),_:1})):(0,a.Q3)("",!0)]}),_:1})]),(0,a.Lk)("div",R,[(0,a.bF)(f,{type:"primary",size:"small",onClick:s},{default:(0,a.k6)(function(){return[(0,a.bF)(c,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.Refresh))]}),_:1}),e[1]||(e[1]=(0,a.eW)(" 刷新 "))]}),_:1,__:[1]}),(0,a.bF)(f,{size:"small",onClick:d},{default:(0,a.k6)(function(){return[(0,a.bF)(c,null,{default:(0,a.k6)(function(){return[(0,a.bF)((0,r.R1)(l.HomeFilled))]}),_:1}),e[2]||(e[2]=(0,a.eW)(" 返回首页 "))]}),_:1,__:[2]})])]),(0,a.Lk)("div",C,[(0,a.bF)(_)]),(0,a.bF)(F.A,{"is-admin":!0})])])}}},A=(0,p.A)(x,[["__scopeId","data-v-e4c39442"]]),w=A},48077:(n,e,t)=>{t.d(e,{A:()=>s});var a=t(95976),u=t(10160);const r={__name:"CopyrightFooter",props:{isAdmin:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!1}},setup:function(n){return function(e,t){return(0,a.uX)(),(0,a.CE)("div",{class:(0,u.C4)(["copyright-footer",{"admin-style":n.isAdmin,"fixed-style":n.isFixed}])},t[0]||(t[0]=[(0,a.Fv)('<div class="footer-content" data-v-7e9937ff><p class="copyright" data-v-7e9937ff>Copyright © 2025 衍盾星云 版权所有</p><div class="footer-links" data-v-7e9937ff><a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" data-v-7e9937ff> 渝ICP备2025053738号-1 </a><span class="separator" data-v-7e9937ff>|</span><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50011702501094" target="_blank" rel="noopener noreferrer" data-v-7e9937ff><img src="/assets/images/备案图标.png" alt="公安备案" class="beian-icon" data-v-7e9937ff> 渝公网安备50011702501094号 </a></div></div>',1)]),2)}}};var i=t(1169);const l=(0,i.A)(r,[["__scopeId","data-v-7e9937ff"]]),s=l}}]);