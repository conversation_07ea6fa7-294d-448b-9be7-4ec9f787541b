"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[971],{40971:(e,n,t)=>{t.r(n),t.d(n,{default:()=>w});var a=t(24059),r=t(88844),u=t(698),l=(t(28706),t(62062),t(23288),t(18111),t(61701),t(26099),t(27495),t(47764),t(5746),t(62953),t(95976)),c=t(12040),i=t(10160),o=t(18057),s=t(30578),d=t(17383),f=t(36149),p={class:"user-management"},b={class:"page-header"},v={class:"search-filters"},k={class:"table-container"},g={class:"pagination"};const h={__name:"UserManagement",setup:function(e){var n=(0,c.KR)(!1),t=(0,c.KR)([]),h=(0,c.KR)([]),_=(0,c.Kh)({search:"",userType:"",status:""}),m=(0,c.Kh)({page:1,limit:10,total:0}),w=function(){var e=(0,u.A)((0,a.A)().m(function e(){var u,l,c;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return n.value=!0,e.p=1,u=(0,r.A)({page:m.page,limit:m.limit},_),e.n=2,f.A.get("/admin/users",{params:u});case 2:l=e.v,l.data.success&&(t.value=l.data.data.users,m.total=l.data.data.total),e.n=4;break;case 3:e.p=3,c=e.v,console.error("加载用户列表失败:",c),o.nk.error("加载用户列表失败");case 4:return e.p=4,n.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),F=function(){var e=(0,u.A)((0,a.A)().m(function e(n,t){var r,u;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要".concat("banned"===t?"封禁":"解封","该用户吗？"),"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:return e.n=2,f.A.put("/admin/users/".concat(n,"/status"),{status:t});case 2:r=e.v,r.data.success&&(o.nk.success("操作成功"),w()),e.n=4;break;case 3:e.p=3,u=e.v,"cancel"!==u&&(console.error("更新用户状态失败:",u),o.nk.error("操作失败"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(n,t){return e.apply(this,arguments)}}(),y=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t,r;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要删除该用户吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:return e.n=2,f.A["delete"]("/admin/users/".concat(n));case 2:t=e.v,t.data.success&&(o.nk.success("删除成功"),w()),e.n=4;break;case 3:e.p=3,r=e.v,"cancel"!==r&&(console.error("删除用户失败:",r),o.nk.error("删除失败"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(n){return e.apply(this,arguments)}}(),A=function(e){var n={whiteHat:"白帽子",enterprise:"企业用户",admin:"管理员"};return n[e]||e},C=function(e){var n={whiteHat:"primary",enterprise:"success",admin:"danger"};return n[e]||""},W=function(e){var n={active:"正常",inactive:"禁用",banned:"封禁"};return n[e]||e},z=function(e){var n={active:"success",inactive:"warning",banned:"danger"};return n[e]||""},T=function(e){m.limit=e,m.page=1,w()},V=function(e){m.page=e,w()},x=function(e){h.value=e},L=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t,r;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:if(0!==h.value.length){e.n=1;break}return o.nk.warning("请先选择要操作的用户"),e.a(2);case 1:return e.p=1,e.n=2,s.s.confirm("确定要".concat("banned"===n?"封禁":"解封","选中的 ").concat(h.value.length," 个用户吗？"),"批量操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 2:return t=h.value.map(function(e){return f.A.put("/admin/users/".concat(e.id,"/status"),{status:n})}),e.n=3,Promise.all(t);case 3:o.nk.success("批量操作成功"),w(),h.value=[],e.n=5;break;case 4:e.p=4,r=e.v,"cancel"!==r&&(console.error("批量操作失败:",r),o.nk.error("批量操作失败"));case 5:return e.a(2)}},e,null,[[1,4]])}));return function(n){return e.apply(this,arguments)}}(),B=function(e){return e?new Date(e).toLocaleString("zh-CN"):"-"};return(0,l.sV)(function(){w()}),function(e,a){var r=(0,l.g2)("el-button"),u=(0,l.g2)("el-icon"),o=(0,l.g2)("el-input"),s=(0,l.g2)("el-col"),f=(0,l.g2)("el-option"),K=(0,l.g2)("el-select"),U=(0,l.g2)("el-row"),X=(0,l.g2)("el-table-column"),R=(0,l.g2)("el-avatar"),S=(0,l.g2)("el-tag"),H=(0,l.g2)("el-table"),N=(0,l.g2)("el-pagination"),D=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",p,[(0,l.Lk)("div",b,[a[7]||(a[7]=(0,l.Lk)("h1",null,"用户管理",-1)),(0,l.bF)(r,{onClick:a[0]||(a[0]=function(n){return e.$router.go(-1)})},{default:(0,l.k6)(function(){return a[6]||(a[6]=[(0,l.eW)("返回")])}),_:1,__:[6]})]),(0,l.Lk)("div",v,[(0,l.bF)(U,{gutter:20},{default:(0,l.k6)(function(){return[(0,l.bF)(s,{span:6},{default:(0,l.k6)(function(){return[(0,l.bF)(o,{modelValue:_.search,"onUpdate:modelValue":a[1]||(a[1]=function(e){return _.search=e}),placeholder:"搜索用户名、邮箱或公司名",clearable:"",onChange:w},{prefix:(0,l.k6)(function(){return[(0,l.bF)(u,null,{default:(0,l.k6)(function(){return[(0,l.bF)((0,c.R1)(d.Search))]}),_:1})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(s,{span:4},{default:(0,l.k6)(function(){return[(0,l.bF)(K,{modelValue:_.userType,"onUpdate:modelValue":a[2]||(a[2]=function(e){return _.userType=e}),placeholder:"用户类型",clearable:"",onChange:w},{default:(0,l.k6)(function(){return[(0,l.bF)(f,{label:"白帽子",value:"whiteHat"}),(0,l.bF)(f,{label:"企业用户",value:"enterprise"}),(0,l.bF)(f,{label:"管理员",value:"admin"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(s,{span:4},{default:(0,l.k6)(function(){return[(0,l.bF)(K,{modelValue:_.status,"onUpdate:modelValue":a[3]||(a[3]=function(e){return _.status=e}),placeholder:"用户状态",clearable:"",onChange:w},{default:(0,l.k6)(function(){return[(0,l.bF)(f,{label:"正常",value:"active"}),(0,l.bF)(f,{label:"禁用",value:"inactive"}),(0,l.bF)(f,{label:"封禁",value:"banned"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(s,{span:4},{default:(0,l.k6)(function(){return[(0,l.bF)(r,{type:"primary",onClick:w},{default:(0,l.k6)(function(){return a[8]||(a[8]=[(0,l.eW)("搜索")])}),_:1,__:[8]})]}),_:1}),(0,l.bF)(s,{span:6},{default:(0,l.k6)(function(){return[(0,l.bF)(r,{type:"warning",disabled:0===h.value.length,onClick:a[4]||(a[4]=function(e){return L("banned")})},{default:(0,l.k6)(function(){return a[9]||(a[9]=[(0,l.eW)(" 批量封禁 ")])}),_:1,__:[9]},8,["disabled"]),(0,l.bF)(r,{type:"success",disabled:0===h.value.length,onClick:a[5]||(a[5]=function(e){return L("active")})},{default:(0,l.k6)(function(){return a[10]||(a[10]=[(0,l.eW)(" 批量解封 ")])}),_:1,__:[10]},8,["disabled"])]}),_:1})]}),_:1})]),(0,l.Lk)("div",k,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(H,{data:t.value,stripe:"",style:{width:"100%"},onSelectionChange:x},{default:(0,l.k6)(function(){return[(0,l.bF)(X,{type:"selection",width:"55"}),(0,l.bF)(X,{prop:"id",label:"ID",width:"80"}),(0,l.bF)(X,{label:"头像",width:"80"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.bF)(R,{src:n.avatar,size:40},{default:(0,l.k6)(function(){return[(0,l.eW)((0,i.v_)(n.username.charAt(0).toUpperCase()),1)]}),_:2},1032,["src"])]}),_:1}),(0,l.bF)(X,{prop:"username",label:"用户名",width:"120"}),(0,l.bF)(X,{prop:"email",label:"邮箱",width:"200"}),(0,l.bF)(X,{label:"用户类型",width:"100"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.bF)(S,{type:C(n.userType),size:"small"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,i.v_)(A(n.userType)),1)]}),_:2},1032,["type"])]}),_:1}),(0,l.bF)(X,{prop:"companyName",label:"公司名称",width:"150"}),(0,l.bF)(X,{label:"状态",width:"100"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.bF)(S,{type:z(n.status),size:"small"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,i.v_)(W(n.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,l.bF)(X,{prop:"lastLoginAt",label:"最后登录",width:"180"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.eW)((0,i.v_)(B(n.lastLoginAt)),1)]}),_:1}),(0,l.bF)(X,{prop:"createdAt",label:"注册时间",width:"180"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.eW)((0,i.v_)(B(n.createdAt)),1)]}),_:1}),(0,l.bF)(X,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)(function(e){var n=e.row;return["active"===n.status?((0,l.uX)(),(0,l.Wv)(r,{key:0,type:"warning",size:"small",onClick:function(e){return F(n.id,"banned")}},{default:(0,l.k6)(function(){return a[11]||(a[11]=[(0,l.eW)(" 封禁 ")])}),_:2,__:[11]},1032,["onClick"])):((0,l.uX)(),(0,l.Wv)(r,{key:1,type:"success",size:"small",onClick:function(e){return F(n.id,"active")}},{default:(0,l.k6)(function(){return a[12]||(a[12]=[(0,l.eW)(" 解封 ")])}),_:2,__:[12]},1032,["onClick"])),"admin"!==n.userType?((0,l.uX)(),(0,l.Wv)(r,{key:2,type:"danger",size:"small",onClick:function(e){return y(n.id)}},{default:(0,l.k6)(function(){return a[13]||(a[13]=[(0,l.eW)(" 删除 ")])}),_:2,__:[13]},1032,["onClick"])):(0,l.Q3)("",!0)]}),_:1})]}),_:1},8,["data"])),[[D,n.value]]),(0,l.Lk)("div",g,[(0,l.bF)(N,{"current-page":m.page,"page-size":m.limit,"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:T,onCurrentChange:V},null,8,["current-page","page-size","total"])])])])}}};var _=t(1169);const m=(0,_.A)(h,[["__scopeId","data-v-97a18806"]]),w=m}}]);