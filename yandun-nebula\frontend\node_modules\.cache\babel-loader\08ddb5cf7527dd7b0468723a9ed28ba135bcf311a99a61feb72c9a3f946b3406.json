{"ast": null, "code": "import _objectSpread from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"F:/Yandun_Nebula/yandun-nebula/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.from.js\";\nimport \"core-js/modules/es.array.is-array.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.parse-float.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.trim.js\";\nimport \"core-js/modules/web.timers.js\";\nimport { ref, reactive, onMounted, watch } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport auth from '../utils/auth';\nimport api from '../utils/api';\nimport TheHeader from '../components/TheHeader.vue';\nimport AppFooter from '../components/AppFooter.vue';\nexport default {\n  name: 'UserProfile',\n  components: {\n    TheHeader: TheHeader,\n    AppFooter: AppFooter\n  },\n  props: {\n    activeTab: {\n      type: String,\n      \"default\": 'info'\n    }\n  },\n  setup: function setup(props) {\n    var router = useRouter();\n    var user = ref(null);\n    var currentTab = ref(props.activeTab || 'info');\n    var loading = ref(false);\n    var saving = ref(false);\n    var editMode = ref(false);\n    var passwordFormRef = ref(null);\n    var userFormRef = ref(null);\n\n    // 头像上传相关\n    var uploadLoading = ref(false);\n    var avatarUrl = ref('');\n    var uploadUrl = \"/upload/avatar\";\n    var uploadHeaders = {\n      'Authorization': \"Bearer \".concat(auth.getToken())\n    };\n\n    // 编辑表单数据\n    var editForm = reactive({\n      username: '',\n      email: '',\n      phone: '',\n      realName: '',\n      companyName: '',\n      bio: ''\n    });\n\n    // 提交相关数据\n    var submissions = ref([]);\n    var loadingSubmissions = ref(false);\n    var submissionFilter = ref('');\n    var detailDialogVisible = ref(false);\n    var selectedSubmission = ref(null);\n    var submissionStats = reactive({\n      total: 0,\n      confirmed: 0,\n      pending: 0,\n      rejected: 0\n    });\n\n    // 奖励相关数据\n    var rewards = ref([]);\n    var loadingRewards = ref(false);\n    var rewardDateRange = ref([]);\n    var rewardStats = reactive({\n      totalEarned: 0,\n      thisMonth: 0,\n      totalRewards: 0\n    });\n\n    // 余额信息\n    var balanceInfo = reactive({\n      currentBalance: 0,\n      totalEarnings: 0\n    });\n\n    // 提现相关数据\n    var showWithdrawalHistory = ref(false);\n    var loadingWithdrawal = ref(false);\n    var loadingWithdrawalHistory = ref(false);\n    var withdrawalHistory = ref([]);\n    var withdrawalFormRef = ref(null);\n    var withdrawalForm = reactive({\n      amount: '',\n      bankCardNumber: '',\n      bankName: '',\n      accountHolderName: ''\n    });\n\n    // 证书相关数据\n    var certificates = ref([]);\n    var loadingCertificates = ref(false);\n    var certificateFilter = ref('');\n    var certificatePagination = ref({\n      page: 1,\n      limit: 6,\n      total: 0,\n      totalPages: 0\n    });\n    var showCertificateDetail = ref(false);\n    var selectedCertificate = ref(null);\n\n    // 企业用户账户相关数据\n    var accountBalance = reactive({\n      currentBalance: 0,\n      totalSpent: 0\n    });\n    var myTasks = ref([]);\n    var loadingMyTasks = ref(false);\n    var taskDetailVisible = ref(false);\n    var selectedTask = ref(null);\n    var rechargingLoading = ref(false);\n    var rechargeFormRef = ref(null);\n    var rechargeForm = reactive({\n      amount: 1000,\n      paymentMethod: 'alipay'\n    });\n    var rechargeRules = reactive({\n      amount: [{\n        required: true,\n        message: '请输入充值金额',\n        trigger: 'blur'\n      }, {\n        type: 'number',\n        min: 100,\n        message: '最低充值金额为100元',\n        trigger: 'blur'\n      }]\n    });\n    var passwordForm = reactive({\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n\n    // 用户信息验证规则\n    var userRules = reactive({\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '用户名长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱地址',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      phone: [{\n        pattern: /^1[3-9]\\d{9}$/,\n        message: '请输入正确的手机号码',\n        trigger: 'blur'\n      }]\n    });\n    var validatePass = function validatePass(_rule, value, callback) {\n      if (value === '') {\n        callback(new Error('请输入新密码'));\n      } else {\n        if (passwordForm.confirmPassword !== '') {\n          passwordFormRef.value.validateField('confirmPassword');\n        }\n        callback();\n      }\n    };\n    var validatePass2 = function validatePass2(_rule, value, callback) {\n      if (value === '') {\n        callback(new Error('请再次输入新密码'));\n      } else if (value !== passwordForm.newPassword) {\n        callback(new Error('两次输入密码不一致'));\n      } else {\n        callback();\n      }\n    };\n    var passwordRules = {\n      currentPassword: [{\n        required: true,\n        message: '请输入当前密码',\n        trigger: 'blur'\n      }],\n      newPassword: [{\n        required: true,\n        message: '请输入新密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        message: '密码长度不能小于6个字符',\n        trigger: 'blur'\n      }, {\n        validator: validatePass,\n        trigger: 'blur'\n      }],\n      confirmPassword: [{\n        required: true,\n        message: '请再次输入新密码',\n        trigger: 'blur'\n      }, {\n        validator: validatePass2,\n        trigger: 'blur'\n      }]\n    };\n\n    // 提现表单验证规则\n    var withdrawalRules = {\n      amount: [{\n        required: true,\n        message: '请输入提现金额',\n        trigger: 'blur'\n      }, {\n        validator: function validator(rule, value, callback) {\n          if (!value || value <= 0) {\n            callback(new Error('提现金额必须大于0'));\n          } else if (parseFloat(value) > balanceInfo.currentBalance) {\n            callback(new Error('提现金额不能超过当前余额'));\n          } else {\n            callback();\n          }\n        },\n        trigger: 'blur'\n      }],\n      bankCardNumber: [{\n        required: true,\n        message: '请输入银行卡号',\n        trigger: 'blur'\n      }, {\n        pattern: /^\\d{16,19}$/,\n        message: '请输入正确的银行卡号',\n        trigger: 'blur'\n      }],\n      bankName: [{\n        required: true,\n        message: '请输入银行名称',\n        trigger: 'blur'\n      }],\n      accountHolderName: [{\n        required: true,\n        message: '请输入开户人姓名',\n        trigger: 'blur'\n      }]\n    };\n    var loadUserInfo = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var isValid, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              if (!auth.isLoggedIn()) {\n                _context.n = 5;\n                break;\n              }\n              user.value = auth.getCurrentUser();\n\n              // 初始化编辑表单\n              if (user.value) {\n                Object.assign(editForm, {\n                  username: user.value.username || '',\n                  email: user.value.email || '',\n                  phone: user.value.phone || '',\n                  realName: user.value.realName || '',\n                  companyName: user.value.companyName || '',\n                  bio: user.value.bio || ''\n                });\n              }\n\n              // 异步验证登录状态\n              _context.p = 1;\n              _context.n = 2;\n              return auth.validateLoginStatus();\n            case 2:\n              isValid = _context.v;\n              if (!isValid) {\n                user.value = null;\n              }\n              _context.n = 4;\n              break;\n            case 3:\n              _context.p = 3;\n              _t = _context.v;\n              console.error('验证登录状态失败:', _t);\n              user.value = null;\n            case 4:\n              _context.n = 6;\n              break;\n            case 5:\n              user.value = null;\n            case 6:\n              return _context.a(2);\n          }\n        }, _callee, null, [[1, 3]]);\n      }));\n      return function loadUserInfo() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n\n    // 启用编辑模式\n    var enableEditMode = function enableEditMode() {\n      editMode.value = true;\n    };\n\n    // 取消编辑\n    var cancelEdit = function cancelEdit() {\n      editMode.value = false;\n      // 重置表单数据\n      if (user.value) {\n        Object.assign(editForm, {\n          username: user.value.username || '',\n          email: user.value.email || '',\n          phone: user.value.phone || '',\n          realName: user.value.realName || '',\n          companyName: user.value.companyName || '',\n          bio: user.value.bio || ''\n        });\n      }\n    };\n\n    // 保存用户信息\n    var saveUserInfo = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var response, _t2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              if (userFormRef.value) {\n                _context2.n = 1;\n                break;\n              }\n              return _context2.a(2);\n            case 1:\n              _context2.p = 1;\n              _context2.n = 2;\n              return userFormRef.value.validate();\n            case 2:\n              saving.value = true;\n              _context2.n = 3;\n              return api.put('/auth/profile', editForm);\n            case 3:\n              response = _context2.v;\n              if (response.data.success) {\n                // 更新本地用户信息\n                Object.assign(user.value, editForm);\n                auth.saveLoginInfo(auth.getToken(), user.value, localStorage.getItem('token') ? true : false);\n                editMode.value = false;\n                ElMessage.success('个人信息更新成功');\n              } else {\n                ElMessage.error(response.data.message || '更新失败');\n              }\n              _context2.n = 5;\n              break;\n            case 4:\n              _context2.p = 4;\n              _t2 = _context2.v;\n              console.error('更新用户信息失败:', _t2);\n              ElMessage.error('更新失败，请稍后重试');\n            case 5:\n              _context2.p = 5;\n              saving.value = false;\n              return _context2.f(5);\n            case 6:\n              return _context2.a(2);\n          }\n        }, _callee2, null, [[1, 4, 5, 6]]);\n      }));\n      return function saveUserInfo() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n\n    // 头像上传前的验证\n    var beforeAvatarUpload = function beforeAvatarUpload(file) {\n      var isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';\n      var isLt500K = file.size / 1024 < 500;\n      if (!isJPG) {\n        ElMessage.error('头像只能是 JPG/PNG 格式!');\n        return false;\n      }\n      if (!isLt500K) {\n        ElMessage.error('头像大小不能超过 500KB!');\n        return false;\n      }\n      uploadLoading.value = true;\n      return true;\n    };\n\n    // 头像上传成功\n    var handleAvatarSuccess = function handleAvatarSuccess(response) {\n      uploadLoading.value = false;\n      if (response.success) {\n        avatarUrl.value = response.data.url;\n        user.value.avatar = response.data.url;\n\n        // 更新本地存储的用户信息\n        auth.saveLoginInfo(auth.getToken(), user.value, localStorage.getItem('token') ? true : false);\n        ElMessage.success('头像上传成功');\n      } else {\n        ElMessage.error(response.message || '头像上传失败');\n      }\n    };\n\n    // 头像上传失败\n    var handleAvatarError = function handleAvatarError(error) {\n      uploadLoading.value = false;\n      console.error('头像上传失败:', error);\n      ElMessage.error('头像上传失败，请稍后重试');\n    };\n    var changePassword = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        var response, _t3;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              if (passwordFormRef.value) {\n                _context3.n = 1;\n                break;\n              }\n              return _context3.a(2);\n            case 1:\n              _context3.p = 1;\n              _context3.n = 2;\n              return passwordFormRef.value.validate();\n            case 2:\n              loading.value = true;\n              _context3.n = 3;\n              return api.put('/auth/change-password', {\n                currentPassword: passwordForm.currentPassword,\n                newPassword: passwordForm.newPassword\n              });\n            case 3:\n              response = _context3.v;\n              if (response.data.success) {\n                ElMessage.success('密码修改成功');\n\n                // 清空表单\n                passwordForm.currentPassword = '';\n                passwordForm.newPassword = '';\n                passwordForm.confirmPassword = '';\n              } else {\n                ElMessage.error(response.data.message || '密码修改失败');\n              }\n              _context3.n = 5;\n              break;\n            case 4:\n              _context3.p = 4;\n              _t3 = _context3.v;\n              console.error('修改密码失败:', _t3);\n              if (_t3.response && _t3.response.data && _t3.response.data.message) {\n                ElMessage.error(_t3.response.data.message);\n              } else {\n                ElMessage.error('密码修改失败，请稍后重试');\n              }\n            case 5:\n              _context3.p = 5;\n              loading.value = false;\n              return _context3.f(5);\n            case 6:\n              return _context3.a(2);\n          }\n        }, _callee3, null, [[1, 4, 5, 6]]);\n      }));\n      return function changePassword() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    // 获取提交记录\n    var fetchSubmissions = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var params, response, _t4;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              loadingSubmissions.value = true;\n              _context4.p = 1;\n              params = {};\n              if (submissionFilter.value) {\n                params.status = submissionFilter.value;\n              }\n              _context4.n = 2;\n              return api.get('/vulnerabilities/my-submissions', {\n                params: params\n              });\n            case 2:\n              response = _context4.v;\n              if (response.data.success) {\n                submissions.value = response.data.data;\n                submissionStats.total = response.data.stats.total;\n                submissionStats.confirmed = response.data.stats.confirmed;\n                submissionStats.pending = response.data.stats.pending;\n                submissionStats.rejected = response.data.stats.rejected;\n              } else {\n                ElMessage.error(response.data.message || '获取提交记录失败');\n              }\n              _context4.n = 4;\n              break;\n            case 3:\n              _context4.p = 3;\n              _t4 = _context4.v;\n              console.error('获取提交记录失败:', _t4);\n              ElMessage.error('获取提交记录失败');\n            case 4:\n              _context4.p = 4;\n              loadingSubmissions.value = false;\n              return _context4.f(4);\n            case 5:\n              return _context4.a(2);\n          }\n        }, _callee4, null, [[1, 3, 4, 5]]);\n      }));\n      return function fetchSubmissions() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n\n    // 查看漏洞详情\n    var viewSubmissionDetail = function viewSubmissionDetail(submission) {\n      selectedSubmission.value = submission;\n      detailDialogVisible.value = true;\n    };\n\n    // 用户确认漏洞\n    var confirmVulnerability = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(vulnerability) {\n        var isConfirmed, message, response, successMessage, _t5;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              _context5.p = 0;\n              isConfirmed = vulnerability.status === 'enterprise_confirmed';\n              message = isConfirmed ? \"\\u786E\\u8BA4\\u63A5\\u53D7\\u4F01\\u4E1A\\u5BF9\\u6F0F\\u6D1E\\\"\".concat(vulnerability.title, \"\\\"\\u7684\\u5BA1\\u6838\\u7ED3\\u679C\\u5417\\uFF1F\\u786E\\u8BA4\\u540E\\u5C06\\u53D1\\u653E\\u5956\\u52B1\\u5E76\\u751F\\u6210\\u8BC1\\u4E66\\u3002\") : \"\\u786E\\u8BA4\\u63A5\\u53D7\\u4F01\\u4E1A\\u5BF9\\u6F0F\\u6D1E\\\"\".concat(vulnerability.title, \"\\\"\\u7684\\u62D2\\u7EDD\\u7ED3\\u679C\\u5417\\uFF1F\\u786E\\u8BA4\\u540E\\u8BE5\\u6F0F\\u6D1E\\u5C06\\u88AB\\u6807\\u8BB0\\u4E3A\\u5DF2\\u5904\\u7406\\u3002\");\n              _context5.n = 1;\n              return ElMessageBox.confirm(message, isConfirmed ? '确认漏洞' : '接受拒绝', {\n                confirmButtonText: isConfirmed ? '确认' : '接受',\n                cancelButtonText: '取消',\n                type: isConfirmed ? 'success' : 'warning'\n              });\n            case 1:\n              _context5.n = 2;\n              return api.post(\"/vulnerability-workflow/user-confirm/\".concat(vulnerability.id), {\n                action: 'confirm'\n              });\n            case 2:\n              response = _context5.v;\n              if (response.data.success) {\n                // 使用服务器返回的消息，包含区块链信息\n                successMessage = response.data.message;\n                if (response.data.data.blockchain) {\n                  successMessage += \"\\n\\u533A\\u5757\\u94FE\\u4EA4\\u6613\\u54C8\\u5E0C: \".concat(response.data.data.blockchain.transactionHash);\n                }\n                ElMessage.success(successMessage);\n                fetchSubmissions(); // 刷新列表\n              } else {\n                ElMessage.error(response.data.message || '操作失败');\n              }\n              _context5.n = 4;\n              break;\n            case 3:\n              _context5.p = 3;\n              _t5 = _context5.v;\n              if (_t5 !== 'cancel') {\n                console.error('确认漏洞失败:', _t5);\n                ElMessage.error('操作失败，请稍后重试');\n              }\n            case 4:\n              return _context5.a(2);\n          }\n        }, _callee5, null, [[0, 3]]);\n      }));\n      return function confirmVulnerability(_x) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n\n    // 申诉漏洞\n    var disputeVulnerability = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(vulnerability) {\n        var _yield$ElMessageBox$p, disputeReason, response, successMessage, _t6;\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              _context6.p = 0;\n              _context6.n = 1;\n              return ElMessageBox.prompt(\"\\u8BF7\\u8BF4\\u660E\\u5BF9\\u6F0F\\u6D1E\\\"\".concat(vulnerability.title, \"\\\"\\u7684\\u4E89\\u8BAE\\u7406\\u7531\\uFF1A\"), '申诉漏洞', {\n                confirmButtonText: '提交申诉',\n                cancelButtonText: '取消',\n                inputType: 'textarea',\n                inputPlaceholder: '请详细说明争议理由...',\n                inputValidator: function inputValidator(value) {\n                  if (!value || value.trim().length < 10) {\n                    return '争议理由不能少于10个字符';\n                  }\n                  return true;\n                }\n              });\n            case 1:\n              _yield$ElMessageBox$p = _context6.v;\n              disputeReason = _yield$ElMessageBox$p.value;\n              _context6.n = 2;\n              return api.post(\"/vulnerability-workflow/user-confirm/\".concat(vulnerability.id), {\n                action: 'dispute',\n                disputeReason: disputeReason\n              });\n            case 2:\n              response = _context6.v;\n              if (response.data.success) {\n                // 使用服务器返回的消息，包含区块链信息\n                successMessage = response.data.message;\n                if (response.data.data.blockchain) {\n                  successMessage += \"\\n\\u533A\\u5757\\u94FE\\u4EA4\\u6613\\u54C8\\u5E0C: \".concat(response.data.data.blockchain.transactionHash);\n                }\n                ElMessage.success(successMessage);\n                fetchSubmissions(); // 刷新列表\n              } else {\n                ElMessage.error(response.data.message || '申诉提交失败');\n              }\n              _context6.n = 4;\n              break;\n            case 3:\n              _context6.p = 3;\n              _t6 = _context6.v;\n              if (_t6 !== 'cancel') {\n                console.error('申诉漏洞失败:', _t6);\n                ElMessage.error('申诉提交失败，请稍后重试');\n              }\n            case 4:\n              return _context6.a(2);\n          }\n        }, _callee6, null, [[0, 3]]);\n      }));\n      return function disputeVulnerability(_x2) {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n\n    // 关闭详情弹窗\n    var closeDetailDialog = function closeDetailDialog() {\n      detailDialogVisible.value = false;\n      selectedSubmission.value = null;\n    };\n\n    // 获取URL列表\n    var getUrlList = function getUrlList(submission) {\n      if (!submission || !submission.urls) return [];\n      try {\n        var urls = typeof submission.urls === 'string' ? JSON.parse(submission.urls) : submission.urls;\n        return Array.isArray(urls) ? urls.filter(function (url) {\n          return url && url.trim();\n        }) : [];\n      } catch (e) {\n        return [];\n      }\n    };\n\n    // 获取附件链接列表\n    var getAttachmentList = function getAttachmentList(submission) {\n      if (!submission || !submission.attachments) return [];\n      try {\n        var attachments = typeof submission.attachments === 'string' ? JSON.parse(submission.attachments) : submission.attachments;\n        return Array.isArray(attachments) ? attachments.filter(function (link) {\n          return link && link.trim();\n        }) : [];\n      } catch (e) {\n        return [];\n      }\n    };\n\n    // 检查是否有区块链信息\n    var hasBlockchainInfo = function hasBlockchainInfo(submission) {\n      if (!submission) return false;\n      return !!(submission.confirmationTransactionHash || submission.disputeTransactionHash);\n    };\n\n    // 检查是否有漏洞提交区块链信息\n    var hasSubmissionBlockchainInfo = function hasSubmissionBlockchainInfo(submission) {\n      if (!submission) return false;\n      return !!submission.submissionTransactionHash;\n    };\n\n    // 检查是否有企业审批区块链信息\n    var hasApprovalBlockchainInfo = function hasApprovalBlockchainInfo(submission) {\n      if (!submission) return false;\n      return !!submission.approvalTransactionHash;\n    };\n\n    // 检查是否有管理员审核区块链信息\n    var hasAdminReviewBlockchainInfo = function hasAdminReviewBlockchainInfo(submission) {\n      if (!submission) return false;\n      return !!submission.adminReviewTransactionHash;\n    };\n\n    // 获取审批状态类型\n    var getApprovalStatusType = function getApprovalStatusType(submission) {\n      if (!submission || !submission.status) return '';\n      if (submission.status === 'enterprise_confirmed') return 'success';\n      if (submission.status === 'enterprise_rejected') return 'danger';\n      return 'info';\n    };\n\n    // 获取审批状态文本\n    var getApprovalStatusText = function getApprovalStatusText(submission) {\n      if (!submission || !submission.status) return '未审批';\n      if (submission.status === 'enterprise_confirmed') return '已确认';\n      if (submission.status === 'enterprise_rejected') return '已拒绝';\n      return '待审批';\n    };\n\n    // 获取管理员审核状态类型\n    var getAdminReviewStatusType = function getAdminReviewStatusType(submission) {\n      if (!submission || !submission.status) return '';\n      if (submission.status === 'admin_confirmed') return 'success';\n      if (submission.status === 'admin_rejected') return 'danger';\n      return 'info';\n    };\n\n    // 获取管理员审核状态文本\n    var getAdminReviewStatusText = function getAdminReviewStatusText(submission) {\n      if (!submission || !submission.status) return '未审核';\n      if (submission.status === 'admin_confirmed') return '已通过';\n      if (submission.status === 'admin_rejected') return '已拒绝';\n      return '待审核';\n    };\n\n    // 格式化时间戳\n    var formatTimestamp = function formatTimestamp(timestamp) {\n      if (!timestamp) return '未知';\n      try {\n        var date = new Date(timestamp * 1000); // 转换为毫秒\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit'\n        });\n      } catch (error) {\n        return '格式错误';\n      }\n    };\n\n    // 复制到剪贴板\n    var copyToClipboard = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7(text) {\n        var fieldName,\n          textArea,\n          successful,\n          _args7 = arguments,\n          _t7;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.n) {\n            case 0:\n              fieldName = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : '';\n              _context7.p = 1;\n              if (!(navigator.clipboard && window.isSecureContext)) {\n                _context7.n = 3;\n                break;\n              }\n              _context7.n = 2;\n              return navigator.clipboard.writeText(text);\n            case 2:\n              ElMessage.success(fieldName ? \"\".concat(fieldName, \"\\u5DF2\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\") : '已复制到剪贴板');\n              _context7.n = 4;\n              break;\n            case 3:\n              // 降级方案 - 使用传统方法\n              textArea = document.createElement('textarea');\n              textArea.value = text;\n              textArea.style.position = 'fixed';\n              textArea.style.left = '-999999px';\n              textArea.style.top = '-999999px';\n              document.body.appendChild(textArea);\n              textArea.focus();\n              textArea.select();\n\n              // 使用 execCommand 作为降级方案（虽然已弃用但仍然有效）\n              successful = document.execCommand('copy');\n              document.body.removeChild(textArea);\n              if (successful) {\n                ElMessage.success(fieldName ? \"\".concat(fieldName, \"\\u5DF2\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\") : '已复制到剪贴板');\n              } else {\n                ElMessage.error('复制失败，请手动复制');\n              }\n            case 4:\n              _context7.n = 6;\n              break;\n            case 5:\n              _context7.p = 5;\n              _t7 = _context7.v;\n              ElMessage.error('复制失败，请手动复制');\n            case 6:\n              return _context7.a(2);\n          }\n        }, _callee7, null, [[1, 5]]);\n      }));\n      return function copyToClipboard(_x3) {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n\n    // AI严重程度类型映射\n    var getAISeverityType = function getAISeverityType(severity) {\n      var typeMap = {\n        'critical': 'danger',\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info',\n        'info': 'success'\n      };\n      return typeMap[severity] || 'info';\n    };\n\n    // AI严重程度文本映射\n    var getAISeverityText = function getAISeverityText(severity) {\n      var textMap = {\n        'critical': '严重',\n        'high': '高危',\n        'medium': '中危',\n        'low': '低危',\n        'info': '提示'\n      };\n      return textMap[severity] || '未知';\n    };\n\n    // 下载附件（保留原有方法，但可能不再使用）\n    var downloadAttachment = function downloadAttachment(attachment) {\n      // 实现附件下载逻辑\n      var link = document.createElement('a');\n      link.href = attachment.url;\n      link.download = attachment.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    };\n\n    // 获取漏洞类型标签\n    var getVulnTypeLabel = function getVulnTypeLabel(type) {\n      var typeMap = {\n        'sql_injection': 'SQL注入',\n        'xss': 'XSS跨站脚本',\n        'csrf': 'CSRF跨站请求伪造',\n        'file_upload': '文件上传漏洞',\n        'auth_bypass': '身份认证绕过',\n        'privilege_escalation': '权限提升',\n        'info_disclosure': '信息泄露',\n        'rce': '远程代码执行',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    };\n\n    // 获取奖励记录\n    var fetchRewards = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        var response, _t8;\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              loadingRewards.value = true;\n              _context8.p = 1;\n              _context8.n = 2;\n              return fetchBalanceInfo();\n            case 2:\n              _context8.n = 3;\n              return api.get('/rewards/my-rewards');\n            case 3:\n              response = _context8.v;\n              if (response.data.success) {\n                rewards.value = response.data.data.rewards || [];\n                // 使用与提现中心相同的数据源\n                rewardStats.totalEarned = balanceInfo.totalEarnings;\n                rewardStats.thisMonth = response.data.data.thisMonth || 0;\n                rewardStats.totalRewards = rewards.value.length;\n              } else {\n                // 如果API失败，使用余额信息中的总收入\n                rewardStats.totalEarned = balanceInfo.totalEarnings;\n                rewardStats.thisMonth = 0;\n                rewardStats.totalRewards = 0;\n              }\n              _context8.n = 5;\n              break;\n            case 4:\n              _context8.p = 4;\n              _t8 = _context8.v;\n              console.error('获取奖励记录失败:', _t8);\n              // 即使获取奖励记录失败，也要确保总收入数据一致\n              rewardStats.totalEarned = balanceInfo.totalEarnings;\n              rewardStats.thisMonth = 0;\n              rewardStats.totalRewards = 0;\n              ElMessage.error('获取奖励记录失败');\n            case 5:\n              _context8.p = 5;\n              loadingRewards.value = false;\n              return _context8.f(5);\n            case 6:\n              return _context8.a(2);\n          }\n        }, _callee8, null, [[1, 4, 5, 6]]);\n      }));\n      return function fetchRewards() {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n\n    // 刷新提交记录\n    var refreshSubmissions = function refreshSubmissions() {\n      fetchSubmissions();\n    };\n\n    // 刷新奖励记录\n    var refreshRewards = function refreshRewards() {\n      fetchRewards();\n    };\n\n    // 筛选奖励记录\n    var filterRewards = function filterRewards() {\n      fetchRewards();\n    };\n\n    // 获取余额信息\n    var fetchBalanceInfo = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9() {\n        var response, _t9;\n        return _regenerator().w(function (_context9) {\n          while (1) switch (_context9.n) {\n            case 0:\n              _context9.p = 0;\n              _context9.n = 1;\n              return api.get('/withdrawals/balance');\n            case 1:\n              response = _context9.v;\n              if (response.data.success) {\n                balanceInfo.currentBalance = response.data.data.currentBalance;\n                balanceInfo.totalEarnings = response.data.data.totalEarnings;\n              }\n              _context9.n = 3;\n              break;\n            case 2:\n              _context9.p = 2;\n              _t9 = _context9.v;\n              console.error('获取余额信息失败:', _t9);\n            case 3:\n              return _context9.a(2);\n          }\n        }, _callee9, null, [[0, 2]]);\n      }));\n      return function fetchBalanceInfo() {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n\n    // 提交提现申请\n    var submitWithdrawal = /*#__PURE__*/function () {\n      var _ref0 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee0() {\n        var response, _t0;\n        return _regenerator().w(function (_context0) {\n          while (1) switch (_context0.n) {\n            case 0:\n              if (withdrawalFormRef.value) {\n                _context0.n = 1;\n                break;\n              }\n              return _context0.a(2);\n            case 1:\n              _context0.p = 1;\n              _context0.n = 2;\n              return withdrawalFormRef.value.validate();\n            case 2:\n              loadingWithdrawal.value = true;\n              _context0.n = 3;\n              return api.post('/withdrawals/apply', withdrawalForm);\n            case 3:\n              response = _context0.v;\n              if (response.data.success) {\n                ElMessage.success('提现申请提交成功');\n                resetWithdrawalForm();\n                fetchBalanceInfo();\n                fetchWithdrawalHistory();\n              } else {\n                ElMessage.error(response.data.message || '提现申请失败');\n              }\n              _context0.n = 5;\n              break;\n            case 4:\n              _context0.p = 4;\n              _t0 = _context0.v;\n              console.error('提现申请失败:', _t0);\n              if (_t0.response && _t0.response.data && _t0.response.data.message) {\n                ElMessage.error(_t0.response.data.message);\n              } else {\n                ElMessage.error('提现申请失败，请稍后重试');\n              }\n            case 5:\n              _context0.p = 5;\n              loadingWithdrawal.value = false;\n              return _context0.f(5);\n            case 6:\n              return _context0.a(2);\n          }\n        }, _callee0, null, [[1, 4, 5, 6]]);\n      }));\n      return function submitWithdrawal() {\n        return _ref0.apply(this, arguments);\n      };\n    }();\n\n    // 重置提现表单\n    var resetWithdrawalForm = function resetWithdrawalForm() {\n      withdrawalForm.amount = '';\n      withdrawalForm.bankCardNumber = '';\n      withdrawalForm.bankName = '';\n      withdrawalForm.accountHolderName = '';\n      if (withdrawalFormRef.value) {\n        withdrawalFormRef.value.resetFields();\n      }\n    };\n\n    // 获取提现记录\n    var fetchWithdrawalHistory = /*#__PURE__*/function () {\n      var _ref1 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee1() {\n        var response, _t1;\n        return _regenerator().w(function (_context1) {\n          while (1) switch (_context1.n) {\n            case 0:\n              _context1.p = 0;\n              loadingWithdrawalHistory.value = true;\n              _context1.n = 1;\n              return api.get('/withdrawals/my-withdrawals');\n            case 1:\n              response = _context1.v;\n              if (response.data.success) {\n                withdrawalHistory.value = response.data.data;\n              }\n              _context1.n = 3;\n              break;\n            case 2:\n              _context1.p = 2;\n              _t1 = _context1.v;\n              console.error('获取提现记录失败:', _t1);\n              ElMessage.error('获取提现记录失败');\n            case 3:\n              _context1.p = 3;\n              loadingWithdrawalHistory.value = false;\n              return _context1.f(3);\n            case 4:\n              return _context1.a(2);\n          }\n        }, _callee1, null, [[0, 2, 3, 4]]);\n      }));\n      return function fetchWithdrawalHistory() {\n        return _ref1.apply(this, arguments);\n      };\n    }();\n\n    // 获取证书列表\n    var fetchCertificates = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee10() {\n        var params, response, _t10;\n        return _regenerator().w(function (_context10) {\n          while (1) switch (_context10.n) {\n            case 0:\n              _context10.p = 0;\n              loadingCertificates.value = true;\n              params = {\n                page: certificatePagination.value.page,\n                limit: certificatePagination.value.limit\n              };\n              if (certificateFilter.value) {\n                params.severity = certificateFilter.value;\n              }\n              _context10.n = 1;\n              return api.get('/certificates/my-certificates', {\n                params: params\n              });\n            case 1:\n              response = _context10.v;\n              if (response.data.success) {\n                certificates.value = response.data.data;\n                // 更新分页信息\n                if (response.data.pagination) {\n                  certificatePagination.value = _objectSpread(_objectSpread({}, certificatePagination.value), response.data.pagination);\n                }\n              }\n              _context10.n = 3;\n              break;\n            case 2:\n              _context10.p = 2;\n              _t10 = _context10.v;\n              console.error('获取证书列表失败:', _t10);\n              ElMessage.error('获取证书列表失败');\n            case 3:\n              _context10.p = 3;\n              loadingCertificates.value = false;\n              return _context10.f(3);\n            case 4:\n              return _context10.a(2);\n          }\n        }, _callee10, null, [[0, 2, 3, 4]]);\n      }));\n      return function fetchCertificates() {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n\n    // 处理筛选条件变化\n    var handleFilterChange = function handleFilterChange() {\n      certificatePagination.value.page = 1; // 重置到第一页\n      fetchCertificates();\n    };\n\n    // 处理分页变化\n    var handleCertificatePageChange = function handleCertificatePageChange(page) {\n      certificatePagination.value.page = page;\n      fetchCertificates();\n    };\n\n    // 处理每页大小变化\n    var handleCertificatePageSizeChange = function handleCertificatePageSizeChange(size) {\n      certificatePagination.value.limit = size;\n      certificatePagination.value.page = 1; // 重置到第一页\n      fetchCertificates();\n    };\n\n    // 查看证书详情\n    var viewCertificateDetail = /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee11(certificate) {\n        var response, _t11;\n        return _regenerator().w(function (_context11) {\n          while (1) switch (_context11.n) {\n            case 0:\n              _context11.p = 0;\n              _context11.n = 1;\n              return api.get(\"/certificates/\".concat(certificate.id));\n            case 1:\n              response = _context11.v;\n              if (response.data.success) {\n                selectedCertificate.value = response.data.data;\n                showCertificateDetail.value = true;\n              } else {\n                ElMessage.error('获取证书详情失败');\n              }\n              _context11.n = 3;\n              break;\n            case 2:\n              _context11.p = 2;\n              _t11 = _context11.v;\n              console.error('获取证书详情失败:', _t11);\n              ElMessage.error('获取证书详情失败');\n            case 3:\n              return _context11.a(2);\n          }\n        }, _callee11, null, [[0, 2]]);\n      }));\n      return function viewCertificateDetail(_x4) {\n        return _ref11.apply(this, arguments);\n      };\n    }();\n\n    // 企业用户账户相关方法\n    // 获取账户余额信息\n    var fetchAccountBalance = /*#__PURE__*/function () {\n      var _ref12 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee12() {\n        var response, _t12;\n        return _regenerator().w(function (_context12) {\n          while (1) switch (_context12.n) {\n            case 0:\n              _context12.p = 0;\n              _context12.n = 1;\n              return api.get('/withdrawals/balance');\n            case 1:\n              response = _context12.v;\n              if (response.data.success) {\n                accountBalance.currentBalance = response.data.data.currentBalance;\n                // 企业用户的totalSpent需要单独计算或从API获取\n                accountBalance.totalSpent = 0; // 暂时设为0，后续可以添加API\n              }\n              _context12.n = 3;\n              break;\n            case 2:\n              _context12.p = 2;\n              _t12 = _context12.v;\n              console.error('获取账户余额失败:', _t12);\n            case 3:\n              return _context12.a(2);\n          }\n        }, _callee12, null, [[0, 2]]);\n      }));\n      return function fetchAccountBalance() {\n        return _ref12.apply(this, arguments);\n      };\n    }();\n\n    // 获取我发布的任务\n    var fetchMyTasks = /*#__PURE__*/function () {\n      var _ref13 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee13() {\n        var response, _t13;\n        return _regenerator().w(function (_context13) {\n          while (1) switch (_context13.n) {\n            case 0:\n              _context13.p = 0;\n              loadingMyTasks.value = true;\n              _context13.n = 1;\n              return api.get('/security-tasks/my/tasks');\n            case 1:\n              response = _context13.v;\n              if (response.data.success) {\n                myTasks.value = response.data.data.tasks;\n              }\n              _context13.n = 3;\n              break;\n            case 2:\n              _context13.p = 2;\n              _t13 = _context13.v;\n              console.error('获取我的任务失败:', _t13);\n              ElMessage.error('获取任务列表失败');\n            case 3:\n              _context13.p = 3;\n              loadingMyTasks.value = false;\n              return _context13.f(3);\n            case 4:\n              return _context13.a(2);\n          }\n        }, _callee13, null, [[0, 2, 3, 4]]);\n      }));\n      return function fetchMyTasks() {\n        return _ref13.apply(this, arguments);\n      };\n    }();\n\n    // 提交充值\n    var submitRecharge = /*#__PURE__*/function () {\n      var _ref14 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee14() {\n        var response, _t14;\n        return _regenerator().w(function (_context14) {\n          while (1) switch (_context14.n) {\n            case 0:\n              if (rechargeFormRef.value) {\n                _context14.n = 1;\n                break;\n              }\n              return _context14.a(2);\n            case 1:\n              _context14.p = 1;\n              _context14.n = 2;\n              return rechargeFormRef.value.validate();\n            case 2:\n              rechargingLoading.value = true;\n\n              // 调用充值API\n              _context14.n = 3;\n              return api.post('/recharge/create-order', {\n                amount: rechargeForm.amount,\n                paymentMethod: rechargeForm.paymentMethod\n              });\n            case 3:\n              response = _context14.v;\n              if (response.data.success) {\n                // 更新余额\n                accountBalance.currentBalance = response.data.data.newBalance;\n                ElMessage.success(\"\\u5145\\u503C\\u6210\\u529F\\uFF01\\u5DF2\\u4E3A\\u60A8\\u7684\\u8D26\\u6237\\u5145\\u503C \\xA5\".concat(rechargeForm.amount));\n\n                // 重置表单\n                rechargeForm.amount = 1000;\n\n                // 刷新账户余额\n                fetchAccountBalance();\n              } else {\n                ElMessage.error(response.data.message || '充值失败');\n              }\n              _context14.n = 5;\n              break;\n            case 4:\n              _context14.p = 4;\n              _t14 = _context14.v;\n              console.error('充值失败:', _t14);\n              if (_t14.response && _t14.response.data) {\n                ElMessage.error(_t14.response.data.message || '充值失败，请稍后重试');\n              } else {\n                ElMessage.error('充值失败，请稍后重试');\n              }\n            case 5:\n              _context14.p = 5;\n              rechargingLoading.value = false;\n              return _context14.f(5);\n            case 6:\n              return _context14.a(2);\n          }\n        }, _callee14, null, [[1, 4, 5, 6]]);\n      }));\n      return function submitRecharge() {\n        return _ref14.apply(this, arguments);\n      };\n    }();\n\n    // 查看任务详情\n    var viewTaskDetail = function viewTaskDetail(task) {\n      selectedTask.value = task;\n      taskDetailVisible.value = true;\n    };\n\n    // 跳转到审核漏洞页面\n    var goToReviewTask = function goToReviewTask() {\n      taskDetailVisible.value = false;\n      router.push('/enterprise/review');\n    };\n\n    // 获取任务状态类型\n    var getTaskStatusType = function getTaskStatusType(status) {\n      var statusMap = {\n        draft: 'info',\n        published: 'success',\n        in_progress: 'warning',\n        completed: '',\n        cancelled: 'danger'\n      };\n      return statusMap[status] || '';\n    };\n\n    // 获取任务状态文本\n    var getTaskStatusText = function getTaskStatusText(status) {\n      var statusMap = {\n        draft: '草稿',\n        published: '已发布',\n        in_progress: '进行中',\n        completed: '已完成',\n        cancelled: '已取消'\n      };\n      return statusMap[status] || status;\n    };\n\n    // 获取提现状态类型\n    var getWithdrawalStatusType = function getWithdrawalStatusType(status) {\n      var statusMap = {\n        pending: '',\n        processing: 'warning',\n        completed: 'success',\n        failed: 'danger',\n        cancelled: 'info'\n      };\n      return statusMap[status] || '';\n    };\n\n    // 获取提现状态文本\n    var getWithdrawalStatusText = function getWithdrawalStatusText(status) {\n      var statusMap = {\n        pending: '待处理',\n        processing: '处理中',\n        completed: '已完成',\n        failed: '失败',\n        cancelled: '已取消'\n      };\n      return statusMap[status] || status;\n    };\n\n    // 生成模拟区块链编号\n    var generateBlockchainHash = function generateBlockchainHash() {\n      var timestamp = Date.now().toString(16);\n      var random = Math.random().toString(36).substring(2, 15);\n      return \"0x\".concat(timestamp).concat(random).toLowerCase();\n    };\n\n    // 获取漏洞状态类型\n    var getVulnStatusType = function getVulnStatusType(status) {\n      var statusMap = {\n        pending: 'warning',\n        confirmed: 'success',\n        rejected: 'danger',\n        fixed: 'info',\n        duplicate: 'info'\n      };\n      return statusMap[status] || '';\n    };\n\n    // 获取漏洞状态文本\n    var getVulnStatusText = function getVulnStatusText(status) {\n      var statusMap = {\n        pending: '待审核',\n        confirmed: '已确认',\n        rejected: '已拒绝',\n        fixed: '已修复',\n        duplicate: '重复'\n      };\n      return statusMap[status] || status;\n    };\n\n    // 下载证书\n    var downloadCertificate = /*#__PURE__*/function () {\n      var _ref15 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee15() {\n        var certificateElement, printWindow, certificateHTML, styles, _t15;\n        return _regenerator().w(function (_context15) {\n          while (1) switch (_context15.n) {\n            case 0:\n              if (selectedCertificate.value) {\n                _context15.n = 1;\n                break;\n              }\n              return _context15.a(2);\n            case 1:\n              _context15.p = 1;\n              ElMessage.info('正在生成PDF证书，请稍候...');\n\n              // 检查是否支持下载功能\n              if (!(typeof window === 'undefined')) {\n                _context15.n = 2;\n                break;\n              }\n              ElMessage.error('当前环境不支持下载功能');\n              return _context15.a(2);\n            case 2:\n              // 获取证书内容元素\n              certificateElement = document.getElementById('certificate-content');\n              if (certificateElement) {\n                _context15.n = 3;\n                break;\n              }\n              ElMessage.error('证书内容未找到');\n              return _context15.a(2);\n            case 3:\n              // 临时实现：使用浏览器打印功能\n              printWindow = window.open('', '_blank');\n              if (printWindow) {\n                _context15.n = 4;\n                break;\n              }\n              ElMessage.error('无法打开打印窗口，请检查浏览器设置');\n              return _context15.a(2);\n            case 4:\n              // 复制证书内容到新窗口\n              certificateHTML = certificateElement.outerHTML; // 获取当前页面的所有CSS样式\n              styles = Array.from(document.styleSheets).map(function (styleSheet) {\n                try {\n                  return Array.from(styleSheet.cssRules).map(function (rule) {\n                    return rule.cssText;\n                  }).join('\\n');\n                } catch (e) {\n                  return '';\n                }\n              }).join('\\n'); // 使用document.write方法逐步构建页面\n              printWindow.document.open();\n              printWindow.document.write('<!DOCTYPE html>');\n              printWindow.document.write('<html><head><title>安全漏洞发现证书</title>');\n              printWindow.document.write('<meta charset=\"UTF-8\">');\n              printWindow.document.write('<style>');\n              printWindow.document.write(styles);\n              printWindow.document.write(\"\\n          body {\\n            margin: 0;\\n            padding: 20px;\\n            font-family: 'Microsoft YaHei', serif;\\n            background: white;\\n          }\\n          .certificate-paper {\\n            box-shadow: none !important;\\n            background: white !important;\\n            min-height: auto !important;\\n          }\\n          .certificate-border {\\n            border: 8px solid #d4af37 !important;\\n            padding: 40px !important;\\n            background: white !important;\\n          }\\n          .logo-circle {\\n            width: 60px !important;\\n            height: 60px !important;\\n            background: #d4af37 !important;\\n            border-radius: 50% !important;\\n            display: flex !important;\\n            align-items: center !important;\\n            justify-content: center !important;\\n            margin: 0 auto 20px !important;\\n          }\\n          .logo-circle svg {\\n            width: 40px !important;\\n            height: 40px !important;\\n            fill: white !important;\\n          }\\n          .certificate-logo {\\n            text-align: center !important;\\n            margin-bottom: 20px !important;\\n          }\\n          @media print {\\n            body {\\n              margin: 0 !important;\\n              padding: 0 !important;\\n            }\\n            .certificate-paper {\\n              width: 100% !important;\\n              height: auto !important;\\n              box-shadow: none !important;\\n              page-break-inside: avoid !important;\\n            }\\n            .certificate-border {\\n              page-break-inside: avoid !important;\\n            }\\n            .logo-circle {\\n              -webkit-print-color-adjust: exact !important;\\n              color-adjust: exact !important;\\n            }\\n            .logo-circle svg {\\n              -webkit-print-color-adjust: exact !important;\\n              color-adjust: exact !important;\\n            }\\n          }\\n        \");\n              printWindow.document.write('</style>');\n              printWindow.document.write('</head><body>');\n              printWindow.document.write(certificateHTML);\n              printWindow.document.write('</body></html>');\n              printWindow.document.close();\n\n              // 延迟打印以确保内容加载完成\n              setTimeout(function () {\n                printWindow.print();\n                // 打印后延迟关闭窗口\n                setTimeout(function () {\n                  printWindow.close();\n                }, 2000);\n              }, 1000);\n              ElMessage.success('请在打印对话框中选择\"另存为PDF\"，或选择\"Microsoft Print to PDF\"');\n              _context15.n = 6;\n              break;\n            case 5:\n              _context15.p = 5;\n              _t15 = _context15.v;\n              console.error('下载证书失败:', _t15);\n              ElMessage.error('证书下载失败，请重试');\n            case 6:\n              return _context15.a(2);\n          }\n        }, _callee15, null, [[1, 5]]);\n      }));\n      return function downloadCertificate() {\n        return _ref15.apply(this, arguments);\n      };\n    }();\n\n    // 监听 props.activeTab 变化（路由跳转时）\n    watch(function () {\n      return props.activeTab;\n    }, function (newActiveTab) {\n      currentTab.value = newActiveTab || 'info';\n    });\n\n    // 监听标签页切换\n    watch(currentTab, function (newTab) {\n      if (newTab === 'submissions') {\n        fetchSubmissions();\n      } else if (newTab === 'rewards') {\n        fetchRewards();\n      } else if (newTab === 'withdrawal') {\n        fetchBalanceInfo();\n        fetchWithdrawalHistory();\n      } else if (newTab === 'certificates') {\n        fetchCertificates();\n      } else if (newTab === 'account') {\n        // 企业用户账户标签页\n        fetchAccountBalance();\n        fetchMyTasks();\n      }\n    });\n    onMounted(function () {\n      loadUserInfo();\n      if (props.activeTab === 'submissions') {\n        fetchSubmissions();\n      } else if (props.activeTab === 'rewards') {\n        fetchRewards();\n      } else if (props.activeTab === 'withdrawal') {\n        fetchBalanceInfo();\n        fetchWithdrawalHistory();\n      } else if (props.activeTab === 'certificates') {\n        fetchCertificates();\n      } else if (props.activeTab === 'account') {\n        // 企业用户账户标签页\n        fetchAccountBalance();\n        fetchMyTasks();\n      }\n    });\n\n    // 工具函数\n    var formatDate = function formatDate(date) {\n      if (!date) return '';\n      return new Date(date).toLocaleDateString('zh-CN');\n    };\n    var formatDateTime = function formatDateTime(date) {\n      if (!date) return '';\n      return new Date(date).toLocaleString('zh-CN');\n    };\n    var formatNumber = function formatNumber(num) {\n      if (!num) return '0';\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    };\n    var getSeverityType = function getSeverityType(severity) {\n      var typeMap = {\n        critical: 'danger',\n        high: 'danger',\n        medium: 'warning',\n        low: 'info',\n        info: 'info'\n      };\n      return typeMap[severity] || 'info';\n    };\n    var getSeverityText = function getSeverityText(severity) {\n      var textMap = {\n        critical: '严重',\n        high: '高危',\n        medium: '中危',\n        low: '低危',\n        info: '提示'\n      };\n      return textMap[severity] || severity;\n    };\n    var getStatusType = function getStatusType(status) {\n      var typeMap = {\n        pending: 'warning',\n        enterprise_confirmed: 'primary',\n        enterprise_rejected: 'danger',\n        user_confirmed: 'success',\n        dispute: 'warning',\n        admin_confirmed: 'success',\n        admin_rejected: 'danger',\n        auto_confirmed: 'success',\n        fixed: 'info',\n        duplicate: 'info'\n      };\n      return typeMap[status] || 'info';\n    };\n    var getStatusText = function getStatusText(status) {\n      var textMap = {\n        pending: '待企业审核',\n        enterprise_confirmed: '待用户确认',\n        enterprise_rejected: '企业已拒绝',\n        user_confirmed: '用户已确认',\n        dispute: '争议中',\n        admin_confirmed: '管理员确认',\n        admin_rejected: '管理员拒绝',\n        auto_confirmed: '自动确认',\n        fixed: '已修复',\n        duplicate: '重复漏洞'\n      };\n      return textMap[status] || status;\n    };\n    var getRewardType = function getRewardType(type) {\n      var typeMap = {\n        vulnerability: 'success',\n        bonus: 'warning',\n        activity: 'info'\n      };\n      return typeMap[type] || 'info';\n    };\n    var getRewardTypeText = function getRewardTypeText(type) {\n      var textMap = {\n        vulnerability: '漏洞奖励',\n        bonus: '额外奖励',\n        activity: '活动奖励'\n      };\n      return textMap[type] || type;\n    };\n    var getPaymentStatusType = function getPaymentStatusType(status) {\n      var typeMap = {\n        pending: 'warning',\n        paid: 'success',\n        failed: 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n    var getPaymentStatusText = function getPaymentStatusText(status) {\n      var textMap = {\n        pending: '待发放',\n        paid: '已发放',\n        failed: '发放失败'\n      };\n      return textMap[status] || status;\n    };\n    var viewRewardDetail = function viewRewardDetail(reward) {\n      // 显示奖励详情\n      ElMessageBox.alert(\"\\u5956\\u52B1\\u91D1\\u989D\\uFF1A\\xA5\".concat(formatNumber(reward.amount), \"\\n\\u5956\\u52B1\\u7C7B\\u578B\\uFF1A\").concat(getRewardTypeText(reward.type), \"\\n\\u53D1\\u653E\\u72B6\\u6001\\uFF1A\").concat(getPaymentStatusText(reward.status)), '奖励详情', {\n        confirmButtonText: '确定'\n      });\n    };\n    return {\n      user: user,\n      currentTab: currentTab,\n      loading: loading,\n      saving: saving,\n      editMode: editMode,\n      editForm: editForm,\n      userRules: userRules,\n      userFormRef: userFormRef,\n      enableEditMode: enableEditMode,\n      cancelEdit: cancelEdit,\n      saveUserInfo: saveUserInfo,\n      // 头像上传相关\n      uploadLoading: uploadLoading,\n      avatarUrl: avatarUrl,\n      uploadUrl: uploadUrl,\n      uploadHeaders: uploadHeaders,\n      beforeAvatarUpload: beforeAvatarUpload,\n      handleAvatarSuccess: handleAvatarSuccess,\n      handleAvatarError: handleAvatarError,\n      passwordForm: passwordForm,\n      passwordRules: passwordRules,\n      passwordFormRef: passwordFormRef,\n      changePassword: changePassword,\n      submissions: submissions,\n      loadingSubmissions: loadingSubmissions,\n      submissionFilter: submissionFilter,\n      submissionStats: submissionStats,\n      fetchSubmissions: fetchSubmissions,\n      refreshSubmissions: refreshSubmissions,\n      rewards: rewards,\n      loadingRewards: loadingRewards,\n      rewardDateRange: rewardDateRange,\n      rewardStats: rewardStats,\n      fetchRewards: fetchRewards,\n      refreshRewards: refreshRewards,\n      filterRewards: filterRewards,\n      formatDate: formatDate,\n      formatDateTime: formatDateTime,\n      formatNumber: formatNumber,\n      getSeverityType: getSeverityType,\n      getSeverityText: getSeverityText,\n      getStatusType: getStatusType,\n      getStatusText: getStatusText,\n      getRewardType: getRewardType,\n      getRewardTypeText: getRewardTypeText,\n      getPaymentStatusType: getPaymentStatusType,\n      getPaymentStatusText: getPaymentStatusText,\n      viewSubmissionDetail: viewSubmissionDetail,\n      confirmVulnerability: confirmVulnerability,\n      disputeVulnerability: disputeVulnerability,\n      viewRewardDetail: viewRewardDetail,\n      detailDialogVisible: detailDialogVisible,\n      selectedSubmission: selectedSubmission,\n      closeDetailDialog: closeDetailDialog,\n      downloadAttachment: downloadAttachment,\n      getVulnTypeLabel: getVulnTypeLabel,\n      getUrlList: getUrlList,\n      getAttachmentList: getAttachmentList,\n      hasBlockchainInfo: hasBlockchainInfo,\n      hasSubmissionBlockchainInfo: hasSubmissionBlockchainInfo,\n      hasApprovalBlockchainInfo: hasApprovalBlockchainInfo,\n      hasAdminReviewBlockchainInfo: hasAdminReviewBlockchainInfo,\n      getApprovalStatusType: getApprovalStatusType,\n      getApprovalStatusText: getApprovalStatusText,\n      getAdminReviewStatusType: getAdminReviewStatusType,\n      getAdminReviewStatusText: getAdminReviewStatusText,\n      formatTimestamp: formatTimestamp,\n      copyToClipboard: copyToClipboard,\n      getAISeverityType: getAISeverityType,\n      getAISeverityText: getAISeverityText,\n      // 提现相关\n      balanceInfo: balanceInfo,\n      showWithdrawalHistory: showWithdrawalHistory,\n      loadingWithdrawal: loadingWithdrawal,\n      loadingWithdrawalHistory: loadingWithdrawalHistory,\n      withdrawalHistory: withdrawalHistory,\n      withdrawalFormRef: withdrawalFormRef,\n      withdrawalForm: withdrawalForm,\n      withdrawalRules: withdrawalRules,\n      submitWithdrawal: submitWithdrawal,\n      resetWithdrawalForm: resetWithdrawalForm,\n      fetchWithdrawalHistory: fetchWithdrawalHistory,\n      getWithdrawalStatusType: getWithdrawalStatusType,\n      getWithdrawalStatusText: getWithdrawalStatusText,\n      // 证书相关\n      certificates: certificates,\n      loadingCertificates: loadingCertificates,\n      certificateFilter: certificateFilter,\n      certificatePagination: certificatePagination,\n      fetchCertificates: fetchCertificates,\n      handleFilterChange: handleFilterChange,\n      handleCertificatePageChange: handleCertificatePageChange,\n      handleCertificatePageSizeChange: handleCertificatePageSizeChange,\n      viewCertificateDetail: viewCertificateDetail,\n      // 证书详情相关\n      showCertificateDetail: showCertificateDetail,\n      selectedCertificate: selectedCertificate,\n      getVulnStatusType: getVulnStatusType,\n      getVulnStatusText: getVulnStatusText,\n      downloadCertificate: downloadCertificate,\n      generateBlockchainHash: generateBlockchainHash,\n      // 企业用户账户相关\n      accountBalance: accountBalance,\n      myTasks: myTasks,\n      loadingMyTasks: loadingMyTasks,\n      rechargingLoading: rechargingLoading,\n      rechargeFormRef: rechargeFormRef,\n      rechargeForm: rechargeForm,\n      rechargeRules: rechargeRules,\n      fetchAccountBalance: fetchAccountBalance,\n      fetchMyTasks: fetchMyTasks,\n      submitRecharge: submitRecharge,\n      viewTaskDetail: viewTaskDetail,\n      goToReviewTask: goToReviewTask,\n      taskDetailVisible: taskDetailVisible,\n      selectedTask: selectedTask,\n      getTaskStatusType: getTaskStatusType,\n      getTaskStatusText: getTaskStatusText\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}