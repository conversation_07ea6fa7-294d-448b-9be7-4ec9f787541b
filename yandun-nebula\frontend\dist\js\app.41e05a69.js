(()=>{"use strict";var e={20907:(e,a,t)=>{t.d(a,{A:()=>_});var n=t(24059),r=t(698),i=(t(76918),t(51629),t(25276),t(44114),t(54554),t(59089),t(60739),t(23288),t(18111),t(7588),t(33110),t(79432),t(26099),t(16034),t(58940),t(27495),t(38781),t(25440),t(2945),t(23500),t(55815),t(64979),t(79739),t(36149)),s=[],l=function(e){s.push(e)},c=function(e){var a=s.indexOf(e);a>-1&&s.splice(a,1)},o=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;s.forEach(function(t){try{t(e,a)}catch(n){console.error("登录状态监听器执行失败:",n)}})},d={LOCAL:"local",SESSION:"session"},u={TOKEN:"token",USER:"user",REFRESH_TOKEN:"refresh_token"},v=3e5,f=function(e){if(!e)return!0;try{var a=e.split(".")[1],t=a.replace(/-/g,"+").replace(/_/g,"/"),n=JSON.parse(window.atob(t)),r=1e3*n.exp,i=Date.now();return r<i||(r-i<v&&w(),!1)}catch(s){return console.error("Token解析失败:",s),!0}},p=function(e){var a=sessionStorage.getItem(e);return a||(a=localStorage.getItem(e)),a},h=function(e,a){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d.SESSION,n=t===d.LOCAL?localStorage:sessionStorage,r=t===d.LOCAL?sessionStorage:localStorage;n.setItem(e,a),r.removeItem(e)},m=function(){Object.values(u).forEach(function(e){localStorage.removeItem(e),sessionStorage.removeItem(e)})},b=function(){var e=p(u.USER);if(!e)return null;try{return JSON.parse(e)}catch(a){return console.error("解析用户信息失败:",a),null}},k=function(){return p(u.TOKEN)},g=function(){var e=k(),a=b();return!(!e||!a)},w=function(){var e=(0,r.A)((0,n.A)().m(function e(){var a,t,r;return(0,n.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,i.A.post("/auth/refresh",{token:k()});case 1:if(a=e.v,!a.data.success){e.n=2;break}return t=localStorage.getItem(u.TOKEN)?d.LOCAL:d.SESSION,h(u.TOKEN,a.data.token,t),e.a(2,!0);case 2:e.n=4;break;case 3:return e.p=3,r=e.v,console.error("刷新token失败:",r),e.a(2,!1);case 4:return e.a(2)}},e,null,[[0,3]])}));return function(){return e.apply(this,arguments)}}(),y=function(e,a){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=t?d.LOCAL:d.SESSION;h(u.TOKEN,e,n),h(u.USER,JSON.stringify(a),n),o(!0,a)},L=function(){var e=(0,r.A)((0,n.A)().m(function e(){var a,t,r,s,l,c,v,p;return(0,n.A)().w(function(e){while(1)switch(e.n){case 0:if(a=k(),t=b(),a&&t){e.n=1;break}return m(),e.a(2,!1);case 1:if(!f(a)){e.n=3;break}return e.n=2,w();case 2:if(r=e.v,r){e.n=3;break}return m(),e.a(2,!1);case 3:if(s=sessionStorage.getItem("lastValidation"),l=Date.now(),s&&!(l-parseInt(s)>6e5)){e.n=9;break}return e.p=4,e.n=5,i.A.get("/auth/me");case 5:if(c=e.v,!c.data.success){e.n=6;break}return v=localStorage.getItem(u.TOKEN)?d.LOCAL:d.SESSION,h(u.USER,JSON.stringify(c.data.user),v),sessionStorage.setItem("lastValidation",l.toString()),e.a(2,!0);case 6:e.n=9;break;case 7:if(e.p=7,p=e.v,console.error("后端验证失败:",p),!p.response||401!==p.response.status){e.n=8;break}return m(),o(!1,null),e.a(2,!1);case 8:return e.a(2,!0);case 9:return e.a(2,!0)}},e,null,[[4,7]])}));return function(){return e.apply(this,arguments)}}(),F=function(){m(),o(!1,null)};const _={isLoggedIn:g,getCurrentUser:b,getUserInfo:b,getToken:k,saveLoginInfo:y,validateLoginStatus:L,logout:F,addLoginStatusListener:l,removeLoginStatusListener:c,STORAGE_TYPE:d,STORAGE_KEYS:u,refreshToken:w}},33153:(e,a,t)=>{e.exports=t.p+"img/logo.97dd6513.png"},36149:(e,a,t)=>{t.d(a,{A:()=>s});t(26099);var n=t(95113),r=t(18057),i=n.A.create({baseURL:"/api",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});console.log("API配置:",{baseURL:i.defaults.baseURL,timeout:i.defaults.timeout,headers:i.defaults.headers}),i.interceptors.request.use(function(e){var a=sessionStorage.getItem("token");return a||(a=localStorage.getItem("token")),a&&(e.headers.Authorization="Bearer ".concat(a)),console.log("发送请求:",e.url,e.method,e.data),e},function(e){return console.error("请求错误:",e),Promise.reject(e)}),i.interceptors.response.use(function(e){return console.log("收到响应:",{url:e.config.url,method:e.config.method,status:e.status,data:e.data}),e},function(e){if(console.error("响应错误:",{config:e.config,message:e.message,stack:e.stack}),e.response)switch(console.error("错误详情:",{url:e.config?e.config.url:"unknown",method:e.config?e.config.method:"unknown",status:e.response.status,data:e.response.data,headers:e.response.headers}),e.response.status){case 400:console.error("请求参数错误:",e.response.data),r.nk.error(e.response.data.message||"请求参数错误");break;case 401:console.error("认证失败:",e.response.data),r.nk.error(e.response.data.message||"未授权，请重新登录");break;case 403:console.error("权限不足:",e.response.data),r.nk.error(e.response.data.message||"拒绝访问");break;case 404:console.error("资源不存在:",e.response.data),r.nk.error(e.response.data.message||"请求的资源不存在");break;case 500:console.error("服务器错误:",e.response.data),r.nk.error(e.response.data.message||"服务器内部错误");break;default:console.error("连接错误 ".concat(e.response.status,":"),e.response.data),r.nk.error(e.response.data.message||"连接错误 ".concat(e.response.status))}else e.request?(console.error("网络错误，无法连接到服务器:",{request:e.request,config:e.config,message:e.message}),r.nk.error("网络错误，无法连接到服务器，请确保后端服务已启动")):(console.error("请求配置错误:",{message:e.message,config:e.config}),r.nk.error("请求配置错误: "+e.message));return Promise.reject(e)});const s=i},40267:(e,a,t)=>{var n=t(67951),r=t(8366),i=t(48816),s=t(45820),l=t(49616),c=(t(23792),t(3362),t(69085),t(9391),t(5506),t(76031),t(29746)),o=t(29193),d=(t(36271),t(17383)),u=t(95976);function v(e,a,t,n,r,i){var s=(0,u.g2)("router-view");return(0,u.uX)(),(0,u.Wv)(s)}const f={name:"App"};var p=t(1169);const h=(0,p.A)(f,[["render",v]]),m=h;var b=t(24059),k=t(698),g=(t(26099),t(47764),t(62953),t(39053)),w=t(10160),y={class:"home-container"},L={class:"banner"},F={class:"carousel-background"},_={class:"carousel-indicators"},C=["onClick"],x={class:"carousel-controls"},A={class:"particles-bg",ref:"particlesBg"},S={class:"banner-content"},T={class:"banner-text","data-aos":"fade-up","data-aos-duration":"1000"},z={class:"banner-actions","data-aos":"fade-up","data-aos-delay":"600"},M={class:"banner-visual","data-aos":"fade-left","data-aos-delay":"800"},E={class:"cyber-grid"},V={class:"floating-elements"},R={class:"floating-icon network"},W={class:"floating-icon security"},H={class:"floating-icon data"},I={class:"floating-icon warning"},B={class:"floating-icon monitor"},X={class:"scroll-arrow"};function D(e,a,t,n,r,i){var s=(0,u.g2)("TheHeader"),l=(0,u.g2)("ArrowLeft"),c=(0,u.g2)("el-icon"),o=(0,u.g2)("ArrowRight"),d=(0,u.g2)("QuestionFilled"),v=(0,u.g2)("Connection"),f=(0,u.g2)("Lock"),p=(0,u.g2)("TrendCharts"),h=(0,u.g2)("WarningFilled"),m=(0,u.g2)("Cpu"),b=(0,u.g2)("ArrowDown"),k=(0,u.g2)("AppFooter");return(0,u.uX)(),(0,u.CE)("div",y,[(0,u.bF)(s),(0,u.Lk)("section",L,[(0,u.Lk)("div",F,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.backgroundImages,function(e,a){return(0,u.uX)(),(0,u.CE)("div",{key:a,class:(0,w.C4)(["carousel-slide",{active:n.currentSlide===a}]),style:(0,w.Tr)({backgroundImage:"url(".concat(e,")")})},null,6)}),128))]),(0,u.Lk)("div",_,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.backgroundImages,function(e,a){return(0,u.uX)(),(0,u.CE)("button",{key:a,class:(0,w.C4)(["indicator",{active:n.currentSlide===a}]),onClick:function(e){return n.goToSlide(a)}},null,10,C)}),128))]),(0,u.Lk)("div",x,[(0,u.Lk)("button",{class:"control-btn prev",onClick:a[0]||(a[0]=function(){return n.prevSlide&&n.prevSlide.apply(n,arguments)})},[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(l)]}),_:1})]),(0,u.Lk)("button",{class:"control-btn next",onClick:a[1]||(a[1]=function(){return n.nextSlide&&n.nextSlide.apply(n,arguments)})},[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(o)]}),_:1})])]),(0,u.Lk)("div",A,null,512),a[10]||(a[10]=(0,u.Lk)("div",{class:"banner-overlay"},null,-1)),(0,u.Lk)("div",S,[(0,u.Lk)("div",T,[a[6]||(a[6]=(0,u.Fv)('<h1 class="banner-title" data-v-fc43bd8e><span class="title-line" data-v-fc43bd8e>网络安全</span><span class="title-line highlight" data-v-fc43bd8e>我们共同的责任</span></h1><p class="banner-subtitle" data-aos="fade-up" data-aos-delay="200" data-v-fc43bd8e><span class="typing-text" data-v-fc43bd8e>加入衍盾星云 · 安全可信</span></p><p class="banner-desc" data-aos="fade-up" data-aos-delay="400" data-v-fc43bd8e> 和众多企业一起，共建网络空间共同体 </p>',3)),(0,u.Lk)("div",z,[(0,u.Lk)("button",{class:"btn-primary glow-btn",onClick:a[2]||(a[2]=function(){return n.goToLogin&&n.goToLogin.apply(n,arguments)})}," 立即开始 "),(0,u.Lk)("button",{class:"btn-secondary",onClick:a[3]||(a[3]=function(){return n.goToHelp&&n.goToHelp.apply(n,arguments)})},[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(d)]}),_:1}),a[5]||(a[5]=(0,u.eW)(" 帮助中心 "))])])]),(0,u.Lk)("div",M,[(0,u.Lk)("div",E,[a[7]||(a[7]=(0,u.Lk)("div",{class:"grid-lines"},null,-1)),(0,u.Lk)("div",V,[(0,u.Lk)("div",R,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(v)]}),_:1})]),(0,u.Lk)("div",W,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(f)]}),_:1})]),(0,u.Lk)("div",H,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(p)]}),_:1})]),(0,u.Lk)("div",I,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(h)]}),_:1})]),(0,u.Lk)("div",B,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(m)]}),_:1})])]),a[8]||(a[8]=(0,u.Lk)("div",{class:"grid-overlay"},null,-1))])])]),(0,u.Lk)("div",{class:"scroll-hint",onClick:a[4]||(a[4]=function(){return n.scrollToNext&&n.scrollToNext.apply(n,arguments)})},[a[9]||(a[9]=(0,u.Lk)("div",{class:"scroll-text"},"了解更多请向下滑动~",-1)),(0,u.Lk)("div",X,[(0,u.bF)(c,null,{default:(0,u.k6)(function(){return[(0,u.bF)(b)]}),_:1})])])]),a[11]||(a[11]=(0,u.Fv)('<section class="philosophy" data-v-fc43bd8e><div class="container" data-v-fc43bd8e><h2 class="section-title" data-aos="fade-up" data-v-fc43bd8e><span class="title-bg" data-v-fc43bd8e>创新 · 共赢 · 安全 · 信任</span></h2><div class="philosophy-grid" data-aos="fade-up" data-aos-delay="200" data-v-fc43bd8e><div class="philosophy-item" data-v-fc43bd8e><div class="philosophy-icon innovation" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF6B35" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FF8A65" data-v-fc43bd8e></path><path d="M512 256c141.376 0 256 114.624 256 256s-114.624 256-256 256-256-114.624-256-256 114.624-256 256-256z" fill="#FFB74D" data-v-fc43bd8e></path><path d="M448 384h128v128h-128z" fill="#FFF" data-v-fc43bd8e></path><path d="M480 320h64v64h-64z" fill="#FFF" data-v-fc43bd8e></path><path d="M480 576h64v64h-64z" fill="#FFF" data-v-fc43bd8e></path></svg></div><h3 data-v-fc43bd8e>创新驱动</h3><p data-v-fc43bd8e>持续技术创新，引领安全发展</p></div><div class="philosophy-item" data-v-fc43bd8e><div class="philosophy-icon cooperation" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#4CAF50" data-v-fc43bd8e></path><path d="M320 384c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF" data-v-fc43bd8e></path><path d="M704 384c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF" data-v-fc43bd8e></path><path d="M512 448c35.346 0 64-28.654 64-64s-28.654-64-64-64-64 28.654-64 64 28.654 64 64 64z" fill="#FFF" data-v-fc43bd8e></path><path d="M256 576h512v64H256z" fill="#FFF" data-v-fc43bd8e></path><path d="M320 640v64h64v-64h256v64h64v-64" fill="#FFF" data-v-fc43bd8e></path></svg></div><h3 data-v-fc43bd8e>合作共赢</h3><p data-v-fc43bd8e>携手共建，实现多方共赢</p></div><div class="philosophy-item" data-v-fc43bd8e><div class="philosophy-icon security" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64l320 128v256c0 177.664-123.392 326.592-288 366.336C379.392 774.592 256 625.664 256 448V192L512 64z" fill="#2196F3" data-v-fc43bd8e></path><path d="M512 128l256 102.4v217.6c0 142.336-98.816 261.248-230.4 293.056C405.216 708.848 320 589.936 320 448V230.4L512 128z" fill="#42A5F5" data-v-fc43bd8e></path><path d="M448 384l64 64 128-128" stroke="#FFF" stroke-width="32" fill="none" stroke-linecap="round" stroke-linejoin="round" data-v-fc43bd8e></path></svg></div><h3 data-v-fc43bd8e>安全至上</h3><p data-v-fc43bd8e>以安全为核心，保障用户利益</p></div><div class="philosophy-item" data-v-fc43bd8e><div class="philosophy-icon trust" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#9C27B0" data-v-fc43bd8e></path><path d="M512 192l96 192h192l-160 128 64 192-192-128-192 128 64-192-160-128h192z" fill="#FFD700" data-v-fc43bd8e></path><path d="M512 256l64 128h128l-104 80 40 128-128-88-128 88 40-128-104-80h128z" fill="#FFC107" data-v-fc43bd8e></path></svg></div><h3 data-v-fc43bd8e>信任基石</h3><p data-v-fc43bd8e>建立可信体系，赢得用户信赖</p></div></div></div></section><section class="products" data-v-fc43bd8e><div class="container" data-v-fc43bd8e><h2 class="section-title" data-aos="fade-up" data-v-fc43bd8e><span class="title-bg" data-v-fc43bd8e>平台服务</span></h2><div class="product-list" data-v-fc43bd8e><div class="product-item" data-aos="flip-left" data-aos-delay="100" data-v-fc43bd8e><div class="product-card" data-v-fc43bd8e><div class="product-icon" data-v-fc43bd8e><div class="icon-bg vulnerability" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="50" height="50" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF5722" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FF7043" data-v-fc43bd8e></path><path d="M400 320h224v64H400z" fill="#FFF" data-v-fc43bd8e></path><path d="M400 416h224v64H400z" fill="#FFF" data-v-fc43bd8e></path><path d="M400 512h160v64H400z" fill="#FFF" data-v-fc43bd8e></path><path d="M640 320l64 64-64 64-64-64z" fill="#FFD700" data-v-fc43bd8e></path><path d="M320 576h384v64H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M448 672h128v32H448z" fill="#FFF" data-v-fc43bd8e></path></svg></div><div class="icon-glow" data-v-fc43bd8e></div></div><h3 class="product-title" data-v-fc43bd8e>漏洞悬赏</h3><p class="product-desc" data-v-fc43bd8e>基于区块链技术的漏洞发现与奖励系统，激励优质安全研究</p><div class="product-features" data-v-fc43bd8e><span class="feature-tag" data-v-fc43bd8e>区块链</span><span class="feature-tag" data-v-fc43bd8e>智能合约</span><span class="feature-tag" data-v-fc43bd8e>自动奖励</span></div><div class="product-hover-effect" data-v-fc43bd8e></div></div></div><div class="product-item" data-aos="flip-left" data-aos-delay="200" data-v-fc43bd8e><div class="product-card" data-v-fc43bd8e><div class="product-icon" data-v-fc43bd8e><div class="icon-bg assessment" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="50" height="50" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#2196F3" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#42A5F5" data-v-fc43bd8e></path><circle cx="400" cy="400" r="80" fill="none" stroke="#FFF" stroke-width="16" data-v-fc43bd8e></circle><path d="M360 400l20 20 40-40" stroke="#FFF" stroke-width="12" fill="none" stroke-linecap="round" data-v-fc43bd8e></path><path d="M320 560h384v32H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M320 624h256v24H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M320 672h320v24H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M680 360l40 40-40 40" stroke="#FFD700" stroke-width="12" fill="none" stroke-linecap="round" data-v-fc43bd8e></path></svg></div><div class="icon-glow" data-v-fc43bd8e></div></div><h3 class="product-title" data-v-fc43bd8e>安全评估</h3><p class="product-desc" data-v-fc43bd8e>全方位系统安全体检与风险评估，精准定位潜在安全隐患</p><div class="product-features" data-v-fc43bd8e><span class="feature-tag" data-v-fc43bd8e>全面扫描</span><span class="feature-tag" data-v-fc43bd8e>风险评估</span><span class="feature-tag" data-v-fc43bd8e>专业报告</span></div><div class="product-hover-effect" data-v-fc43bd8e></div></div></div><div class="product-item" data-aos="flip-left" data-aos-delay="300" data-v-fc43bd8e><div class="product-card" data-v-fc43bd8e><div class="product-icon" data-v-fc43bd8e><div class="icon-bg monitoring" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="50" height="50" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#4CAF50" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#66BB6A" data-v-fc43bd8e></path><circle cx="512" cy="400" r="120" fill="none" stroke="#FFF" stroke-width="16" data-v-fc43bd8e></circle><circle cx="512" cy="400" r="80" fill="none" stroke="#FFF" stroke-width="12" data-v-fc43bd8e></circle><circle cx="512" cy="400" r="40" fill="#FFF" data-v-fc43bd8e></circle><path d="M320 600h384v32H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M360 650h304v20H360z" fill="#FFF" data-v-fc43bd8e></path><path d="M400 680h224v20H400z" fill="#FFF" data-v-fc43bd8e></path><circle cx="720" cy="320" r="24" fill="#FF5722" data-v-fc43bd8e></circle><circle cx="720" cy="320" r="16" fill="#FF8A65" opacity="0.8" data-v-fc43bd8e></circle></svg></div><div class="icon-glow" data-v-fc43bd8e></div></div><h3 class="product-title" data-v-fc43bd8e>威胁监测</h3><p class="product-desc" data-v-fc43bd8e>实时监控与智能分析，第一时间发现并应对网络安全威胁</p><div class="product-features" data-v-fc43bd8e><span class="feature-tag" data-v-fc43bd8e>实时监控</span><span class="feature-tag" data-v-fc43bd8e>AI分析</span><span class="feature-tag" data-v-fc43bd8e>快速响应</span></div><div class="product-hover-effect" data-v-fc43bd8e></div></div></div><div class="product-item" data-aos="flip-left" data-aos-delay="400" data-v-fc43bd8e><div class="product-card" data-v-fc43bd8e><div class="product-icon" data-v-fc43bd8e><div class="icon-bg emergency" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="50" height="50" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#FF9800" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FFB74D" data-v-fc43bd8e></path><path d="M400 280l112 160-112 160-112-160z" fill="#FFF" data-v-fc43bd8e></path><path d="M512 320l80 120-80 120-80-120z" fill="#FF5722" data-v-fc43bd8e></path><circle cx="512" cy="440" r="32" fill="#FFF" data-v-fc43bd8e></circle><path d="M320 640h384v32H320z" fill="#FFF" data-v-fc43bd8e></path><path d="M360 680h304v20H360z" fill="#FFF" data-v-fc43bd8e></path><path d="M400 710h224v20H400z" fill="#FFF" data-v-fc43bd8e></path><circle cx="680" cy="320" r="20" fill="#FF5722" data-v-fc43bd8e></circle><path d="M680 300v40M660 320h40" stroke="#FFF" stroke-width="4" data-v-fc43bd8e></path></svg></div><div class="icon-glow" data-v-fc43bd8e></div></div><h3 class="product-title" data-v-fc43bd8e>应急响应</h3><p class="product-desc" data-v-fc43bd8e>专业的安全事件快速响应与修复方案，最小化安全事件损失</p><div class="product-features" data-v-fc43bd8e><span class="feature-tag" data-v-fc43bd8e>7x24服务</span><span class="feature-tag" data-v-fc43bd8e>专家团队</span><span class="feature-tag" data-v-fc43bd8e>快速修复</span></div><div class="product-hover-effect" data-v-fc43bd8e></div></div></div></div></div></section><section class="why-choose-us" data-v-fc43bd8e><div class="container" data-v-fc43bd8e><h2 class="section-title" data-aos="fade-up" data-v-fc43bd8e><span class="title-bg" data-v-fc43bd8e>为什么选择衍盾星云</span></h2><div class="features-grid" data-v-fc43bd8e><div class="feature-card" data-aos="fade-up" data-aos-delay="100" data-v-fc43bd8e><div class="feature-number" data-v-fc43bd8e>01</div><div class="feature-content" data-v-fc43bd8e><div class="feature-icon blockchain" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#6366F1" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#8B5CF6" data-v-fc43bd8e></path><rect x="320" y="320" width="120" height="120" rx="16" fill="#FFF" data-v-fc43bd8e></rect><rect x="584" y="320" width="120" height="120" rx="16" fill="#FFF" data-v-fc43bd8e></rect><rect x="320" y="584" width="120" height="120" rx="16" fill="#FFF" data-v-fc43bd8e></rect><rect x="584" y="584" width="120" height="120" rx="16" fill="#FFF" data-v-fc43bd8e></rect><path d="M440 380h144v8H440z" fill="#6366F1" data-v-fc43bd8e></path><path d="M440 644h144v8H440z" fill="#6366F1" data-v-fc43bd8e></path><path d="M380 440v144h8V440z" fill="#6366F1" data-v-fc43bd8e></path><path d="M644 440v144h8V440z" fill="#6366F1" data-v-fc43bd8e></path><circle cx="380" cy="380" r="8" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="644" cy="380" r="8" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="380" cy="644" r="8" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="644" cy="644" r="8" fill="#FFD700" data-v-fc43bd8e></circle></svg></div><h3 class="feature-title" data-v-fc43bd8e>区块链技术保障</h3><p class="feature-desc" data-v-fc43bd8e>利用区块链不可篡改特性，确保漏洞提交记录真实可信，智能合约自动执行奖励结算</p><div class="feature-stats" data-v-fc43bd8e><span class="stat" data-v-fc43bd8e>100% 可信</span><span class="stat" data-v-fc43bd8e>自动结算</span></div></div><div class="feature-bg-effect" data-v-fc43bd8e></div></div><div class="feature-card" data-aos="fade-up" data-aos-delay="200" data-v-fc43bd8e><div class="feature-number" data-v-fc43bd8e>02</div><div class="feature-content" data-v-fc43bd8e><div class="feature-icon detection" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#10B981" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#34D399" data-v-fc43bd8e></path><path d="M320 600h80v80h-80z" fill="#FFF" data-v-fc43bd8e></path><path d="M440 520h80v160h-80z" fill="#FFF" data-v-fc43bd8e></path><path d="M560 440h80v240h-80z" fill="#FFF" data-v-fc43bd8e></path><path d="M680 360h80v320h-80z" fill="#FFF" data-v-fc43bd8e></path><path d="M320 320l120 80 120-80 120 80" stroke="#FFD700" stroke-width="6" fill="none" stroke-linecap="round" data-v-fc43bd8e></path><circle cx="320" cy="320" r="12" fill="#FF5722" data-v-fc43bd8e></circle><circle cx="440" cy="400" r="12" fill="#FF5722" data-v-fc43bd8e></circle><circle cx="560" cy="320" r="12" fill="#FF5722" data-v-fc43bd8e></circle><circle cx="680" cy="400" r="12" fill="#FF5722" data-v-fc43bd8e></circle><text x="360" y="300" fill="#FFF" font-size="24" font-weight="bold" data-v-fc43bd8e>+25%</text></svg></div><h3 class="feature-title" data-v-fc43bd8e>可以多检测出业务逻辑漏洞</h3><p class="feature-desc" data-v-fc43bd8e>白帽深入剖析业务逻辑，全面细致的发现可利用弱点，帮助企业强化业务健壮性</p><div class="feature-stats" data-v-fc43bd8e><span class="stat" data-v-fc43bd8e>提升发现率</span><span class="stat" data-v-fc43bd8e>深度分析</span></div></div><div class="feature-bg-effect" data-v-fc43bd8e></div></div><div class="feature-card" data-aos="fade-up" data-aos-delay="300" data-v-fc43bd8e><div class="feature-number" data-v-fc43bd8e>03</div><div class="feature-content" data-v-fc43bd8e><div class="feature-icon timer" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#F59E0B" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#FBBF24" data-v-fc43bd8e></path><circle cx="512" cy="512" r="200" fill="none" stroke="#FFF" stroke-width="16" data-v-fc43bd8e></circle><circle cx="512" cy="512" r="160" fill="none" stroke="#FFF" stroke-width="8" data-v-fc43bd8e></circle><path d="M512 352v160l80 80" stroke="#FFF" stroke-width="12" fill="none" stroke-linecap="round" data-v-fc43bd8e></path><circle cx="512" cy="512" r="16" fill="#FFF" data-v-fc43bd8e></circle><path d="M512 200v40M824 512h-40M512 824v-40M200 512h40" stroke="#FFF" stroke-width="8" stroke-linecap="round" data-v-fc43bd8e></path><text x="440" y="420" fill="#FF5722" font-size="32" font-weight="bold" data-v-fc43bd8e>90%</text><path d="M680 344l-40 40" stroke="#FF5722" stroke-width="8" stroke-linecap="round" data-v-fc43bd8e></path></svg></div><h3 class="feature-title" data-v-fc43bd8e>缩短至少漏洞暴露窗口期</h3><p class="feature-desc" data-v-fc43bd8e>依托平台工作流系统，在漏洞发现后的第一时间及时推送给企业，协助企业快速处置</p><div class="feature-stats" data-v-fc43bd8e><span class="stat" data-v-fc43bd8e>减少暴露期</span><span class="stat" data-v-fc43bd8e>实时推送</span></div></div><div class="feature-bg-effect" data-v-fc43bd8e></div></div><div class="feature-card" data-aos="fade-up" data-aos-delay="400" data-v-fc43bd8e><div class="feature-number" data-v-fc43bd8e>04</div><div class="feature-content" data-v-fc43bd8e><div class="feature-icon expert" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="40" height="40" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#EC4899" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#F472B6" data-v-fc43bd8e></path><circle cx="512" cy="400" r="80" fill="#FFF" data-v-fc43bd8e></circle><path d="M512 320c-44 0-80 36-80 80s36 80 80 80 80-36 80-80-36-80-80-80z" fill="#EC4899" data-v-fc43bd8e></path><path d="M320 680c0-106 86-192 192-192s192 86 192 192v40H320v-40z" fill="#FFF" data-v-fc43bd8e></path><path d="M360 680c0-84 68-152 152-152s152 68 152 152v40H360v-40z" fill="#EC4899" data-v-fc43bd8e></path><circle cx="400" cy="320" r="24" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="624" cy="320" r="24" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="400" cy="600" r="24" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="624" cy="600" r="24" fill="#FFD700" data-v-fc43bd8e></circle><path d="M400 320l24 24M624 320l-24 24M400 600l24-24M624 600l-24-24" stroke="#FFD700" stroke-width="4" data-v-fc43bd8e></path></svg></div><h3 class="feature-title" data-v-fc43bd8e>白帽专家众多</h3><p class="feature-desc" data-v-fc43bd8e>根据企业测试需求和重点匹配适合的白帽子人选，提供高价值漏洞，让漏洞无处可逃</p><div class="feature-stats" data-v-fc43bd8e><span class="stat" data-v-fc43bd8e>精准匹配</span><span class="stat" data-v-fc43bd8e>专家团队</span></div></div><div class="feature-bg-effect" data-v-fc43bd8e></div></div></div></div></section><section class="scenarios" data-v-fc43bd8e><div class="container" data-v-fc43bd8e><h2 class="section-title" data-aos="fade-up" data-v-fc43bd8e><span class="title-bg" data-v-fc43bd8e>应用场景</span></h2><div class="scenarios-grid" data-aos="fade-up" data-aos-delay="200" data-v-fc43bd8e><div class="scenario-card enterprise" data-aos="fade-up" data-aos-delay="300" data-v-fc43bd8e><div class="scenario-header" data-v-fc43bd8e><div class="scenario-icon" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="48" height="48" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#3B82F6" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#60A5FA" data-v-fc43bd8e></path><rect x="320" y="280" width="384" height="280" rx="16" fill="#FFF" data-v-fc43bd8e></rect><rect x="340" y="300" width="344" height="40" fill="#3B82F6" data-v-fc43bd8e></rect><rect x="360" y="360" width="80" height="80" rx="8" fill="#E5E7EB" data-v-fc43bd8e></rect><rect x="460" y="360" width="80" height="80" rx="8" fill="#E5E7EB" data-v-fc43bd8e></rect><rect x="560" y="360" width="80" height="80" rx="8" fill="#E5E7EB" data-v-fc43bd8e></rect><rect x="360" y="460" width="280" height="20" rx="4" fill="#D1D5DB" data-v-fc43bd8e></rect><rect x="360" y="500" width="200" height="20" rx="4" fill="#D1D5DB" data-v-fc43bd8e></rect></svg></div><div class="scenario-badge" data-v-fc43bd8e>企业首选</div></div><h3 class="scenario-title" data-v-fc43bd8e>企业安全建设</h3><p class="scenario-subtitle" data-v-fc43bd8e>业务驱动场景</p><div class="scenario-features" data-v-fc43bd8e><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>企业注重服务品质和效果提升</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>业务变更频繁，需要持续安全保障</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>实战化验证企业安全能力建设</span></div></div></div><div class="scenario-card risk" data-aos="fade-up" data-aos-delay="400" data-v-fc43bd8e><div class="scenario-header" data-v-fc43bd8e><div class="scenario-icon" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="48" height="48" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#EF4444" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#F87171" data-v-fc43bd8e></path><path d="M512 200L600 400H424L512 200z" fill="#FFF" data-v-fc43bd8e></path><circle cx="512" cy="480" r="24" fill="#FFF" data-v-fc43bd8e></circle><rect x="500" y="520" width="24" height="120" rx="12" fill="#FFF" data-v-fc43bd8e></rect><path d="M400 680h224l-16 40H416l-16-40z" fill="#FFF" data-v-fc43bd8e></path></svg></div><div class="scenario-badge" data-v-fc43bd8e>风险控制</div></div><h3 class="scenario-title" data-v-fc43bd8e>风险预防控制</h3><p class="scenario-subtitle" data-v-fc43bd8e>预知风险场景</p><div class="scenario-features" data-v-fc43bd8e><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>系统上线前外部安全测试验证</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>重保前专项体检和漏洞修复</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>合规驱动的未知安全隐患发现</span></div></div></div><div class="scenario-card intelligent" data-aos="fade-up" data-aos-delay="500" data-v-fc43bd8e><div class="scenario-header" data-v-fc43bd8e><div class="scenario-icon" data-v-fc43bd8e><svg viewBox="0 0 1024 1024" width="48" height="48" data-v-fc43bd8e><path d="M512 64c247.424 0 448 200.576 448 448s-200.576 448-448 448S64 759.424 64 512 264.576 64 512 64z" fill="#8B5CF6" data-v-fc43bd8e></path><path d="M512 128c212.064 0 384 171.936 384 384s-171.936 384-384 384S128 724.064 128 512 299.936 128 512 128z" fill="#A78BFA" data-v-fc43bd8e></path><circle cx="512" cy="400" r="80" fill="#FFF" data-v-fc43bd8e></circle><circle cx="512" cy="400" r="40" fill="#8B5CF6" data-v-fc43bd8e></circle><path d="M400 500h224v120H400z" fill="#FFF" data-v-fc43bd8e></path><rect x="420" y="520" width="40" height="8" fill="#8B5CF6" data-v-fc43bd8e></rect><rect x="480" y="520" width="40" height="8" fill="#8B5CF6" data-v-fc43bd8e></rect><rect x="540" y="520" width="40" height="8" fill="#8B5CF6" data-v-fc43bd8e></rect><rect x="420" y="540" width="60" height="8" fill="#A78BFA" data-v-fc43bd8e></rect><rect x="500" y="540" width="80" height="8" fill="#A78BFA" data-v-fc43bd8e></rect><rect x="420" y="560" width="40" height="8" fill="#C4B5FD" data-v-fc43bd8e></rect><rect x="480" y="560" width="100" height="8" fill="#C4B5FD" data-v-fc43bd8e></rect><circle cx="400" cy="350" r="16" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="624" cy="350" r="16" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="400" cy="450" r="16" fill="#FFD700" data-v-fc43bd8e></circle><circle cx="624" cy="450" r="16" fill="#FFD700" data-v-fc43bd8e></circle><path d="M400 350l24 24M624 350l-24 24M400 450l24-24M624 450l-24-24" stroke="#FFD700" stroke-width="3" data-v-fc43bd8e></path></svg></div><div class="scenario-badge" data-v-fc43bd8e>AI驱动</div></div><h3 class="scenario-title" data-v-fc43bd8e>智能化运营</h3><p class="scenario-subtitle" data-v-fc43bd8e>技术创新场景</p><div class="scenario-features" data-v-fc43bd8e><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>AI驱动的智能威胁检测</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>区块链促使安全中心建设</span></div><div class="feature-item" data-v-fc43bd8e><span data-v-fc43bd8e>区块链技术保障数据可信</span></div></div></div></div></div></section>',4)),(0,u.bF)(k)])}t(33771),t(51629),t(44114),t(34782),t(18111),t(7588),t(58940),t(23500);var N=t(12040),P=t(20907),U=t(80401),K=t(74010),j=t(55409),q=t.n(j);const Q={name:"Home",components:{TheHeader:U.A,AppFooter:K.A,QuestionFilled:d.QuestionFilled,Connection:d.Connection,Lock:d.Lock,WarningFilled:d.WarningFilled,Cpu:d.Cpu,ArrowLeft:d.ArrowLeft,ArrowRight:d.ArrowRight,ArrowDown:d.ArrowDown,TrendCharts:d.TrendCharts},setup:function(){var e=(0,N.KR)(null),a=(0,g.rd)(),t=(0,N.KR)(!1),n=(0,N.KR)(null),r=(0,N.KR)(null),i=(0,N.KR)(0),s=(0,N.KR)(["/assets/images/bg1.png","/assets/images/bg2.png","/assets/images/bg3.png","/assets/images/bg4.png","/assets/images/bg5.png"]),l=null,c=function(){var a=(0,k.A)((0,b.A)().m(function a(){var t,n;return(0,b.A)().w(function(a){while(1)switch(a.n){case 0:if(!P.A.isLoggedIn()){a.n=5;break}return e.value=P.A.getCurrentUser(),console.log("当前用户类型:",e.value.userType),a.p=1,a.n=2,P.A.validateLoginStatus();case 2:t=a.v,t||(e.value=null),a.n=4;break;case 3:a.p=3,n=a.v,console.error("验证登录状态失败:",n);case 4:a.n=6;break;case 5:e.value=null;case 6:return a.a(2)}},a,null,[[1,3]])}));return function(){return a.apply(this,arguments)}}(),o=function(){t.value=!t.value},d=function(e){n.value&&!n.value.contains(e.target)&&(t.value=!1)},v=function(){a.push("/login")},f=function(){a.push("/help")},p=function(){var e=document.querySelector(".philosophy");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},h=function(){i.value=(i.value+1)%s.value.length},m=function(){i.value=0===i.value?s.value.length-1:i.value-1},w=function(e){i.value=e},y=function(){l=setInterval(function(){h()},4e3)},L=function(){l&&(clearInterval(l),l=null)},F=function(){if(r.value){var e=document.createElement("canvas"),a=e.getContext("2d");r.value.appendChild(e),e.width=window.innerWidth,e.height=window.innerHeight;for(var t=[],n=50,i=0;i<n;i++)t.push({x:Math.random()*e.width,y:Math.random()*e.height,vx:.5*(Math.random()-.5),vy:.5*(Math.random()-.5),size:2*Math.random()+1,opacity:.5*Math.random()+.2});var s=function(){a.clearRect(0,0,e.width,e.height),t.forEach(function(t){t.x+=t.vx,t.y+=t.vy,(t.x<0||t.x>e.width)&&(t.vx*=-1),(t.y<0||t.y>e.height)&&(t.vy*=-1),a.beginPath(),a.arc(t.x,t.y,t.size,0,2*Math.PI),a.fillStyle="rgba(255, 255, 255, ".concat(t.opacity,")"),a.fill()}),t.forEach(function(e,n){t.slice(n+1).forEach(function(t){var n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);i<100&&(a.beginPath(),a.moveTo(e.x,e.y),a.lineTo(t.x,t.y),a.strokeStyle="rgba(255, 255, 255, ".concat(.1*(1-i/100),")"),a.stroke())})}),requestAnimationFrame(s)};s()}},_=function(){var e=document.querySelectorAll(".stat-num");e.forEach(function(e){var a=parseInt(e.getAttribute("data-count")),t=0,n=a/100,r=setInterval(function(){t+=n,t>=a&&(t=a,clearInterval(r)),e.textContent=Math.floor(t)+(99===a?"%":"+")},20)})},C=function(){var e=document.querySelector(".typing-text");if(e){var a=e.textContent;e.textContent="";var t=0,n=setInterval(function(){t<a.length?(e.textContent+=a.charAt(t),t++):clearInterval(n)},100)}};return(0,u.sV)((0,k.A)((0,b.A)().m(function e(){return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return c(),document.addEventListener("click",d),q().init({duration:1e3,once:!0,offset:100}),e.n=1,(0,u.dY)();case 1:F(),y(),setTimeout(function(){_(),C()},1e3);case 2:return e.a(2)}},e)}))),(0,u.xo)(function(){document.removeEventListener("click",d),L()}),{currentUser:e,showDropdown:t,toggleDropdown:o,userDropdown:n,particlesBg:r,goToLogin:v,goToHelp:f,scrollToNext:p,currentSlide:i,backgroundImages:s,nextSlide:h,prevSlide:m,goToSlide:w}}},O=(0,p.A)(Q,[["render",D],["__scopeId","data-v-fc43bd8e"]]),Y=O;var $={class:"login-container"},J={class:"login-box"},G={class:"login-form"},Z={class:"register-tip"};function ee(e,a,t,n,r,i){var s=(0,u.g2)("el-input"),l=(0,u.g2)("el-form-item"),c=(0,u.g2)("el-button"),o=(0,u.g2)("el-form"),d=(0,u.g2)("router-link");return(0,u.uX)(),(0,u.CE)("div",$,[(0,u.Lk)("div",J,[a[6]||(a[6]=(0,u.Lk)("div",{class:"login-header"},[(0,u.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云",class:"login-logo"}),(0,u.Lk)("h2",{class:"login-title"},"衍盾星云"),(0,u.Lk)("p",{class:"login-subtitle"},"基于区块链的漏洞悬赏智能响应平台")],-1)),(0,u.Lk)("div",G,[(0,u.bF)(o,{ref:"loginFormRef",model:n.loginForm,rules:n.rules,"label-position":"top"},{default:(0,u.k6)(function(){return[(0,u.bF)(l,{label:"用户名/邮箱",prop:"username"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.loginForm.username,"onUpdate:modelValue":a[0]||(a[0]=function(e){return n.loginForm.username=e}),placeholder:"请输入用户名或邮箱",size:"large"},{prefix:(0,u.k6)(function(){return a[2]||(a[2]=[(0,u.Lk)("i",{class:"el-icon-user"},null,-1)])}),_:1},8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"密码",prop:"password"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.loginForm.password,"onUpdate:modelValue":a[1]||(a[1]=function(e){return n.loginForm.password=e}),type:"password",placeholder:"请输入密码",size:"large","show-password":""},{prefix:(0,u.k6)(function(){return a[3]||(a[3]=[(0,u.Lk)("i",{class:"el-icon-lock"},null,-1)])}),_:1},8,["modelValue"])]}),_:1}),(0,u.bF)(l,null,{default:(0,u.k6)(function(){return[(0,u.bF)(c,{type:"primary",loading:n.loading,onClick:n.handleLogin,class:"login-button",size:"large"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.loading?"登录中...":"登录"),1)]}),_:1},8,["loading","onClick"])]}),_:1})]}),_:1},8,["model","rules"]),(0,u.Lk)("div",Z,[a[5]||(a[5]=(0,u.eW)(" 还没有账号？ ")),(0,u.bF)(d,{to:"/register",class:"register-link"},{default:(0,u.k6)(function(){return a[4]||(a[4]=[(0,u.eW)("立即注册")])}),_:1,__:[4]})])])]),a[7]||(a[7]=(0,u.Fv)('<div class="login-footer" data-v-2aa80265><p class="footer-text" data-v-2aa80265> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-2aa80265>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-2aa80265>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-2aa80265>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-2aa80265><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-2aa80265> 渝公网安备50011702501094号 </a></p></div>',1))])}var ae=t(18057),te=t(36149);const ne={name:"Login",setup:function(){var e=(0,g.rd)(),a=(0,g.lq)(),t=(0,N.KR)(null),n=(0,N.KR)(!1),r=(0,N.KR)(!1),i=(0,N.Kh)({username:"",password:""}),s={username:[{required:!0,message:"请输入用户名或邮箱",trigger:"blur"},{min:3,message:"用户名长度不能小于3个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"}]},l=function(){var s=(0,k.A)((0,b.A)().m(function s(){var l,c,o,d;return(0,b.A)().w(function(s){while(1)switch(s.n){case 0:if(t.value){s.n=1;break}return s.a(2);case 1:return s.p=1,s.n=2,t.value.validate();case 2:return n.value=!0,s.p=3,console.log("准备发送登录请求:",{username:i.username,password:"******",remember:r.value}),s.n=4,te.A.post("/auth/login",{username:i.username,password:i.password,remember:r.value});case 4:l=s.v,console.log("登录响应:",l.data),l.data.success?(P.A.saveLoginInfo(l.data.token,l.data.user,r.value),(0,ae.nk)({type:"success",message:"登录成功！"}),c=a.query.redirect||"/",e.push(c)):ae.nk.error(l.data.message||"登录失败"),s.n=6;break;case 5:s.p=5,d=s.v,console.error("登录失败:",d),ae.nk.error((null===(o=d.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.message)||"登录失败，请稍后重试");case 6:s.n=8;break;case 7:s.p=7,s.v,console.log("表单验证失败");case 8:return s.p=8,n.value=!1,s.f(8);case 9:return s.a(2)}},s,null,[[3,5],[1,7,8,9]])}));return function(){return s.apply(this,arguments)}}();return{loginFormRef:t,loginForm:i,loading:n,rememberMe:r,rules:s,handleLogin:l}}},re=(0,p.A)(ne,[["render",ee],["__scopeId","data-v-2aa80265"]]),ie=re;var se={class:"register-container"},le={class:"register-box"},ce={class:"register-content"},oe={class:"login-link-box"};function de(e,a,t,n,r,i){var s=(0,u.g2)("el-input"),l=(0,u.g2)("el-form-item"),c=(0,u.g2)("el-radio"),o=(0,u.g2)("el-radio-group"),d=(0,u.g2)("router-link"),v=(0,u.g2)("el-checkbox"),f=(0,u.g2)("el-button"),p=(0,u.g2)("el-form");return(0,u.uX)(),(0,u.CE)("div",se,[(0,u.Lk)("div",le,[a[18]||(a[18]=(0,u.Lk)("div",{class:"register-header"},[(0,u.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云",class:"register-logo"}),(0,u.Lk)("h2",{class:"register-title"},"衍盾星云"),(0,u.Lk)("p",{class:"register-subtitle"},"基于区块链的漏洞悬赏智能响应平台")],-1)),(0,u.Lk)("div",ce,[a[17]||(a[17]=(0,u.Lk)("h3",{class:"form-title"},"用户注册",-1)),(0,u.bF)(p,{ref:"registerFormRef",model:n.registerForm,rules:n.registerRules,"label-position":"top"},{default:(0,u.k6)(function(){return[(0,u.bF)(l,{label:"用户名",prop:"username"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.username,"onUpdate:modelValue":a[0]||(a[0]=function(e){return n.registerForm.username=e}),placeholder:"请输入用户名（3-20个字符）"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"邮箱",prop:"email"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.email,"onUpdate:modelValue":a[1]||(a[1]=function(e){return n.registerForm.email=e}),placeholder:"请输入邮箱"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"手机号码",prop:"phone"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.phone,"onUpdate:modelValue":a[2]||(a[2]=function(e){return n.registerForm.phone=e}),placeholder:"请输入手机号码"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"密码",prop:"password"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.password,"onUpdate:modelValue":a[3]||(a[3]=function(e){return n.registerForm.password=e}),type:"password",placeholder:"请输入密码（至少6位）","show-password":""},null,8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"确认密码",prop:"confirmPassword"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.confirmPassword,"onUpdate:modelValue":a[4]||(a[4]=function(e){return n.registerForm.confirmPassword=e}),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]}),_:1}),(0,u.bF)(l,{label:"用户类型",prop:"userType"},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{modelValue:n.registerForm.userType,"onUpdate:modelValue":a[5]||(a[5]=function(e){return n.registerForm.userType=e})},{default:(0,u.k6)(function(){return[(0,u.bF)(c,{label:"whiteHat"},{default:(0,u.k6)(function(){return a[8]||(a[8]=[(0,u.eW)("白帽子（漏洞提交者）")])}),_:1,__:[8]}),(0,u.bF)(c,{label:"enterprise"},{default:(0,u.k6)(function(){return a[9]||(a[9]=[(0,u.eW)("企业用户（需求方）")])}),_:1,__:[9]})]}),_:1},8,["modelValue"])]}),_:1}),"enterprise"===n.registerForm.userType?((0,u.uX)(),(0,u.Wv)(l,{key:0,label:"企业名称",prop:"companyName"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{modelValue:n.registerForm.companyName,"onUpdate:modelValue":a[6]||(a[6]=function(e){return n.registerForm.companyName=e}),placeholder:"请输入企业名称"},null,8,["modelValue"])]}),_:1})):(0,u.Q3)("",!0),(0,u.bF)(l,null,{default:(0,u.k6)(function(){return[(0,u.bF)(v,{modelValue:n.registerForm.agreement,"onUpdate:modelValue":a[7]||(a[7]=function(e){return n.registerForm.agreement=e}),required:""},{default:(0,u.k6)(function(){return[a[12]||(a[12]=(0,u.eW)(" 我已阅读并同意 ")),(0,u.bF)(d,{to:"/terms",class:"link"},{default:(0,u.k6)(function(){return a[10]||(a[10]=[(0,u.eW)("《服务条款》")])}),_:1,__:[10]}),a[13]||(a[13]=(0,u.eW)(" 和 ")),(0,u.bF)(d,{to:"/privacy",class:"link"},{default:(0,u.k6)(function(){return a[11]||(a[11]=[(0,u.eW)("《隐私政策》")])}),_:1,__:[11]})]}),_:1,__:[12,13]},8,["modelValue"])]}),_:1}),(0,u.bF)(l,null,{default:(0,u.k6)(function(){return[(0,u.bF)(f,{type:"primary",onClick:n.handleRegister,class:"register-button",loading:n.loading},{default:(0,u.k6)(function(){return a[14]||(a[14]=[(0,u.eW)(" 注册 ")])}),_:1,__:[14]},8,["onClick","loading"])]}),_:1})]}),_:1},8,["model","rules"]),(0,u.Lk)("div",oe,[(0,u.Lk)("p",null,[a[16]||(a[16]=(0,u.eW)("已有账号？")),(0,u.bF)(d,{to:"/login",class:"login-link"},{default:(0,u.k6)(function(){return a[15]||(a[15]=[(0,u.eW)("立即登录")])}),_:1,__:[15]})])])])]),a[19]||(a[19]=(0,u.Fv)('<div class="register-footer" data-v-4fb6b865><p class="footer-text" data-v-4fb6b865> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-4fb6b865>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-4fb6b865>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-4fb6b865>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-4fb6b865><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-4fb6b865> 渝公网安备50011702501094号 </a></p></div>',1))])}t(16280),t(76918),t(42762);const ue={name:"Register",setup:function(){var e=(0,g.rd)(),a=(0,N.Kh)({username:"",email:"",phone:"",password:"",confirmPassword:"",userType:"whiteHat",agreement:!1,companyName:""}),t=(0,N.KR)(!1),n=(0,N.KR)(null),r=function(e,t,r){""===t?r(new Error("请输入密码")):(""!==a.confirmPassword&&n.value.validateField("confirmPassword"),r())},i=function(e,t,n){""===t?n(new Error("请再次输入密码")):t!==a.password?n(new Error("两次输入密码不一致")):n()},s=function(e,a,t){a?t():t(new Error("请阅读并同意服务条款和隐私政策"))},l={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"},{validator:r,trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:i,trigger:"blur"}],userType:[{required:!0,message:"请选择用户类型",trigger:"change"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur",validator:function(e,t,n){"enterprise"===a.userType?t&&""!==t.trim()?t.length<2||t.length>100?n(new Error("企业名称长度在 2 到 100 个字符")):n():n(new Error("请输入企业名称")):n()}}],agreement:[{validator:s,trigger:"change"}]};(0,u.wB)(function(){return a.userType},function(e,t){n.value&&("enterprise"===t&&"whiteHat"===e&&(n.value.clearValidate("companyName"),a.companyName=""),"enterprise"===e&&n.value.clearValidate("companyName"))});var c=function(){var r=(0,k.A)((0,b.A)().m(function r(){var i,s,l;return(0,b.A)().w(function(r){while(1)switch(r.n){case 0:if(n.value){r.n=1;break}return r.a(2);case 1:if(r.p=1,a.agreement){r.n=2;break}return(0,ae.nk)({type:"warning",message:"请阅读并同意服务条款和隐私政策"}),r.a(2);case 2:if("enterprise"!==a.userType||a.companyName&&""!==a.companyName.trim()){r.n=3;break}return(0,ae.nk)({type:"warning",message:"请输入企业名称"}),r.a(2);case 3:return r.n=4,n.value.validate();case 4:return t.value=!0,r.p=5,r.n=6,te.A.post("/auth/register",{username:a.username,email:a.email,phone:a.phone,password:a.password,userType:a.userType,companyName:"enterprise"===a.userType?a.companyName:void 0});case 6:i=r.v,i.data.success?((0,ae.nk)({type:"success",message:"注册成功！请前往登录页面"}),i.data.blockchain&&(i.data.blockchain.success?((0,ae.nk)({type:"success",message:"区块链账户创建成功！"}),console.log("区块链地址:",i.data.user.blockchainAddress),console.log("区块链用户ID:",i.data.user.blockchainUserId)):((0,ae.nk)({type:"warning",message:"区块链账户创建失败: ".concat(i.data.blockchain.message)}),console.warn("区块链账户创建失败，但用户注册成功"))),setTimeout(function(){e.push({path:"/login"})},3e3)):(0,ae.nk)({type:"error",message:i.data.message||"注册失败，请稍后再试"}),r.n=8;break;case 7:r.p=7,s=r.v,console.error("注册请求错误:",s),s.response&&s.response.data?(0,ae.nk)({type:"error",message:s.response.data.message||"注册失败，服务器错误"}):(0,ae.nk)({type:"error",message:"注册失败，网络连接异常"});case 8:return r.p=8,t.value=!1,r.f(8);case 9:r.n=11;break;case 10:r.p=10,l=r.v,console.log("表单验证失败",l);case 11:return r.a(2)}},r,null,[[5,7,8,9],[1,10]])}));return function(){return r.apply(this,arguments)}}();return{registerForm:a,registerRules:l,loading:t,registerFormRef:n,handleRegister:c}}},ve=(0,p.A)(ue,[["render",de],["__scopeId","data-v-4fb6b865"]]),fe=ve;var pe={class:"not-found"},he={class:"not-found-content"};function me(e,a,t,n,r,i){var s=(0,u.g2)("el-button"),l=(0,u.g2)("router-link");return(0,u.uX)(),(0,u.CE)("div",pe,[(0,u.Lk)("div",he,[a[1]||(a[1]=(0,u.Lk)("h1",{class:"error-code"},"404",-1)),a[2]||(a[2]=(0,u.Lk)("h2",{class:"error-title"},"页面未找到",-1)),a[3]||(a[3]=(0,u.Lk)("p",{class:"error-desc"},"抱歉，您访问的页面不存在或已被移除",-1)),(0,u.bF)(l,{to:"/",class:"back-home"},{default:(0,u.k6)(function(){return[(0,u.bF)(s,{type:"primary"},{default:(0,u.k6)(function(){return a[0]||(a[0]=[(0,u.eW)("返回首页")])}),_:1,__:[0]})]}),_:1})])])}const be={name:"NotFound"},ke=(0,p.A)(be,[["render",me],["__scopeId","data-v-03a3847f"]]),ge=ke;t(52675),t(89463),t(27495),t(25440);var we={class:"user-profile-container"},ye={class:"container main-content"},Le={key:0,class:"profile-card"},Fe={class:"profile-header"},_e={class:"avatar-container"},Ce={class:"avatar-wrapper"},xe={key:0,class:"avatar-placeholder"},Ae={key:1,class:"avatar-image-container"},Se=["src"],Te={key:2,class:"upload-loading"},ze={class:"user-info"},Me={class:"username"},Ee={class:"user-type"},Ve={class:"user-email"},Re={class:"info-section"},We={class:"section-header"},He={key:1,class:"edit-actions"},Ie={class:"info-section blockchain-section"},Be={class:"section-header"},Xe={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},De={class:"blockchain-container"},Ne={key:0,class:"blockchain-cards"},Pe={key:0,class:"blockchain-card user-id-card"},Ue={class:"card-content"},Ke={class:"value-display"},je={class:"value-text"},qe={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Qe={key:1,class:"blockchain-card address-card"},Oe={class:"card-content"},Ye={class:"value-display"},$e={class:"value-text address-text"},Je={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Ge={key:2,class:"blockchain-card public-key-card"},Ze={class:"card-content"},ea={class:"value-display public-key-display"},aa={class:"public-key-text"},ta={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},na={key:1,class:"no-blockchain-info"},ra={class:"security-section"},ia={class:"account-section"},sa={class:"balance-info"},la={class:"balance-item"},ca={class:"balance-value"},oa={class:"balance-item"},da={class:"balance-value"},ua={class:"card-header"},va={key:0,class:"empty-data"},fa={class:"empty-tip"},pa={class:"submissions-section"},ha={class:"section-header"},ma={class:"stats-summary"},ba={class:"filter-bar"},ka={key:0},ga={key:1,class:"text-muted"},wa={class:"action-buttons"},ya={key:0,class:"empty-data"},La={class:"rewards-section"},Fa={class:"reward-summary"},_a={class:"summary-item"},Ca={class:"summary-content"},xa={class:"summary-value"},Aa={class:"summary-item"},Sa={class:"summary-content"},Ta={class:"summary-value"},za={class:"summary-item"},Ma={class:"summary-content"},Ea={class:"summary-value"},Va={class:"filter-bar"},Ra={class:"reward-amount"},Wa={key:0},Ha={key:1,class:"text-muted"},Ia={key:0,class:"empty-data"},Ba={class:"withdrawal-section"},Xa={class:"balance-info"},Da={class:"balance-item"},Na={class:"balance-content"},Pa={class:"balance-value"},Ua={class:"balance-item"},Ka={class:"balance-content"},ja={class:"balance-value"},qa={class:"balance-item"},Qa={class:"balance-content"},Oa={class:"balance-value"},Ya={class:"card-header"},$a={class:"form-tip"},Ja={class:"card-header"},Ga={class:"withdrawal-amount"},Za={key:0,class:"empty-data"},et={class:"certificates-section"},at={class:"card-header"},tt={class:"certificates-grid"},nt=["onClick"],rt={class:"certificate-header"},it={class:"certificate-number"},st={class:"certificate-title"},lt={class:"certificate-info"},ct={class:"certificate-date"},ot={class:"certificate-blockchain"},dt={key:0,class:"pagination-container"},ut={key:1,class:"empty-data"},vt={key:1,class:"not-logged-in"},ft={key:0,class:"certificate-paper",id:"certificate-content"},pt={class:"certificate-border"},ht={class:"certificate-body"},mt={class:"certificate-text"},bt={class:"recipient-name"},kt={class:"certificate-content-text"},gt={class:"severity-highlight"},wt={class:"certificate-details"},yt={class:"detail-row"},Lt={class:"detail-item"},Ft={class:"detail-value"},_t={class:"detail-item"},Ct={class:"detail-value blockchain-code"},xt={class:"detail-row"},At={class:"detail-item"},St={class:"detail-item"},Tt={class:"detail-value"},zt={class:"detail-row"},Mt={class:"detail-item"},Et={class:"detail-value"},Vt={class:"detail-item"},Rt={class:"detail-value"},Wt={class:"dialog-footer"},Ht={key:0,class:"vulnerability-detail"},It={class:"info-grid"},Bt={class:"info-item"},Xt={class:"info-value"},Dt={class:"info-item"},Nt={class:"info-value"},Pt={class:"info-item"},Ut={class:"info-value"},Kt={class:"info-item"},jt={class:"info-value"},qt={class:"info-item"},Qt={class:"info-value"},Ot={class:"info-item"},Yt={class:"info-value"},$t={class:"info-item"},Jt={class:"info-value reward-amount"},Gt={key:0,class:"reward-value"},Zt={key:1,class:"reward-pending"},en={key:0,class:"info-item"},an={class:"info-value"},tn={class:"card-header"},nn={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},rn={class:"blockchain-content"},sn={class:"blockchain-section submission-section"},ln={class:"section-title"},cn={class:"blockchain-fields"},on={class:"field-row"},dn={class:"field-item"},un={class:"field-value hash-value"},vn={class:"hash-text"},fn={class:"field-row"},pn={class:"field-item"},hn={class:"field-value hash-value"},mn={class:"hash-text"},bn={class:"field-row"},kn={class:"field-item"},gn={class:"field-value"},wn={class:"card-header"},yn={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Ln={class:"blockchain-content"},Fn={class:"blockchain-section approval-section"},_n={class:"section-title"},Cn={class:"blockchain-fields"},xn={class:"field-row"},An={class:"field-item"},Sn={class:"field-value hash-value"},Tn={class:"hash-text"},zn={class:"field-row"},Mn={class:"field-item"},En={class:"field-value hash-value"},Vn={class:"hash-text"},Rn={class:"field-row"},Wn={class:"field-item"},Hn={class:"field-value"},In={class:"card-header"},Bn={viewBox:"0 0 1024 1024",width:"12",height:"12",fill:"currentColor",style:{"margin-right":"4px"}},Xn={class:"blockchain-content"},Dn={key:0,class:"blockchain-section confirmation-section"},Nn={class:"section-title"},Pn={class:"blockchain-fields"},Un={class:"field-row"},Kn={class:"field-item"},jn={class:"field-value hash-value"},qn={class:"hash-text"},Qn={class:"field-row"},On={class:"field-item"},Yn={class:"field-value hash-value"},$n={class:"hash-text"},Jn={class:"field-row"},Gn={class:"field-item"},Zn={class:"field-value"},er={key:1,class:"blockchain-section dispute-section"},ar={class:"section-title"},tr={class:"blockchain-fields"},nr={class:"field-row"},rr={class:"field-item"},ir={class:"field-value hash-value"},sr={class:"hash-text"},lr={class:"field-row"},cr={class:"field-item"},or={class:"field-value hash-value"},dr={class:"hash-text"},ur={class:"field-row"},vr={class:"field-item"},fr={class:"field-value"},pr={class:"card-header"},hr={class:"blockchain-content"},mr={class:"blockchain-section admin-review-section"},br={class:"section-title"},kr={class:"blockchain-fields"},gr={class:"field-row"},wr={class:"field-item"},yr={class:"field-value hash-value"},Lr={class:"hash-text"},Fr={class:"field-row"},_r={class:"field-item"},Cr={class:"field-value hash-value"},xr={class:"hash-text"},Ar={class:"field-row"},Sr={class:"field-item"},Tr={class:"field-value"},zr={class:"content-text"},Mr={class:"content-text"},Er={class:"content-text"},Vr={class:"ai-analysis-content"},Rr={class:"ai-severity-display"},Wr={class:"ai-analysis-text"},Hr={class:"content-text"},Ir={class:"url-list"},Br={class:"url-content"},Xr={class:"url-text"},Dr={class:"url-actions"},Nr={class:"attachment-list"},Pr={class:"attachment-content"},Ur={class:"attachment-text"},Kr={class:"attachment-actions"},jr={class:"dialog-footer"},qr={key:0,class:"task-detail"},Qr={class:"task-description"},Or={class:"reward-section"},Yr={class:"reward-item low"},$r={class:"reward-amount"},Jr={class:"reward-item medium"},Gr={class:"reward-amount"},Zr={class:"reward-item high"},ei={class:"reward-amount"},ai={class:"reward-item critical"},ti={class:"reward-amount"},ni={class:"scope-section"},ri={class:"scope-content"},ii={class:"rules-section"},si={class:"rules-content"},li={class:"dialog-footer"};function ci(e,a,t,n,r,i){var s=(0,u.g2)("TheHeader"),l=(0,u.g2)("el-upload"),c=(0,u.g2)("el-tag"),o=(0,u.g2)("el-button"),d=(0,u.g2)("el-input"),v=(0,u.g2)("el-form-item"),f=(0,u.g2)("el-form"),p=(0,u.g2)("el-tab-pane"),h=(0,u.g2)("el-card"),m=(0,u.g2)("el-col"),b=(0,u.g2)("el-row"),k=(0,u.g2)("el-input-number"),g=(0,u.g2)("el-radio"),y=(0,u.g2)("el-radio-group"),L=(0,u.g2)("el-table-column"),F=(0,u.g2)("el-table"),_=(0,u.g2)("el-statistic"),C=(0,u.g2)("el-option"),x=(0,u.g2)("el-select"),A=(0,u.g2)("el-link"),S=(0,u.g2)("router-link"),T=(0,u.g2)("el-date-picker"),z=(0,u.g2)("el-pagination"),M=(0,u.g2)("el-tabs"),E=(0,u.g2)("el-dialog"),V=(0,u.g2)("el-descriptions-item"),R=(0,u.g2)("el-descriptions"),W=(0,u.g2)("AppFooter"),H=(0,u.gN)("loading");return(0,u.uX)(),(0,u.CE)("div",we,[(0,u.bF)(s),(0,u.Lk)("div",ye,[n.user?((0,u.uX)(),(0,u.CE)("div",Le,[(0,u.Lk)("div",Fe,[(0,u.Lk)("div",_e,[(0,u.bF)(l,{class:"avatar-uploader",action:n.uploadUrl,headers:n.uploadHeaders,"show-file-list":!1,"before-upload":n.beforeAvatarUpload,"on-success":n.handleAvatarSuccess,"on-error":n.handleAvatarError,disabled:n.uploadLoading},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Ce,[n.user.avatar||n.avatarUrl?((0,u.uX)(),(0,u.CE)("div",Ae,[(0,u.Lk)("img",{src:n.avatarUrl||n.user.avatar,alt:"用户头像",class:"avatar-img"},null,8,Se),a[42]||(a[42]=(0,u.Lk)("div",{class:"upload-overlay"},[(0,u.Lk)("i",{class:"el-icon-camera"}),(0,u.Lk)("span",null,"更换头像")],-1))])):((0,u.uX)(),(0,u.CE)("div",xe,[(0,u.eW)((0,w.v_)(n.user.username.charAt(0).toUpperCase())+" ",1),a[41]||(a[41]=(0,u.Lk)("div",{class:"upload-overlay"},[(0,u.Lk)("i",{class:"el-icon-camera"}),(0,u.Lk)("span",null,"上传头像")],-1))])),n.uploadLoading?((0,u.uX)(),(0,u.CE)("div",Te,a[43]||(a[43]=[(0,u.Lk)("i",{class:"el-icon-loading"},null,-1)]))):(0,u.Q3)("",!0)])]}),_:1},8,["action","headers","before-upload","on-success","on-error","disabled"])]),(0,u.Lk)("div",ze,[(0,u.Lk)("h2",Me,(0,w.v_)(n.user.username),1),(0,u.Lk)("p",Ee,[(0,u.bF)(c,{type:"whiteHat"===n.user.userType?"success":"warning"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)("whiteHat"===n.user.userType?"白帽子":"企业用户"),1)]}),_:1},8,["type"])]),(0,u.Lk)("p",Ve,(0,w.v_)(n.user.email),1)])]),(0,u.bF)(M,{modelValue:n.currentTab,"onUpdate:modelValue":a[25]||(a[25]=function(e){return n.currentTab=e}),class:"profile-tabs"},{default:(0,u.k6)(function(){return[(0,u.bF)(p,{label:"基本信息",name:"info"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Re,[(0,u.Lk)("div",We,[a[47]||(a[47]=(0,u.Lk)("h3",{class:"section-title"},"账户信息",-1)),n.editMode?((0,u.uX)(),(0,u.CE)("div",He,[(0,u.bF)(o,{size:"small",onClick:n.cancelEdit},{default:(0,u.k6)(function(){return a[45]||(a[45]=[(0,u.eW)("取消")])}),_:1,__:[45]},8,["onClick"]),(0,u.bF)(o,{type:"primary",size:"small",onClick:n.saveUserInfo,loading:n.saving},{default:(0,u.k6)(function(){return a[46]||(a[46]=[(0,u.eW)("保存")])}),_:1,__:[46]},8,["onClick","loading"])])):((0,u.uX)(),(0,u.Wv)(o,{key:0,type:"primary",size:"small",onClick:n.enableEditMode},{default:(0,u.k6)(function(){return a[44]||(a[44]=[(0,u.eW)(" 编辑资料 ")])}),_:1,__:[44]},8,["onClick"]))]),(0,u.bF)(f,{ref:"userFormRef",model:n.editForm,rules:n.userRules,"label-position":"left","label-width":"120px"},{default:(0,u.k6)(function(){return[(0,u.bF)(v,{label:"用户名",prop:"username"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.username,"onUpdate:modelValue":a[0]||(a[0]=function(e){return n.editForm.username=e}),disabled:!n.editMode},null,8,["modelValue","disabled"])]}),_:1}),(0,u.bF)(v,{label:"邮箱",prop:"email"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.email,"onUpdate:modelValue":a[1]||(a[1]=function(e){return n.editForm.email=e}),disabled:!n.editMode},null,8,["modelValue","disabled"])]}),_:1}),(0,u.bF)(v,{label:"手机号码",prop:"phone"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.phone,"onUpdate:modelValue":a[2]||(a[2]=function(e){return n.editForm.phone=e}),disabled:!n.editMode,placeholder:"请输入手机号码"},null,8,["modelValue","disabled"])]}),_:1}),n.editMode||n.editForm.realName?((0,u.uX)(),(0,u.Wv)(v,{key:0,label:"真实姓名",prop:"realName"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.realName,"onUpdate:modelValue":a[3]||(a[3]=function(e){return n.editForm.realName=e}),disabled:!n.editMode,placeholder:"请输入真实姓名"},null,8,["modelValue","disabled"])]}),_:1})):(0,u.Q3)("",!0),"enterprise"===n.user.userType?((0,u.uX)(),(0,u.Wv)(v,{key:1,label:"公司名称",prop:"companyName"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.companyName,"onUpdate:modelValue":a[4]||(a[4]=function(e){return n.editForm.companyName=e}),disabled:!n.editMode,placeholder:"请输入公司名称"},null,8,["modelValue","disabled"])]}),_:1})):(0,u.Q3)("",!0),n.editMode||n.editForm.bio?((0,u.uX)(),(0,u.Wv)(v,{key:2,label:"个人简介",prop:"bio"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.editForm.bio,"onUpdate:modelValue":a[5]||(a[5]=function(e){return n.editForm.bio=e}),disabled:!n.editMode,type:"textarea",rows:3,placeholder:"请输入个人简介"},null,8,["modelValue","disabled"])]}),_:1})):(0,u.Q3)("",!0),(0,u.bF)(v,{label:"用户类型"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{value:"whiteHat"===n.user.userType?"白帽子":"企业用户",disabled:""},null,8,["value"])]}),_:1}),(0,u.bF)(v,{label:"注册时间"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{value:n.formatDate(n.user.createdAt),disabled:""},null,8,["value"])]}),_:1}),(0,u.bF)(v,{label:"账户ID"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{value:n.user.id,disabled:""},null,8,["value"])]}),_:1})]}),_:1},8,["model","rules"])]),(0,u.Lk)("div",Ie,[(0,u.Lk)("div",Be,[a[50]||(a[50]=(0,u.Lk)("h3",{class:"section-title"},[(0,u.Lk)("div",{class:"title-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"18",height:"18",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])]),(0,u.eW)(" 区块链身份信息 ")],-1)),(0,u.bF)(c,{type:"success",size:"small",effect:"light"},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",Xe,a[48]||(a[48]=[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"},null,-1)]))),a[49]||(a[49]=(0,u.eW)(" 安全凭证 "))]}),_:1,__:[49]})]),(0,u.Lk)("div",De,[a[61]||(a[61]=(0,u.Lk)("div",{class:"security-notice"},[(0,u.Lk)("div",{class:"notice-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,u.Lk)("div",{class:"notice-content"},[(0,u.Lk)("h4",null,"安全提示"),(0,u.Lk)("p",null,"以下信息为您的区块链数字身份凭证，请妥善保管，切勿泄露给他人")])],-1)),n.user.blockchainAddress||n.user.blockchainUserId||n.user.blockchainPublicKey?((0,u.uX)(),(0,u.CE)("div",Ne,[n.user.blockchainUserId?((0,u.uX)(),(0,u.CE)("div",Pe,[a[53]||(a[53]=(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("div",{class:"card-icon user-id-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"})])]),(0,u.Lk)("div",{class:"card-title"},[(0,u.Lk)("h4",null,"区块链用户ID"),(0,u.Lk)("p",null,"WeBASE系统中的唯一标识")])],-1)),(0,u.Lk)("div",Ue,[(0,u.Lk)("div",Ke,[(0,u.Lk)("span",je,(0,w.v_)(n.user.blockchainUserId),1)]),(0,u.bF)(o,{type:"primary",size:"small",onClick:a[6]||(a[6]=function(e){return n.copyToClipboard(n.user.blockchainUserId,"区块链用户ID")}),class:"copy-button"},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",qe,a[51]||(a[51]=[(0,u.Lk)("path",{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0 3.9-.7 5.3-2l.3-.3L924 391.3c2.3-2.3 3.7-5.3 3.7-8.5V192c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM888 349L562 675H232V264h656v85z"},null,-1)]))),a[52]||(a[52]=(0,u.eW)(" 复制 "))]}),_:1,__:[52]})])])):(0,u.Q3)("",!0),n.user.blockchainAddress?((0,u.uX)(),(0,u.CE)("div",Qe,[a[56]||(a[56]=(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("div",{class:"card-icon address-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M515.664 512.735L512 516.4l-3.664-3.665C448.688 453.087 384 388.399 384 320c0-70.692 57.308-128 128-128s128 57.308 128 128c0 68.399-64.688 133.087-124.336 192.735zM512 224c-52.935 0-96 43.065-96 96 0 52.935 43.065 96 96 96s96-43.065 96-96c0-52.935-43.065-96-96-96z"}),(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"})])]),(0,u.Lk)("div",{class:"card-title"},[(0,u.Lk)("h4",null,"区块链地址"),(0,u.Lk)("p",null,"您在区块链网络中的唯一地址")])],-1)),(0,u.Lk)("div",Oe,[(0,u.Lk)("div",Ye,[(0,u.Lk)("span",$e,(0,w.v_)(n.user.blockchainAddress),1)]),(0,u.bF)(o,{type:"primary",size:"small",onClick:a[7]||(a[7]=function(e){return n.copyToClipboard(n.user.blockchainAddress,"区块链地址")}),class:"copy-button"},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",Je,a[54]||(a[54]=[(0,u.Lk)("path",{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0 3.9-.7 5.3-2l.3-.3L924 391.3c2.3-2.3 3.7-5.3 3.7-8.5V192c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM888 349L562 675H232V264h656v85z"},null,-1)]))),a[55]||(a[55]=(0,u.eW)(" 复制 "))]}),_:1,__:[55]})])])):(0,u.Q3)("",!0),n.user.blockchainPublicKey?((0,u.uX)(),(0,u.CE)("div",Ge,[a[59]||(a[59]=(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("div",{class:"card-icon key-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5l-41.1 41.1-62.3-62.3c-3.1-3.1-8.2-3.1-11.3 0l-39.6 39.6c-3.1 3.1-3.1 8.2 0 11.3l62.3 62.3-44.9 44.9c-3.1 3.1-3.1 8.2 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l44.9-44.9 62.3 62.3c3.1 3.1 8.2 3.1 11.3 0l39.6-39.6c3.1-3.1 3.1-8.2 0-11.3l-62.3-62.3 41.1-41.1C473 712.1 537.7 736 608 736c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.2 668.9 648 608 648s-118.2-27.8-161.2-70.8C403.8 534.2 376 476.9 376 416s27.8-118.2 70.8-161.2C489.8 211.8 547.1 184 608 184s118.2 27.8 161.2 70.8C812.2 297.8 840 355.1 840 416s-27.8 118.2-70.8 161.2zM608 256c-88.2 0-160 71.8-160 160s71.8 160 160 160 160-71.8 160-160-71.8-160-160-160zm0 256c-53 0-96-43-96-96s43-96 96-96 96 43 96 96-43 96-96 96z"})])]),(0,u.Lk)("div",{class:"card-title"},[(0,u.Lk)("h4",null,"公钥"),(0,u.Lk)("p",null,"用于验证数字签名的公开密钥")])],-1)),(0,u.Lk)("div",Ze,[(0,u.Lk)("div",ea,[(0,u.Lk)("div",aa,(0,w.v_)(n.user.blockchainPublicKey),1)]),(0,u.bF)(o,{type:"primary",size:"small",onClick:a[8]||(a[8]=function(e){return n.copyToClipboard(n.user.blockchainPublicKey,"公钥")}),class:"copy-button"},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",ta,a[57]||(a[57]=[(0,u.Lk)("path",{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.4c2 0 3.9-.7 5.3-2l.3-.3L924 391.3c2.3-2.3 3.7-5.3 3.7-8.5V192c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM888 349L562 675H232V264h656v85z"},null,-1)]))),a[58]||(a[58]=(0,u.eW)(" 复制 "))]}),_:1,__:[58]})])])):(0,u.Q3)("",!0)])):((0,u.uX)(),(0,u.CE)("div",na,a[60]||(a[60]=[(0,u.Lk)("div",{class:"empty-state"},[(0,u.Lk)("div",{class:"empty-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"32",height:"32",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])]),(0,u.Lk)("h3",null,"暂无区块链信息"),(0,u.Lk)("p",null,"您的账户尚未绑定区块链身份"),(0,u.Lk)("p",{class:"empty-tip"},"如需使用区块链功能，请联系管理员进行配置")],-1)])))])])]}),_:1}),(0,u.bF)(p,{label:"安全设置",name:"security"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",ra,[a[63]||(a[63]=(0,u.Lk)("h3",{class:"section-title"},"修改密码",-1)),(0,u.bF)(f,{ref:"passwordFormRef",model:n.passwordForm,rules:n.passwordRules,"label-position":"left","label-width":"100px"},{default:(0,u.k6)(function(){return[(0,u.bF)(v,{label:"当前密码",prop:"currentPassword"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.passwordForm.currentPassword,"onUpdate:modelValue":a[9]||(a[9]=function(e){return n.passwordForm.currentPassword=e}),type:"password","show-password":""},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,{label:"新密码",prop:"newPassword"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.passwordForm.newPassword,"onUpdate:modelValue":a[10]||(a[10]=function(e){return n.passwordForm.newPassword=e}),type:"password","show-password":""},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,{label:"确认新密码",prop:"confirmPassword"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.passwordForm.confirmPassword,"onUpdate:modelValue":a[11]||(a[11]=function(e){return n.passwordForm.confirmPassword=e}),type:"password","show-password":""},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,null,{default:(0,u.k6)(function(){return[(0,u.bF)(o,{type:"primary",onClick:n.changePassword,loading:n.loading},{default:(0,u.k6)(function(){return a[62]||(a[62]=[(0,u.eW)("修改密码")])}),_:1,__:[62]},8,["onClick","loading"])]}),_:1})]}),_:1},8,["model","rules"])])]}),_:1}),"enterprise"===n.user.userType?((0,u.uX)(),(0,u.Wv)(p,{key:0,label:"我的账户",name:"account"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",ia,[(0,u.Lk)("div",sa,[(0,u.bF)(b,{gutter:20},{default:(0,u.k6)(function(){return[(0,u.bF)(m,{span:12},{default:(0,u.k6)(function(){return[(0,u.bF)(h,{class:"balance-card"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",la,[a[64]||(a[64]=(0,u.Lk)("div",{class:"balance-label"},"账户余额",-1)),(0,u.Lk)("div",ca,"¥"+(0,w.v_)(n.formatNumber(n.accountBalance.currentBalance||0)),1)])]}),_:1})]}),_:1}),(0,u.bF)(m,{span:12},{default:(0,u.k6)(function(){return[(0,u.bF)(h,{class:"balance-card"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",oa,[a[65]||(a[65]=(0,u.Lk)("div",{class:"balance-label"},"已发布任务",-1)),(0,u.Lk)("div",da,(0,w.v_)(n.myTasks.length),1)])]}),_:1})]}),_:1})]}),_:1})]),(0,u.bF)(h,{class:"recharge-card",style:{"margin-top":"20px"}},{header:(0,u.k6)(function(){return a[66]||(a[66]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("h3",null,"账户充值"),(0,u.Lk)("p",null,"为您的企业账户充值，用于发布安全测试任务")],-1)])}),default:(0,u.k6)(function(){return[(0,u.bF)(f,{model:n.rechargeForm,rules:n.rechargeRules,ref:"rechargeFormRef","label-width":"100px"},{default:(0,u.k6)(function(){return[(0,u.bF)(v,{label:"充值金额",prop:"amount"},{default:(0,u.k6)(function(){return[(0,u.bF)(k,{modelValue:n.rechargeForm.amount,"onUpdate:modelValue":a[12]||(a[12]=function(e){return n.rechargeForm.amount=e}),min:100,max:1e5,step:100,placeholder:"请输入充值金额",style:{width:"200px"}},null,8,["modelValue"]),a[67]||(a[67]=(0,u.Lk)("span",{style:{"margin-left":"10px",color:"#999"}},"元（最低100元）",-1))]}),_:1,__:[67]}),(0,u.bF)(v,{label:"支付方式"},{default:(0,u.k6)(function(){return[(0,u.bF)(y,{modelValue:n.rechargeForm.paymentMethod,"onUpdate:modelValue":a[13]||(a[13]=function(e){return n.rechargeForm.paymentMethod=e})},{default:(0,u.k6)(function(){return[(0,u.bF)(g,{label:"alipay"},{default:(0,u.k6)(function(){return a[68]||(a[68]=[(0,u.eW)("支付宝")])}),_:1,__:[68]}),(0,u.bF)(g,{label:"wechat"},{default:(0,u.k6)(function(){return a[69]||(a[69]=[(0,u.eW)("微信支付")])}),_:1,__:[69]}),(0,u.bF)(g,{label:"bank"},{default:(0,u.k6)(function(){return a[70]||(a[70]=[(0,u.eW)("银行转账")])}),_:1,__:[70]})]}),_:1},8,["modelValue"])]}),_:1}),(0,u.bF)(v,null,{default:(0,u.k6)(function(){return[(0,u.bF)(o,{type:"primary",onClick:n.submitRecharge,loading:n.rechargingLoading},{default:(0,u.k6)(function(){return a[71]||(a[71]=[(0,u.eW)("立即充值")])}),_:1,__:[71]},8,["onClick","loading"])]}),_:1})]}),_:1},8,["model","rules"])]}),_:1}),(0,u.bF)(h,{class:"my-tasks-card",style:{"margin-top":"20px"}},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",ua,[a[73]||(a[73]=(0,u.Lk)("h3",null,"我发布的任务",-1)),(0,u.bF)(o,{type:"primary",onClick:a[14]||(a[14]=function(a){return e.$router.push("/enterprise/publish")})},{default:(0,u.k6)(function(){return a[72]||(a[72]=[(0,u.eW)("发布新任务")])}),_:1,__:[72]})])]}),default:(0,u.k6)(function(){return[(0,u.bo)(((0,u.uX)(),(0,u.Wv)(F,{data:n.myTasks,style:{width:"100%"}},{default:(0,u.k6)(function(){return[(0,u.bF)(L,{prop:"title",label:"任务标题","min-width":"200"}),(0,u.bF)(L,{prop:"status",label:"状态",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getTaskStatusType(e.row.status)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getTaskStatusText(e.row.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"totalBudgetMax",label:"最高预算",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.eW)(" ¥"+(0,w.v_)(n.formatNumber(e.row.totalBudgetMax)),1)]}),_:1}),(0,u.bF)(L,{prop:"participantCount",label:"参与人数",width:"100"}),(0,u.bF)(L,{prop:"submissionCount",label:"提交数",width:"100"}),(0,u.bF)(L,{prop:"createdAt",label:"发布时间",width:"180"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(n.formatDate(e.row.createdAt)),1)]}),_:1}),(0,u.bF)(L,{label:"操作",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.bF)(o,{type:"primary",size:"small",onClick:function(a){return n.viewTaskDetail(e.row)}},{default:(0,u.k6)(function(){return a[74]||(a[74]=[(0,u.eW)("查看详情")])}),_:2,__:[74]},1032,["onClick"])]}),_:1})]}),_:1},8,["data"])),[[H,n.loadingMyTasks]]),0!==n.myTasks.length||n.loadingMyTasks?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",va,[a[76]||(a[76]=(0,u.Lk)("i",{class:"el-icon-document"},null,-1)),a[77]||(a[77]=(0,u.Lk)("p",null,"暂无发布的任务",-1)),(0,u.Lk)("p",fa,[(0,u.bF)(o,{type:"primary",onClick:a[15]||(a[15]=function(a){return e.$router.push("/enterprise/publish")})},{default:(0,u.k6)(function(){return a[75]||(a[75]=[(0,u.eW)("发布第一个任务")])}),_:1,__:[75]})])]))]}),_:1})])]}),_:1})):(0,u.Q3)("",!0),"whiteHat"===n.user.userType?((0,u.uX)(),(0,u.Wv)(p,{key:1,label:"我的提交",name:"submissions"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",pa,[(0,u.Lk)("div",ha,[a[82]||(a[82]=(0,u.Lk)("h3",{class:"section-title"},"漏洞提交记录",-1)),(0,u.Lk)("div",ma,[(0,u.bF)(_,{title:"总提交数",value:n.submissionStats.total},{prefix:(0,u.k6)(function(){return a[78]||(a[78]=[(0,u.Lk)("i",{class:"el-icon-document",style:{color:"#409EFF"}},null,-1)])}),_:1},8,["value"]),(0,u.bF)(_,{title:"已确认",value:n.submissionStats.confirmed},{prefix:(0,u.k6)(function(){return a[79]||(a[79]=[(0,u.Lk)("i",{class:"el-icon-circle-check",style:{color:"#67C23A"}},null,-1)])}),_:1},8,["value"]),(0,u.bF)(_,{title:"待审核",value:n.submissionStats.pending},{prefix:(0,u.k6)(function(){return a[80]||(a[80]=[(0,u.Lk)("i",{class:"el-icon-clock",style:{color:"#E6A23C"}},null,-1)])}),_:1},8,["value"]),(0,u.bF)(_,{title:"已拒绝",value:n.submissionStats.rejected},{prefix:(0,u.k6)(function(){return a[81]||(a[81]=[(0,u.Lk)("i",{class:"el-icon-circle-close",style:{color:"#F56C6C"}},null,-1)])}),_:1},8,["value"])])]),(0,u.Lk)("div",ba,[(0,u.bF)(x,{modelValue:n.submissionFilter,"onUpdate:modelValue":a[16]||(a[16]=function(e){return n.submissionFilter=e}),placeholder:"筛选状态",clearable:""},{default:(0,u.k6)(function(){return[(0,u.bF)(C,{label:"全部",value:""}),(0,u.bF)(C,{label:"待审核",value:"pending"}),(0,u.bF)(C,{label:"已确认",value:"confirmed"}),(0,u.bF)(C,{label:"已拒绝",value:"rejected"}),(0,u.bF)(C,{label:"已修复",value:"fixed"})]}),_:1},8,["modelValue"]),(0,u.bF)(o,{onClick:n.refreshSubmissions,loading:n.loadingSubmissions},{default:(0,u.k6)(function(){return a[83]||(a[83]=[(0,u.eW)("刷新")])}),_:1,__:[83]},8,["onClick","loading"])]),(0,u.bo)(((0,u.uX)(),(0,u.Wv)(F,{data:n.submissions,stripe:"",height:400,style:{width:"100%"}},{default:(0,u.k6)(function(){return[(0,u.bF)(L,{prop:"title",label:"漏洞标题","min-width":"200"},{default:(0,u.k6)(function(e){return[(0,u.bF)(A,{onClick:function(a){return n.viewSubmissionDetail(e.row.id)},type:"primary"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(e.row.title),1)]}),_:2},1032,["onClick"])]}),_:1}),(0,u.bF)(L,{prop:"severity",label:"严重程度",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getSeverityType(e.row.severity)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getSeverityText(e.row.severity)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"status",label:"状态",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getStatusType(e.row.status)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getStatusText(e.row.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"reward",label:"奖励金额",width:"120"},{default:(0,u.k6)(function(e){return[e.row.reward?((0,u.uX)(),(0,u.CE)("span",ka,"¥"+(0,w.v_)(n.formatNumber(e.row.reward)),1)):((0,u.uX)(),(0,u.CE)("span",ga,"待定"))]}),_:1}),(0,u.bF)(L,{prop:"createdAt",label:"提交时间",width:"180"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(n.formatDateTime(e.row.createdAt)),1)]}),_:1}),(0,u.bF)(L,{label:"操作",width:"240",fixed:"right"},{default:(0,u.k6)(function(e){return[(0,u.Lk)("div",wa,[(0,u.bF)(o,{type:"primary",size:"small",onClick:function(a){return n.viewSubmissionDetail(e.row)}},{default:(0,u.k6)(function(){return a[84]||(a[84]=[(0,u.eW)(" 查看详情 ")])}),_:2,__:[84]},1032,["onClick"]),"enterprise_confirmed"===e.row.status||"enterprise_rejected"===e.row.status?((0,u.uX)(),(0,u.Wv)(o,{key:0,type:"success",size:"small",onClick:function(a){return n.confirmVulnerability(e.row)}},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)("enterprise_confirmed"===e.row.status?"确认":"接受"),1)]}),_:2},1032,["onClick"])):(0,u.Q3)("",!0),"enterprise_confirmed"===e.row.status||"enterprise_rejected"===e.row.status?((0,u.uX)(),(0,u.Wv)(o,{key:1,type:"warning",size:"small",onClick:function(a){return n.disputeVulnerability(e.row)}},{default:(0,u.k6)(function(){return a[85]||(a[85]=[(0,u.eW)(" 申诉 ")])}),_:2,__:[85]},1032,["onClick"])):(0,u.Q3)("",!0)])]}),_:1})]}),_:1},8,["data"])),[[H,n.loadingSubmissions]]),0!==n.submissions.length||n.loadingSubmissions?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",ya,[a[87]||(a[87]=(0,u.Lk)("i",{class:"el-icon-document"},null,-1)),a[88]||(a[88]=(0,u.Lk)("p",null,"暂无提交记录",-1)),(0,u.bF)(S,{to:"/submit"},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{type:"primary"},{default:(0,u.k6)(function(){return a[86]||(a[86]=[(0,u.eW)("立即提交漏洞")])}),_:1,__:[86]})]}),_:1})]))])]}),_:1})):(0,u.Q3)("",!0),"whiteHat"===n.user.userType?((0,u.uX)(),(0,u.Wv)(p,{key:2,label:"我的奖励",name:"rewards"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",La,[a[98]||(a[98]=(0,u.Lk)("div",{class:"section-header"},[(0,u.Lk)("h3",{class:"section-title"},"奖励记录")],-1)),(0,u.Lk)("div",Fa,[(0,u.bF)(h,{class:"summary-card total-earned"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",_a,[a[90]||(a[90]=(0,u.Lk)("div",{class:"summary-icon"},[(0,u.Lk)("i",{class:"el-icon-coin",style:{color:"#F56C6C"}})],-1)),(0,u.Lk)("div",Ca,[(0,u.Lk)("div",xa,"¥"+(0,w.v_)(n.formatNumber(n.rewardStats.totalEarned)),1),a[89]||(a[89]=(0,u.Lk)("div",{class:"summary-label"},"累计收入",-1))])])]}),_:1}),(0,u.bF)(h,{class:"summary-card this-month"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Aa,[a[92]||(a[92]=(0,u.Lk)("div",{class:"summary-icon"},[(0,u.Lk)("i",{class:"el-icon-money",style:{color:"#67C23A"}})],-1)),(0,u.Lk)("div",Sa,[(0,u.Lk)("div",Ta,"¥"+(0,w.v_)(n.formatNumber(n.rewardStats.thisMonth)),1),a[91]||(a[91]=(0,u.Lk)("div",{class:"summary-label"},"本月收入",-1))])])]}),_:1}),(0,u.bF)(h,{class:"summary-card total-rewards"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",za,[a[94]||(a[94]=(0,u.Lk)("div",{class:"summary-icon"},[(0,u.Lk)("i",{class:"el-icon-trophy",style:{color:"#E6A23C"}})],-1)),(0,u.Lk)("div",Ma,[(0,u.Lk)("div",Ea,(0,w.v_)(n.rewardStats.totalRewards),1),a[93]||(a[93]=(0,u.Lk)("div",{class:"summary-label"},"奖励次数",-1))])])]}),_:1})]),(0,u.Lk)("div",Va,[(0,u.bF)(T,{modelValue:n.rewardDateRange,"onUpdate:modelValue":a[17]||(a[17]=function(e){return n.rewardDateRange=e}),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:n.filterRewards},null,8,["modelValue","onChange"]),(0,u.bF)(o,{onClick:n.refreshRewards,loading:n.loadingRewards},{default:(0,u.k6)(function(){return a[95]||(a[95]=[(0,u.eW)("刷新")])}),_:1,__:[95]},8,["onClick","loading"])]),(0,u.bo)(((0,u.uX)(),(0,u.Wv)(F,{data:n.rewards,stripe:"",height:400,style:{width:"100%"}},{default:(0,u.k6)(function(){return[(0,u.bF)(L,{prop:"vulnerabilityTitle",label:"相关漏洞","min-width":"200"},{default:(0,u.k6)(function(e){return[(0,u.bF)(A,{onClick:function(a){return n.viewSubmissionDetail(e.row.vulnerabilityId)},type:"primary"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(e.row.vulnerabilityTitle),1)]}),_:2},1032,["onClick"])]}),_:1}),(0,u.bF)(L,{prop:"amount",label:"奖励金额",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.Lk)("span",Ra,"¥"+(0,w.v_)(n.formatNumber(e.row.amount)),1)]}),_:1}),(0,u.bF)(L,{prop:"type",label:"奖励类型",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getRewardType(e.row.type)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getRewardTypeText(e.row.type)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"status",label:"发放状态",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getPaymentStatusType(e.row.status)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getPaymentStatusText(e.row.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"paidAt",label:"发放时间",width:"180"},{default:(0,u.k6)(function(e){return[e.row.paidAt?((0,u.uX)(),(0,u.CE)("span",Wa,(0,w.v_)(n.formatDateTime(e.row.paidAt)),1)):((0,u.uX)(),(0,u.CE)("span",Ha,"待发放"))]}),_:1}),(0,u.bF)(L,{label:"操作",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(o,{size:"small",type:"text",onClick:function(a){return n.viewRewardDetail(e.row)}},{default:(0,u.k6)(function(){return a[96]||(a[96]=[(0,u.eW)(" 详情 ")])}),_:2,__:[96]},1032,["onClick"])]}),_:1})]}),_:1},8,["data"])),[[H,n.loadingRewards]]),0!==n.rewards.length||n.loadingRewards?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",Ia,a[97]||(a[97]=[(0,u.Lk)("i",{class:"el-icon-money"},null,-1),(0,u.Lk)("p",null,"暂无奖励记录",-1),(0,u.Lk)("p",{class:"empty-tip"},"提交有效漏洞后即可获得奖励",-1)])))])]}),_:1})):(0,u.Q3)("",!0),"whiteHat"===n.user.userType?((0,u.uX)(),(0,u.Wv)(p,{key:3,label:"提现中心",name:"withdrawal"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Ba,[(0,u.Lk)("div",Xa,[(0,u.bF)(b,{gutter:20},{default:(0,u.k6)(function(){return[(0,u.bF)(m,{span:8},{default:(0,u.k6)(function(){return[(0,u.bF)(h,{class:"balance-card"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Da,[a[100]||(a[100]=(0,u.Lk)("div",{class:"balance-icon"},[(0,u.Lk)("i",{class:"el-icon-wallet",style:{color:"#409EFF"}})],-1)),(0,u.Lk)("div",Na,[(0,u.Lk)("div",Pa,"¥"+(0,w.v_)(n.formatNumber(n.balanceInfo.currentBalance)),1),a[99]||(a[99]=(0,u.Lk)("div",{class:"balance-label"},"当前余额",-1))])])]}),_:1})]}),_:1}),(0,u.bF)(m,{span:8},{default:(0,u.k6)(function(){return[(0,u.bF)(h,{class:"balance-card"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Ua,[a[102]||(a[102]=(0,u.Lk)("div",{class:"balance-icon"},[(0,u.Lk)("i",{class:"el-icon-coin",style:{color:"#67C23A"}})],-1)),(0,u.Lk)("div",Ka,[(0,u.Lk)("div",ja,"¥"+(0,w.v_)(n.formatNumber(n.balanceInfo.totalEarnings)),1),a[101]||(a[101]=(0,u.Lk)("div",{class:"balance-label"},"总收入",-1))])])]}),_:1})]}),_:1}),(0,u.bF)(m,{span:8},{default:(0,u.k6)(function(){return[(0,u.bF)(h,{class:"balance-card"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",qa,[a[104]||(a[104]=(0,u.Lk)("div",{class:"balance-icon"},[(0,u.Lk)("i",{class:"el-icon-bank-card",style:{color:"#E6A23C"}})],-1)),(0,u.Lk)("div",Qa,[(0,u.Lk)("div",Oa,"¥"+(0,w.v_)(n.formatNumber(n.balanceInfo.totalEarnings-n.balanceInfo.currentBalance)),1),a[103]||(a[103]=(0,u.Lk)("div",{class:"balance-label"},"已提现",-1))])])]}),_:1})]}),_:1})]}),_:1})]),n.showWithdrawalHistory?((0,u.uX)(),(0,u.Wv)(h,{key:1,class:"withdrawal-history-card"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",Ja,[a[111]||(a[111]=(0,u.Lk)("span",null,"提现记录",-1)),(0,u.bF)(o,{type:"text",onClick:a[23]||(a[23]=function(e){return n.showWithdrawalHistory=!1})},{default:(0,u.k6)(function(){return a[110]||(a[110]=[(0,u.eW)("申请提现")])}),_:1,__:[110]})])]}),default:(0,u.k6)(function(){return[(0,u.bo)(((0,u.uX)(),(0,u.Wv)(F,{data:n.withdrawalHistory,stripe:""},{default:(0,u.k6)(function(){return[(0,u.bF)(L,{prop:"amount",label:"提现金额",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.Lk)("span",Ga,"¥"+(0,w.v_)(n.formatNumber(e.row.amount)),1)]}),_:1}),(0,u.bF)(L,{prop:"bankName",label:"银行",width:"120"}),(0,u.bF)(L,{prop:"bankCardNumber",label:"银行卡号",width:"150"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(e.row.bankCardNumber.replace(/(\d{4})\d*(\d{4})/,"$1****$2")),1)]}),_:1}),(0,u.bF)(L,{prop:"status",label:"状态",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.bF)(c,{type:n.getWithdrawalStatusType(e.row.status)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getWithdrawalStatusText(e.row.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,u.bF)(L,{prop:"applicationTime",label:"申请时间",width:"180"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(n.formatDateTime(e.row.applicationTime)),1)]}),_:1}),(0,u.bF)(L,{prop:"completionTime",label:"完成时间",width:"180"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(e.row.completionTime?n.formatDateTime(e.row.completionTime):"-"),1)]}),_:1})]}),_:1},8,["data"])),[[H,n.loadingWithdrawalHistory]]),0!==n.withdrawalHistory.length||n.loadingWithdrawalHistory?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",Za,a[112]||(a[112]=[(0,u.Lk)("i",{class:"el-icon-document"},null,-1),(0,u.Lk)("p",null,"暂无提现记录",-1)])))]}),_:1})):((0,u.uX)(),(0,u.Wv)(h,{key:0,class:"withdrawal-form-card"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",Ya,[a[106]||(a[106]=(0,u.Lk)("span",null,"申请提现",-1)),(0,u.bF)(o,{type:"text",onClick:a[18]||(a[18]=function(e){return n.showWithdrawalHistory=!0})},{default:(0,u.k6)(function(){return a[105]||(a[105]=[(0,u.eW)("查看提现记录")])}),_:1,__:[105]})])]}),default:(0,u.k6)(function(){return[(0,u.bF)(f,{ref:"withdrawalFormRef",model:n.withdrawalForm,rules:n.withdrawalRules,"label-width":"120px"},{default:(0,u.k6)(function(){return[(0,u.bF)(v,{label:"提现金额",prop:"amount"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.withdrawalForm.amount,"onUpdate:modelValue":a[19]||(a[19]=function(e){return n.withdrawalForm.amount=e}),type:"number",placeholder:"请输入提现金额",max:n.balanceInfo.currentBalance},{append:(0,u.k6)(function(){return a[107]||(a[107]=[(0,u.eW)("元")])}),_:1},8,["modelValue","max"]),(0,u.Lk)("div",$a,"可提现余额：¥"+(0,w.v_)(n.formatNumber(n.balanceInfo.currentBalance)),1)]}),_:1}),(0,u.bF)(v,{label:"银行卡号",prop:"bankCardNumber"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.withdrawalForm.bankCardNumber,"onUpdate:modelValue":a[20]||(a[20]=function(e){return n.withdrawalForm.bankCardNumber=e}),placeholder:"请输入银行卡号"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,{label:"银行名称",prop:"bankName"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.withdrawalForm.bankName,"onUpdate:modelValue":a[21]||(a[21]=function(e){return n.withdrawalForm.bankName=e}),placeholder:"请输入银行名称"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,{label:"开户人姓名",prop:"accountHolderName"},{default:(0,u.k6)(function(){return[(0,u.bF)(d,{modelValue:n.withdrawalForm.accountHolderName,"onUpdate:modelValue":a[22]||(a[22]=function(e){return n.withdrawalForm.accountHolderName=e}),placeholder:"请输入开户人姓名"},null,8,["modelValue"])]}),_:1}),(0,u.bF)(v,null,{default:(0,u.k6)(function(){return[(0,u.bF)(o,{type:"primary",onClick:n.submitWithdrawal,loading:n.loadingWithdrawal},{default:(0,u.k6)(function(){return a[108]||(a[108]=[(0,u.eW)(" 申请提现 ")])}),_:1,__:[108]},8,["onClick","loading"]),(0,u.bF)(o,{onClick:n.resetWithdrawalForm},{default:(0,u.k6)(function(){return a[109]||(a[109]=[(0,u.eW)("重置")])}),_:1,__:[109]},8,["onClick"])]}),_:1})]}),_:1},8,["model","rules"])]}),_:1}))])]}),_:1})):(0,u.Q3)("",!0),"whiteHat"===n.user.userType?((0,u.uX)(),(0,u.Wv)(p,{key:4,label:"证书中心",name:"certificates"},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",et,[(0,u.bF)(h,{class:"certificates-list-card"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",at,[a[113]||(a[113]=(0,u.Lk)("span",null,"我的证书",-1)),(0,u.bF)(x,{modelValue:n.certificateFilter,"onUpdate:modelValue":a[24]||(a[24]=function(e){return n.certificateFilter=e}),placeholder:"筛选严重程度",clearable:"",style:{width:"180px"},onChange:n.handleFilterChange},{default:(0,u.k6)(function(){return[(0,u.bF)(C,{label:"全部",value:""}),(0,u.bF)(C,{label:"严重",value:"critical"}),(0,u.bF)(C,{label:"高危",value:"high"}),(0,u.bF)(C,{label:"中危",value:"medium"}),(0,u.bF)(C,{label:"低危",value:"low"})]}),_:1},8,["modelValue","onChange"])])]}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",tt,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.certificates,function(e){return(0,u.uX)(),(0,u.CE)("div",{key:e.id,class:"certificate-card",onClick:function(a){return n.viewCertificateDetail(e)}},[(0,u.Lk)("div",rt,[(0,u.Lk)("div",it,(0,w.v_)(e.certificateId),1),(0,u.bF)(c,{type:n.getSeverityType(e.severity),size:"small"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getSeverityText(e.severity)),1)]}),_:2},1032,["type"])]),(0,u.Lk)("div",st,(0,w.v_)(e.vulnerabilityTitle),1),(0,u.Lk)("div",lt,[a[114]||(a[114]=(0,u.Lk)("div",{class:"certificate-company"},"CCIBE",-1)),(0,u.Lk)("div",ct,(0,w.v_)(n.formatDate(e.issueDate)),1)]),(0,u.Lk)("div",ot,"区块链编号："+(0,w.v_)(e.blockchainHash||"未生成"),1)],8,nt)}),128))]),n.certificatePagination.total>0?((0,u.uX)(),(0,u.CE)("div",dt,[(0,u.bF)(z,{"current-page":n.certificatePagination.page,"page-size":n.certificatePagination.limit,"page-sizes":[6,12,18,24],total:n.certificatePagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:n.handleCertificatePageSizeChange,onCurrentChange:n.handleCertificatePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])):(0,u.Q3)("",!0),0!==n.certificates.length||n.loadingCertificates?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",ut,a[115]||(a[115]=[(0,u.Lk)("i",{class:"el-icon-medal"},null,-1),(0,u.Lk)("p",null,"暂无证书",-1),(0,u.Lk)("p",{class:"empty-tip"},"提交有效漏洞并被确认后即可获得证书",-1)])))]}),_:1})])]}),_:1})):(0,u.Q3)("",!0)]}),_:1},8,["modelValue"])])):((0,u.uX)(),(0,u.CE)("div",vt,[a[117]||(a[117]=(0,u.Lk)("i",{class:"el-icon-warning"},null,-1)),a[118]||(a[118]=(0,u.Lk)("h2",null,"您尚未登录",-1)),a[119]||(a[119]=(0,u.Lk)("p",null,"请先登录后查看个人中心",-1)),(0,u.bF)(S,{to:"/login",class:"login-link"},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{type:"primary"},{default:(0,u.k6)(function(){return a[116]||(a[116]=[(0,u.eW)("前往登录")])}),_:1,__:[116]})]}),_:1})]))]),(0,u.bF)(E,{modelValue:n.showCertificateDetail,"onUpdate:modelValue":a[27]||(a[27]=function(e){return n.showCertificateDetail=e}),title:"",width:"900px",class:"certificate-detail-dialog","show-close":!1},{footer:(0,u.k6)(function(){return[(0,u.Lk)("div",Wt,[(0,u.bF)(o,{onClick:n.downloadCertificate,type:"primary",size:"large"},{default:(0,u.k6)(function(){return a[132]||(a[132]=[(0,u.Lk)("i",{class:"el-icon-download"},null,-1),(0,u.eW)(" 下载PDF证书 ")])}),_:1,__:[132]},8,["onClick"]),(0,u.bF)(o,{onClick:a[26]||(a[26]=function(e){return n.showCertificateDetail=!1}),size:"large"},{default:(0,u.k6)(function(){return a[133]||(a[133]=[(0,u.eW)("关闭")])}),_:1,__:[133]})])]}),default:(0,u.k6)(function(){var e,t;return[n.selectedCertificate?((0,u.uX)(),(0,u.CE)("div",ft,[(0,u.Lk)("div",pt,[a[130]||(a[130]=(0,u.Lk)("div",{class:"certificate-header-formal"},[(0,u.Lk)("div",{class:"certificate-logo"},[(0,u.Lk)("div",{class:"logo-circle"},[(0,u.Lk)("svg",{width:"40",height:"40",viewBox:"0 0 1024 1024",fill:"white"},[(0,u.Lk)("path",{d:"M208.640966 523.824302c0 125.298958 161.575608 342.92794 306.659019 342.92794s306.659019-220.928966 306.659019-342.92794V263.32643c-145.075683 0-227.521208-42.861162-306.659019-102.222249-79.137811 59.353358-161.575608 102.222249-306.659019 102.222249V523.824302z m187.952302-65.9456l79.137811 75.845555c32.976664-26.384423 62.653343-52.761117 89.030038-72.54557 13.192211-9.892226 29.676679-23.084438 42.868891-32.976664 13.192211-9.892226 26.384423-19.784453 42.86889-29.67668 26.384423-19.784453 59.353358-42.861162 95.622279-65.9456l23.07671 32.976665c-19.776725 13.192211-49.453404 39.568906-75.837827 62.653343a306.033026 306.033026 0 0 0-46.168875 46.161147C630.698989 494.147623 610.922264 513.932075 591.137811 537.008785 558.161147 576.577691 512 629.346536 469.131109 691.992151L304.263245 530.416543l92.330023-72.537841z",fill:"white"}),(0,u.Lk)("path",{d:"M512 19.320755C403.185509 101.758551 284.478792 161.111909 83.334279 161.111909v366.012378C83.334279 701.884377 310.855487 1008.543396 512 1008.543396c201.144513 0 428.665721-306.659019 428.665721-481.419109v-366.012378C739.513479 161.111909 620.814491 101.758551 512 19.320755z m366.004649 504.503547c0 148.383396-194.544543 412.173525-366.004649 412.173524S145.987623 672.207698 145.987623 523.824302V210.573042c171.467834 0 273.682355-49.461132 366.012377-122.006702 92.330023 72.54557 194.552272 122.006702 366.004649 122.006702V523.824302z",fill:"white"})])])]),(0,u.Lk)("h1",{class:"certificate-main-title"},"安全漏洞发现证书"),(0,u.Lk)("h2",{class:"certificate-subtitle"},"SECURITY VULNERABILITY DISCOVERY CERTIFICATE"),(0,u.Lk)("div",{class:"certificate-ornament"},[(0,u.Lk)("div",{class:"ornament-line"}),(0,u.Lk)("div",{class:"ornament-diamond"},"◆"),(0,u.Lk)("div",{class:"ornament-line"})])],-1)),(0,u.Lk)("div",ht,[(0,u.Lk)("div",mt,[a[122]||(a[122]=(0,u.Lk)("p",{class:"certificate-intro"},"兹证明",-1)),(0,u.Lk)("div",bt,(0,w.v_)((null===(e=n.selectedCertificate.user)||void 0===e?void 0:e.realName)||(null===(t=n.selectedCertificate.user)||void 0===t?void 0:t.username)||"匿名用户"),1),(0,u.Lk)("p",kt,[a[120]||(a[120]=(0,u.eW)(" 在网络安全领域表现卓越，成功发现并报告了 ")),(0,u.Lk)("span",gt,(0,w.v_)(n.getSeverityText(n.selectedCertificate.severity)),1),a[121]||(a[121]=(0,u.eW)(" 级别的安全漏洞，为维护网络安全做出了重要贡献。 "))]),a[123]||(a[123]=(0,u.Lk)("p",{class:"certificate-content-text"}," 该漏洞已通过专业安全团队验证确认，并已妥善处理。 特此颁发此证书以资鼓励。 ",-1))]),(0,u.Lk)("div",wt,[(0,u.Lk)("div",yt,[(0,u.Lk)("div",Lt,[a[124]||(a[124]=(0,u.Lk)("span",{class:"detail-label"},"证书编号：",-1)),(0,u.Lk)("span",Ft,(0,w.v_)(n.selectedCertificate.certificateNumber),1)]),(0,u.Lk)("div",_t,[a[125]||(a[125]=(0,u.Lk)("span",{class:"detail-label"},"区块链编号：",-1)),(0,u.Lk)("span",Ct,(0,w.v_)(n.selectedCertificate.blockchainHash||"未生成"),1)])]),(0,u.Lk)("div",xt,[(0,u.Lk)("div",At,[a[126]||(a[126]=(0,u.Lk)("span",{class:"detail-label"},"漏洞等级：",-1)),(0,u.Lk)("span",{class:(0,w.C4)(["detail-value severity-badge",n.selectedCertificate.severity])},(0,w.v_)(n.getSeverityText(n.selectedCertificate.severity)),3)]),(0,u.Lk)("div",St,[a[127]||(a[127]=(0,u.Lk)("span",{class:"detail-label"},"相关企业：",-1)),(0,u.Lk)("span",Tt,(0,w.v_)(n.selectedCertificate.companyName||"CCIBE"),1)])]),(0,u.Lk)("div",zt,[(0,u.Lk)("div",Mt,[a[128]||(a[128]=(0,u.Lk)("span",{class:"detail-label"},"项目名称：",-1)),(0,u.Lk)("span",Et,(0,w.v_)(n.selectedCertificate.projectName||"安全测试项目"),1)]),(0,u.Lk)("div",Vt,[a[129]||(a[129]=(0,u.Lk)("span",{class:"detail-label"},"颁发日期：",-1)),(0,u.Lk)("span",Rt,(0,w.v_)(n.formatDate(n.selectedCertificate.issueDate)),1)])])])]),a[131]||(a[131]=(0,u.Lk)("div",{class:"certificate-signature"},[(0,u.Lk)("div",{class:"signature-section"},[(0,u.Lk)("div",{class:"signature-center"},[(0,u.Lk)("div",{class:"official-seal"},[(0,u.Lk)("div",{class:"seal-outer"},[(0,u.Lk)("div",{class:"seal-inner"},[(0,u.Lk)("div",{class:"seal-text-top"},"衍盾星云"),(0,u.Lk)("div",{class:"seal-star"},"★"),(0,u.Lk)("div",{class:"seal-text-bottom"},"安全平台")])])])])]),(0,u.Lk)("div",{class:"certificate-footer-text"},[(0,u.Lk)("p",null,"衍盾星云安全平台 · YANDUN NEBULA SECURITY PLATFORM"),(0,u.Lk)("p",{class:"website"},"www.breakoutthough.cn")])],-1))])])):(0,u.Q3)("",!0)]}),_:1},8,["modelValue"]),(0,u.bF)(E,{modelValue:n.detailDialogVisible,"onUpdate:modelValue":a[38]||(a[38]=function(e){return n.detailDialogVisible=e}),title:n.selectedSubmission?n.selectedSubmission.title:"漏洞详情",width:"90%","before-close":n.closeDetailDialog,class:"vulnerability-detail-dialog"},{footer:(0,u.k6)(function(){return[(0,u.Lk)("span",jr,[(0,u.bF)(o,{onClick:n.closeDetailDialog},{default:(0,u.k6)(function(){return a[212]||(a[212]=[(0,u.eW)("关闭")])}),_:1,__:[212]},8,["onClick"])])]}),default:(0,u.k6)(function(){return[n.selectedSubmission?((0,u.uX)(),(0,u.CE)("div",Ht,[(0,u.bF)(h,{class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[134]||(a[134]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-info"}),(0,u.Lk)("span",null,"基本信息")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",It,[(0,u.Lk)("div",Bt,[a[135]||(a[135]=(0,u.Lk)("label",null,"厂商名称",-1)),(0,u.Lk)("div",Xt,(0,w.v_)(n.selectedSubmission.company_name||"未指定"),1)]),(0,u.Lk)("div",Dt,[a[136]||(a[136]=(0,u.Lk)("label",null,"域名/IP",-1)),(0,u.Lk)("div",Nt,(0,w.v_)(n.selectedSubmission.domain||"未指定"),1)]),(0,u.Lk)("div",Pt,[a[137]||(a[137]=(0,u.Lk)("label",null,"漏洞类型",-1)),(0,u.Lk)("div",Ut,[(0,u.bF)(c,{size:"small",type:"info"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getVulnTypeLabel(n.selectedSubmission.type)),1)]}),_:1})])]),(0,u.Lk)("div",Kt,[a[138]||(a[138]=(0,u.Lk)("label",null,"严重程度",-1)),(0,u.Lk)("div",jt,[(0,u.bF)(c,{type:n.getSeverityType(n.selectedSubmission.severity),size:"small"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getSeverityText(n.selectedSubmission.severity)),1)]}),_:1},8,["type"])])]),(0,u.Lk)("div",qt,[a[139]||(a[139]=(0,u.Lk)("label",null,"当前状态",-1)),(0,u.Lk)("div",Qt,[(0,u.bF)(c,{type:n.getStatusType(n.selectedSubmission.status),size:"small"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getStatusText(n.selectedSubmission.status)),1)]}),_:1},8,["type"])])]),(0,u.Lk)("div",Ot,[a[140]||(a[140]=(0,u.Lk)("label",null,"提交时间",-1)),(0,u.Lk)("div",Yt,(0,w.v_)(n.formatDateTime(n.selectedSubmission.createdAt)),1)]),(0,u.Lk)("div",$t,[a[141]||(a[141]=(0,u.Lk)("label",null,"奖励金额",-1)),(0,u.Lk)("div",Jt,[n.selectedSubmission.reward?((0,u.uX)(),(0,u.CE)("span",Gt,"¥"+(0,w.v_)(n.formatNumber(n.selectedSubmission.reward)),1)):((0,u.uX)(),(0,u.CE)("span",Zt,"待定"))])]),n.selectedSubmission.aiSeverity?((0,u.uX)(),(0,u.CE)("div",en,[a[142]||(a[142]=(0,u.Lk)("label",null,"AI评估等级",-1)),(0,u.Lk)("div",an,[(0,u.bF)(c,{type:n.getAISeverityType(n.selectedSubmission.aiSeverity),size:"small"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getAISeverityText(n.selectedSubmission.aiSeverity)),1)]}),_:1},8,["type"])])])):(0,u.Q3)("",!0)])]}),_:1}),n.hasSubmissionBlockchainInfo(n.selectedSubmission)?((0,u.uX)(),(0,u.Wv)(h,{key:0,class:"detail-card submission-blockchain-info-card",shadow:"never"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",tn,[a[145]||(a[145]=(0,u.Lk)("div",{class:"blockchain-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),a[146]||(a[146]=(0,u.Lk)("span",null,"漏洞提交记录 - 区块链验证",-1)),(0,u.bF)(c,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",nn,a[143]||(a[143]=[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),a[144]||(a[144]=(0,u.eW)(" 链上可验证 "))]}),_:1,__:[144]})])]}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",rn,[(0,u.Lk)("div",sn,[(0,u.Lk)("div",ln,[a[148]||(a[148]=(0,u.Lk)("div",{class:"title-icon submission-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2a7.63 7.63 0 0 1-2.9-6.1V351c0-6.8 7.9-10.5 13.1-6.1l192 161c3.9 3.2 3.9 9.1 0 12.2zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"})])],-1)),a[149]||(a[149]=(0,u.Lk)("span",null,"漏洞提交操作",-1)),(0,u.bF)(c,{type:"primary",size:"mini"},{default:(0,u.k6)(function(){return a[147]||(a[147]=[(0,u.eW)("已提交")])}),_:1,__:[147]})]),(0,u.Lk)("div",cn,[(0,u.Lk)("div",on,[(0,u.Lk)("div",dn,[a[151]||(a[151]=(0,u.Lk)("label",null,"交易哈希",-1)),(0,u.Lk)("div",un,[(0,u.Lk)("span",vn,(0,w.v_)(n.selectedSubmission.submissionTransactionHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[28]||(a[28]=function(e){return n.copyToClipboard(n.selectedSubmission.submissionTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[150]||(a[150]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[150]})])])]),(0,u.Lk)("div",fn,[(0,u.Lk)("div",pn,[a[153]||(a[153]=(0,u.Lk)("label",null,"区块哈希",-1)),(0,u.Lk)("div",hn,[(0,u.Lk)("span",mn,(0,w.v_)(n.selectedSubmission.submissionBlockHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[29]||(a[29]=function(e){return n.copyToClipboard(n.selectedSubmission.submissionBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[152]||(a[152]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[152]})])])]),(0,u.Lk)("div",bn,[(0,u.Lk)("div",kn,[a[154]||(a[154]=(0,u.Lk)("label",null,"记录时间",-1)),(0,u.Lk)("div",gn,(0,w.v_)(n.formatTimestamp(n.selectedSubmission.submissionBlockchainTimestamp)),1)])])])]),a[155]||(a[155]=(0,u.Lk)("div",{class:"blockchain-notice"},[(0,u.Lk)("div",{class:"notice-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,u.Lk)("div",{class:"notice-text"},[(0,u.Lk)("p",null,"此漏洞提交信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,u.Q3)("",!0),n.hasApprovalBlockchainInfo(n.selectedSubmission)?((0,u.uX)(),(0,u.Wv)(h,{key:1,class:"detail-card approval-blockchain-info-card",shadow:"never"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",wn,[a[158]||(a[158]=(0,u.Lk)("div",{class:"blockchain-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),a[159]||(a[159]=(0,u.Lk)("span",null,"企业审批记录 - 区块链验证",-1)),(0,u.bF)(c,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",yn,a[156]||(a[156]=[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),a[157]||(a[157]=(0,u.eW)(" 链上可验证 "))]}),_:1,__:[157]})])]}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Ln,[(0,u.Lk)("div",Fn,[(0,u.Lk)("div",_n,[a[160]||(a[160]=(0,u.Lk)("div",{class:"title-icon approval-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-55.9 718.9L205.3 532.1c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L456 692.2 773.3 374.9c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3L501.4 737.4c-6.2 6.2-14.4 9.4-22.6 9.4s-16.4-3.1-22.6-9.4z"})])],-1)),a[161]||(a[161]=(0,u.Lk)("span",null,"企业审批操作",-1)),(0,u.bF)(c,{type:n.getApprovalStatusType(n.selectedSubmission),size:"mini"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getApprovalStatusText(n.selectedSubmission)),1)]}),_:1},8,["type"])]),(0,u.Lk)("div",Cn,[(0,u.Lk)("div",xn,[(0,u.Lk)("div",An,[a[163]||(a[163]=(0,u.Lk)("label",null,"交易哈希",-1)),(0,u.Lk)("div",Sn,[(0,u.Lk)("span",Tn,(0,w.v_)(n.selectedSubmission.approvalTransactionHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[30]||(a[30]=function(e){return n.copyToClipboard(n.selectedSubmission.approvalTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[162]||(a[162]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[162]})])])]),(0,u.Lk)("div",zn,[(0,u.Lk)("div",Mn,[a[165]||(a[165]=(0,u.Lk)("label",null,"区块哈希",-1)),(0,u.Lk)("div",En,[(0,u.Lk)("span",Vn,(0,w.v_)(n.selectedSubmission.approvalBlockHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[31]||(a[31]=function(e){return n.copyToClipboard(n.selectedSubmission.approvalBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[164]||(a[164]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[164]})])])]),(0,u.Lk)("div",Rn,[(0,u.Lk)("div",Wn,[a[166]||(a[166]=(0,u.Lk)("label",null,"记录时间",-1)),(0,u.Lk)("div",Hn,(0,w.v_)(n.formatTimestamp(n.selectedSubmission.approvalBlockchainTimestamp)),1)])])])]),a[167]||(a[167]=(0,u.Lk)("div",{class:"blockchain-notice"},[(0,u.Lk)("div",{class:"notice-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,u.Lk)("div",{class:"notice-text"},[(0,u.Lk)("p",null,"此企业审批信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,u.Q3)("",!0),n.hasBlockchainInfo(n.selectedSubmission)?((0,u.uX)(),(0,u.Wv)(h,{key:2,class:"detail-card blockchain-info-card",shadow:"never"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",In,[a[170]||(a[170]=(0,u.Lk)("div",{class:"blockchain-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),a[171]||(a[171]=(0,u.Lk)("span",null,"白帽确认信息 - 区块链记录",-1)),(0,u.bF)(c,{type:"success",size:"small",effect:"light",style:{"margin-left":"8px"}},{default:(0,u.k6)(function(){return[((0,u.uX)(),(0,u.CE)("svg",Bn,a[168]||(a[168]=[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"},null,-1)]))),a[169]||(a[169]=(0,u.eW)(" 链上可验证 "))]}),_:1,__:[169]})])]}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Xn,[n.selectedSubmission.confirmationTransactionHash?((0,u.uX)(),(0,u.CE)("div",Dn,[(0,u.Lk)("div",Nn,[a[173]||(a[173]=(0,u.Lk)("div",{class:"title-icon confirm-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2l-359 493.4-183.1-252.4c-6-8.2-15.8-12.2-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"})])],-1)),a[174]||(a[174]=(0,u.Lk)("span",null,"用户确认操作",-1)),(0,u.bF)(c,{type:"success",size:"mini"},{default:(0,u.k6)(function(){return a[172]||(a[172]=[(0,u.eW)("已确认")])}),_:1,__:[172]})]),(0,u.Lk)("div",Pn,[(0,u.Lk)("div",Un,[(0,u.Lk)("div",Kn,[a[176]||(a[176]=(0,u.Lk)("label",null,"交易哈希",-1)),(0,u.Lk)("div",jn,[(0,u.Lk)("span",qn,(0,w.v_)(n.selectedSubmission.confirmationTransactionHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[32]||(a[32]=function(e){return n.copyToClipboard(n.selectedSubmission.confirmationTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[175]||(a[175]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[175]})])])]),(0,u.Lk)("div",Qn,[(0,u.Lk)("div",On,[a[178]||(a[178]=(0,u.Lk)("label",null,"区块哈希",-1)),(0,u.Lk)("div",Yn,[(0,u.Lk)("span",$n,(0,w.v_)(n.selectedSubmission.confirmationBlockHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[33]||(a[33]=function(e){return n.copyToClipboard(n.selectedSubmission.confirmationBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[177]||(a[177]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[177]})])])]),(0,u.Lk)("div",Jn,[(0,u.Lk)("div",Gn,[a[179]||(a[179]=(0,u.Lk)("label",null,"记录时间",-1)),(0,u.Lk)("div",Zn,(0,w.v_)(n.formatTimestamp(n.selectedSubmission.confirmationTimestamp)),1)])])])])):(0,u.Q3)("",!0),n.selectedSubmission.disputeTransactionHash?((0,u.uX)(),(0,u.CE)("div",er,[(0,u.Lk)("div",ar,[a[181]||(a[181]=(0,u.Lk)("div",{class:"title-icon dispute-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])],-1)),a[182]||(a[182]=(0,u.Lk)("span",null,"用户申诉操作",-1)),(0,u.bF)(c,{type:"warning",size:"mini"},{default:(0,u.k6)(function(){return a[180]||(a[180]=[(0,u.eW)("已申诉")])}),_:1,__:[180]})]),(0,u.Lk)("div",tr,[(0,u.Lk)("div",nr,[(0,u.Lk)("div",rr,[a[184]||(a[184]=(0,u.Lk)("label",null,"交易哈希",-1)),(0,u.Lk)("div",ir,[(0,u.Lk)("span",sr,(0,w.v_)(n.selectedSubmission.disputeTransactionHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[34]||(a[34]=function(e){return n.copyToClipboard(n.selectedSubmission.disputeTransactionHash,"交易哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[183]||(a[183]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[183]})])])]),(0,u.Lk)("div",lr,[(0,u.Lk)("div",cr,[a[186]||(a[186]=(0,u.Lk)("label",null,"区块哈希",-1)),(0,u.Lk)("div",or,[(0,u.Lk)("span",dr,(0,w.v_)(n.selectedSubmission.disputeBlockHash),1),(0,u.bF)(o,{type:"text",size:"small",onClick:a[35]||(a[35]=function(e){return n.copyToClipboard(n.selectedSubmission.disputeBlockHash,"区块哈希")}),class:"copy-btn"},{default:(0,u.k6)(function(){return a[185]||(a[185]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[185]})])])]),(0,u.Lk)("div",ur,[(0,u.Lk)("div",vr,[a[187]||(a[187]=(0,u.Lk)("label",null,"记录时间",-1)),(0,u.Lk)("div",fr,(0,w.v_)(n.formatTimestamp(n.selectedSubmission.disputeTimestamp)),1)])])])])):(0,u.Q3)("",!0),a[188]||(a[188]=(0,u.Lk)("div",{class:"blockchain-notice"},[(0,u.Lk)("div",{class:"notice-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,u.Lk)("div",{class:"notice-text"},[(0,u.Lk)("p",null,"以上信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,u.Q3)("",!0),n.hasAdminReviewBlockchainInfo(n.selectedSubmission)?((0,u.uX)(),(0,u.Wv)(h,{key:3,class:"detail-card admin-review-blockchain-info-card",shadow:"never"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",pr,[a[190]||(a[190]=(0,u.Lk)("div",{class:"blockchain-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"16",height:"16",fill:"currentColor"},[(0,u.Lk)("path",{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z"})])],-1)),a[191]||(a[191]=(0,u.Lk)("span",null,"管理员审核记录 - 区块链验证",-1)),(0,u.bF)(c,{type:n.getAdminReviewStatusType(n.selectedSubmission),size:"mini",style:{"margin-left":"8px"}},{default:(0,u.k6)(function(){return a[189]||(a[189]=[(0,u.eW)(" 链上可验证 ")])}),_:1,__:[189]},8,["type"])])]}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",hr,[(0,u.Lk)("div",mr,[(0,u.Lk)("div",br,[a[192]||(a[192]=(0,u.Lk)("div",{class:"title-icon admin-review-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-55.9 718.9L205.3 532.1c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L456 692.2 773.3 374.9c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3L501.4 737.4c-6.2 6.2-14.4 9.4-22.6 9.4s-16.4-3.1-22.6-9.4z"})])],-1)),a[193]||(a[193]=(0,u.Lk)("span",null,"管理员审核操作",-1)),(0,u.bF)(c,{type:n.getAdminReviewStatusType(n.selectedSubmission),size:"mini"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getAdminReviewStatusText(n.selectedSubmission)),1)]}),_:1},8,["type"])]),(0,u.Lk)("div",kr,[(0,u.Lk)("div",gr,[(0,u.Lk)("div",wr,[a[195]||(a[195]=(0,u.Lk)("label",null,"交易哈希",-1)),(0,u.Lk)("div",yr,[(0,u.Lk)("span",Lr,(0,w.v_)(n.selectedSubmission.adminReviewTransactionHash),1),(0,u.bF)(o,{type:"text",size:"mini",class:"copy-btn",onClick:a[36]||(a[36]=function(e){return n.copyToClipboard(n.selectedSubmission.adminReviewTransactionHash,"管理员审核交易哈希")})},{default:(0,u.k6)(function(){return a[194]||(a[194]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[194]})])])]),(0,u.Lk)("div",Fr,[(0,u.Lk)("div",_r,[a[197]||(a[197]=(0,u.Lk)("label",null,"区块哈希",-1)),(0,u.Lk)("div",Cr,[(0,u.Lk)("span",xr,(0,w.v_)(n.selectedSubmission.adminReviewBlockHash),1),(0,u.bF)(o,{type:"text",size:"mini",class:"copy-btn",onClick:a[37]||(a[37]=function(e){return n.copyToClipboard(n.selectedSubmission.adminReviewBlockHash,"管理员审核区块哈希")})},{default:(0,u.k6)(function(){return a[196]||(a[196]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1)])}),_:1,__:[196]})])])]),(0,u.Lk)("div",Ar,[(0,u.Lk)("div",Sr,[a[198]||(a[198]=(0,u.Lk)("label",null,"记录时间",-1)),(0,u.Lk)("div",Tr,(0,w.v_)(n.formatTimestamp(n.selectedSubmission.adminReviewBlockchainTimestamp)),1)])])])]),a[199]||(a[199]=(0,u.Lk)("div",{class:"blockchain-notice"},[(0,u.Lk)("div",{class:"notice-icon"},[(0,u.Lk)("svg",{viewBox:"0 0 1024 1024",width:"14",height:"14",fill:"currentColor"},[(0,u.Lk)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"})])]),(0,u.Lk)("div",{class:"notice-text"},[(0,u.Lk)("p",null,"此管理员审核信息已永久记录在区块链上，具有不可篡改性，可通过交易哈希在区块链浏览器中查询验证。")])],-1))])]}),_:1})):(0,u.Q3)("",!0),(0,u.bF)(h,{class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[200]||(a[200]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-document"}),(0,u.Lk)("span",null,"漏洞描述")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",zr,(0,w.v_)(n.selectedSubmission.description||"暂无描述"),1)]}),_:1}),n.selectedSubmission.reproductionSteps?((0,u.uX)(),(0,u.Wv)(h,{key:4,class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[201]||(a[201]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-cpu"}),(0,u.Lk)("span",null,"复现步骤")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Mr,(0,w.v_)(n.selectedSubmission.reproductionSteps),1)]}),_:1})):(0,u.Q3)("",!0),n.selectedSubmission.remediation?((0,u.uX)(),(0,u.Wv)(h,{key:5,class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[202]||(a[202]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-tools"}),(0,u.Lk)("span",null,"修复建议")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Er,(0,w.v_)(n.selectedSubmission.remediation),1)]}),_:1})):(0,u.Q3)("",!0),n.selectedSubmission.aiAnalysis?((0,u.uX)(),(0,u.Wv)(h,{key:6,class:"detail-card ai-analysis-card",shadow:"never"},{header:(0,u.k6)(function(){return a[203]||(a[203]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-cpu"}),(0,u.Lk)("span",null,"AI智能分析")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Vr,[(0,u.Lk)("div",Rr,[a[204]||(a[204]=(0,u.Lk)("label",null,"AI评估等级：",-1)),(0,u.bF)(c,{type:n.getAISeverityType(n.selectedSubmission.aiSeverity),size:"medium"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getAISeverityText(n.selectedSubmission.aiSeverity)),1)]}),_:1},8,["type"])]),(0,u.Lk)("div",Wr,[a[205]||(a[205]=(0,u.Lk)("label",null,"分析说明：",-1)),(0,u.Lk)("div",Hr,(0,w.v_)(n.selectedSubmission.aiAnalysis),1)])])]}),_:1})):(0,u.Q3)("",!0),n.getUrlList(n.selectedSubmission).length>0?((0,u.uX)(),(0,u.Wv)(h,{key:7,class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[206]||(a[206]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-link"}),(0,u.Lk)("span",null,"相关URL")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Ir,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.getUrlList(n.selectedSubmission),function(e,t){return(0,u.uX)(),(0,u.CE)("div",{key:t,class:"url-item"},[a[208]||(a[208]=(0,u.Lk)("div",{class:"url-icon"},[(0,u.Lk)("i",{class:"el-icon-link"})],-1)),(0,u.Lk)("div",Br,[(0,u.Lk)("div",Xr,(0,w.v_)(e),1),(0,u.Lk)("div",Dr,[(0,u.bF)(o,{type:"text",size:"small",onClick:function(a){return n.copyToClipboard(e)}},{default:(0,u.k6)(function(){return a[207]||(a[207]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1),(0,u.eW)(" 复制 ")])}),_:2,__:[207]},1032,["onClick"])])])])}),128))])]}),_:1})):(0,u.Q3)("",!0),n.getAttachmentList(n.selectedSubmission).length>0?((0,u.uX)(),(0,u.Wv)(h,{key:8,class:"detail-card",shadow:"never"},{header:(0,u.k6)(function(){return a[209]||(a[209]=[(0,u.Lk)("div",{class:"card-header"},[(0,u.Lk)("i",{class:"el-icon-folder-opened"}),(0,u.Lk)("span",null,"附件链接")],-1)])}),default:(0,u.k6)(function(){return[(0,u.Lk)("div",Nr,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.getAttachmentList(n.selectedSubmission),function(e,t){return(0,u.uX)(),(0,u.CE)("div",{key:t,class:"attachment-item"},[a[211]||(a[211]=(0,u.Lk)("div",{class:"attachment-icon"},[(0,u.Lk)("i",{class:"el-icon-cloudy"})],-1)),(0,u.Lk)("div",Pr,[(0,u.Lk)("div",Ur,(0,w.v_)(e),1),(0,u.Lk)("div",Kr,[(0,u.bF)(o,{type:"text",size:"small",onClick:function(a){return n.copyToClipboard(e)}},{default:(0,u.k6)(function(){return a[210]||(a[210]=[(0,u.Lk)("i",{class:"el-icon-copy-document"},null,-1),(0,u.eW)(" 复制链接 ")])}),_:2,__:[210]},1032,["onClick"])])])])}),128))])]}),_:1})):(0,u.Q3)("",!0)])):(0,u.Q3)("",!0)]}),_:1},8,["modelValue","title","before-close"]),(0,u.bF)(E,{modelValue:n.taskDetailVisible,"onUpdate:modelValue":a[40]||(a[40]=function(e){return n.taskDetailVisible=e}),title:"任务详情",width:"800px","close-on-click-modal":!1},{footer:(0,u.k6)(function(){return[(0,u.Lk)("div",li,[(0,u.bF)(o,{onClick:a[39]||(a[39]=function(e){return n.taskDetailVisible=!1})},{default:(0,u.k6)(function(){return a[220]||(a[220]=[(0,u.eW)("关闭")])}),_:1,__:[220]}),(0,u.bF)(o,{type:"primary",onClick:n.goToReviewTask},{default:(0,u.k6)(function(){return a[221]||(a[221]=[(0,u.eW)("审核漏洞")])}),_:1,__:[221]},8,["onClick"])])]}),default:(0,u.k6)(function(){return[n.selectedTask?((0,u.uX)(),(0,u.CE)("div",qr,[(0,u.bF)(R,{column:2,border:""},{default:(0,u.k6)(function(){return[(0,u.bF)(V,{label:"任务标题",span:2},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.selectedTask.title),1)]}),_:1}),(0,u.bF)(V,{label:"任务描述",span:2},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Qr,(0,w.v_)(n.selectedTask.description),1)]}),_:1}),(0,u.bF)(V,{label:"预算范围"},{default:(0,u.k6)(function(){return[(0,u.eW)(" ¥"+(0,w.v_)(n.formatNumber(n.selectedTask.totalBudgetMin))+" - ¥"+(0,w.v_)(n.formatNumber(n.selectedTask.totalBudgetMax)),1)]}),_:1}),(0,u.bF)(V,{label:"任务状态"},{default:(0,u.k6)(function(){return[(0,u.bF)(c,{type:n.getTaskStatusType(n.selectedTask.status)},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.getTaskStatusText(n.selectedTask.status)),1)]}),_:1},8,["type"])]}),_:1}),(0,u.bF)(V,{label:"开始时间"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.formatDate(n.selectedTask.startDate)),1)]}),_:1}),(0,u.bF)(V,{label:"结束时间"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.formatDate(n.selectedTask.endDate)),1)]}),_:1}),(0,u.bF)(V,{label:"参与人数"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.selectedTask.participantCount||0)+"人 ",1)]}),_:1}),(0,u.bF)(V,{label:"提交数量"},{default:(0,u.k6)(function(){return[(0,u.eW)((0,w.v_)(n.selectedTask.submissionCount||0)+"个 ",1)]}),_:1})]}),_:1}),(0,u.Lk)("div",Or,[a[217]||(a[217]=(0,u.Lk)("h4",null,"漏洞奖励标准",-1)),(0,u.bF)(b,{gutter:20},{default:(0,u.k6)(function(){return[(0,u.bF)(m,{span:6},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Yr,[a[213]||(a[213]=(0,u.Lk)("div",{class:"reward-level"},"低危",-1)),(0,u.Lk)("div",$r,"¥"+(0,w.v_)(n.formatNumber(n.selectedTask.lowVulReward)),1)])]}),_:1}),(0,u.bF)(m,{span:6},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Jr,[a[214]||(a[214]=(0,u.Lk)("div",{class:"reward-level"},"中危",-1)),(0,u.Lk)("div",Gr,"¥"+(0,w.v_)(n.formatNumber(n.selectedTask.mediumVulReward)),1)])]}),_:1}),(0,u.bF)(m,{span:6},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",Zr,[a[215]||(a[215]=(0,u.Lk)("div",{class:"reward-level"},"高危",-1)),(0,u.Lk)("div",ei,"¥"+(0,w.v_)(n.formatNumber(n.selectedTask.highVulReward)),1)])]}),_:1}),(0,u.bF)(m,{span:6},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",ai,[a[216]||(a[216]=(0,u.Lk)("div",{class:"reward-level"},"严重",-1)),(0,u.Lk)("div",ti,"¥"+(0,w.v_)(n.formatNumber(n.selectedTask.criticalVulReward)),1)])]}),_:1})]}),_:1})]),(0,u.Lk)("div",ni,[a[218]||(a[218]=(0,u.Lk)("h4",null,"测试范围",-1)),(0,u.Lk)("div",ri,(0,w.v_)(n.selectedTask.scope),1)]),(0,u.Lk)("div",ii,[a[219]||(a[219]=(0,u.Lk)("h4",null,"测试规则",-1)),(0,u.Lk)("div",si,(0,w.v_)(n.selectedTask.rules),1)])])):(0,u.Q3)("",!0)]}),_:1},8,["modelValue"]),(0,u.bF)(W)])}var oi=t(88844),di=(t(28706),t(2008),t(23418),t(64346),t(48598),t(62062),t(59089),t(23288),t(62010),t(22489),t(61701),t(79432),t(78459),t(38781),t(30578));const ui={name:"UserProfile",components:{TheHeader:U.A,AppFooter:K.A},props:{activeTab:{type:String,default:"info"}},setup:function(e){var a=(0,g.rd)(),t=(0,N.KR)(null),n=(0,N.KR)(e.activeTab||"info"),r=(0,N.KR)(!1),i=(0,N.KR)(!1),s=(0,N.KR)(!1),l=(0,N.KR)(null),c=(0,N.KR)(null),o=(0,N.KR)(!1),d=(0,N.KR)(""),v="/upload/avatar",f={Authorization:"Bearer ".concat(P.A.getToken())},p=(0,N.Kh)({username:"",email:"",phone:"",realName:"",companyName:"",bio:""}),h=(0,N.KR)([]),m=(0,N.KR)(!1),w=(0,N.KR)(""),y=(0,N.KR)(!1),L=(0,N.KR)(null),F=(0,N.Kh)({total:0,confirmed:0,pending:0,rejected:0}),_=(0,N.KR)([]),C=(0,N.KR)(!1),x=(0,N.KR)([]),A=(0,N.Kh)({totalEarned:0,thisMonth:0,totalRewards:0}),S=(0,N.Kh)({currentBalance:0,totalEarnings:0}),T=(0,N.KR)(!1),z=(0,N.KR)(!1),M=(0,N.KR)(!1),E=(0,N.KR)([]),V=(0,N.KR)(null),R=(0,N.Kh)({amount:"",bankCardNumber:"",bankName:"",accountHolderName:""}),W=(0,N.KR)([]),H=(0,N.KR)(!1),I=(0,N.KR)(""),B=(0,N.KR)({page:1,limit:6,total:0,totalPages:0}),X=(0,N.KR)(!1),D=(0,N.KR)(null),U=(0,N.Kh)({currentBalance:0,totalSpent:0}),K=(0,N.KR)([]),j=(0,N.KR)(!1),q=(0,N.KR)(!1),Q=(0,N.KR)(null),O=(0,N.KR)(!1),Y=(0,N.KR)(null),$=(0,N.Kh)({amount:1e3,paymentMethod:"alipay"}),J=(0,N.Kh)({amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{type:"number",min:100,message:"最低充值金额为100元",trigger:"blur"}]}),G=(0,N.Kh)({currentPassword:"",newPassword:"",confirmPassword:""}),Z=(0,N.Kh)({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}),ee=function(e,a,t){""===a?t(new Error("请输入新密码")):(""!==G.confirmPassword&&l.value.validateField("confirmPassword"),t())},ne=function(e,a,t){""===a?t(new Error("请再次输入新密码")):a!==G.newPassword?t(new Error("两次输入密码不一致")):t()},re={currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"},{validator:ee,trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:ne,trigger:"blur"}]},ie={amount:[{required:!0,message:"请输入提现金额",trigger:"blur"},{validator:function(e,a,t){!a||a<=0?t(new Error("提现金额必须大于0")):parseFloat(a)>S.currentBalance?t(new Error("提现金额不能超过当前余额")):t()},trigger:"blur"}],bankCardNumber:[{required:!0,message:"请输入银行卡号",trigger:"blur"},{pattern:/^\d{16,19}$/,message:"请输入正确的银行卡号",trigger:"blur"}],bankName:[{required:!0,message:"请输入银行名称",trigger:"blur"}],accountHolderName:[{required:!0,message:"请输入开户人姓名",trigger:"blur"}]},se=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(!P.A.isLoggedIn()){e.n=5;break}return t.value=P.A.getCurrentUser(),t.value&&Object.assign(p,{username:t.value.username||"",email:t.value.email||"",phone:t.value.phone||"",realName:t.value.realName||"",companyName:t.value.companyName||"",bio:t.value.bio||""}),e.p=1,e.n=2,P.A.validateLoginStatus();case 2:a=e.v,a||(t.value=null),e.n=4;break;case 3:e.p=3,n=e.v,console.error("验证登录状态失败:",n),t.value=null;case 4:e.n=6;break;case 5:t.value=null;case 6:return e.a(2)}},e,null,[[1,3]])}));return function(){return e.apply(this,arguments)}}(),le=function(){s.value=!0},ce=function(){s.value=!1,t.value&&Object.assign(p,{username:t.value.username||"",email:t.value.email||"",phone:t.value.phone||"",realName:t.value.realName||"",companyName:t.value.companyName||"",bio:t.value.bio||""})},oe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(c.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,c.value.validate();case 2:return i.value=!0,e.n=3,te.A.put("/auth/profile",p);case 3:a=e.v,a.data.success?(Object.assign(t.value,p),P.A.saveLoginInfo(P.A.getToken(),t.value,!!localStorage.getItem("token")),s.value=!1,ae.nk.success("个人信息更新成功")):ae.nk.error(a.data.message||"更新失败"),e.n=5;break;case 4:e.p=4,n=e.v,console.error("更新用户信息失败:",n),ae.nk.error("更新失败，请稍后重试");case 5:return e.p=5,i.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),de=function(e){var a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type,t=e.size/1024<500;return a?t?(o.value=!0,!0):(ae.nk.error("头像大小不能超过 500KB!"),!1):(ae.nk.error("头像只能是 JPG/PNG 格式!"),!1)},ue=function(e){o.value=!1,e.success?(d.value=e.data.url,t.value.avatar=e.data.url,P.A.saveLoginInfo(P.A.getToken(),t.value,!!localStorage.getItem("token")),ae.nk.success("头像上传成功")):ae.nk.error(e.message||"头像上传失败")},ve=function(e){o.value=!1,console.error("头像上传失败:",e),ae.nk.error("头像上传失败，请稍后重试")},fe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(l.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,l.value.validate();case 2:return r.value=!0,e.n=3,te.A.put("/auth/change-password",{currentPassword:G.currentPassword,newPassword:G.newPassword});case 3:a=e.v,a.data.success?(ae.nk.success("密码修改成功"),G.currentPassword="",G.newPassword="",G.confirmPassword=""):ae.nk.error(a.data.message||"密码修改失败"),e.n=5;break;case 4:e.p=4,t=e.v,console.error("修改密码失败:",t),t.response&&t.response.data&&t.response.data.message?ae.nk.error(t.response.data.message):ae.nk.error("密码修改失败，请稍后重试");case 5:return e.p=5,r.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),pe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return m.value=!0,e.p=1,a={},w.value&&(a.status=w.value),e.n=2,te.A.get("/vulnerabilities/my-submissions",{params:a});case 2:t=e.v,t.data.success?(h.value=t.data.data,F.total=t.data.stats.total,F.confirmed=t.data.stats.confirmed,F.pending=t.data.stats.pending,F.rejected=t.data.stats.rejected):ae.nk.error(t.data.message||"获取提交记录失败"),e.n=4;break;case 3:e.p=3,n=e.v,console.error("获取提交记录失败:",n),ae.nk.error("获取提交记录失败");case 4:return e.p=4,m.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),he=function(e){L.value=e,y.value=!0},me=function(){var e=(0,k.A)((0,b.A)().m(function e(a){var t,n,r,i,s;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,t="enterprise_confirmed"===a.status,n='确认接受企业对漏洞"'.concat(a.title,t?'"的审核结果吗？确认后将发放奖励并生成证书。':'"的拒绝结果吗？确认后该漏洞将被标记为已处理。'),e.n=1,di.s.confirm(n,t?"确认漏洞":"接受拒绝",{confirmButtonText:t?"确认":"接受",cancelButtonText:"取消",type:t?"success":"warning"});case 1:return e.n=2,te.A.post("/vulnerability-workflow/user-confirm/".concat(a.id),{action:"confirm"});case 2:r=e.v,r.data.success?(i=r.data.message,r.data.data.blockchain&&(i+="\n区块链交易哈希: ".concat(r.data.data.blockchain.transactionHash)),ae.nk.success(i),pe()):ae.nk.error(r.data.message||"操作失败"),e.n=4;break;case 3:e.p=3,s=e.v,"cancel"!==s&&(console.error("确认漏洞失败:",s),ae.nk.error("操作失败，请稍后重试"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(a){return e.apply(this,arguments)}}(),be=function(){var e=(0,k.A)((0,b.A)().m(function e(a){var t,n,r,i,s;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,di.s.prompt('请说明对漏洞"'.concat(a.title,'"的争议理由：'),"申诉漏洞",{confirmButtonText:"提交申诉",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请详细说明争议理由...",inputValidator:function(e){return!(!e||e.trim().length<10)||"争议理由不能少于10个字符"}});case 1:return t=e.v,n=t.value,e.n=2,te.A.post("/vulnerability-workflow/user-confirm/".concat(a.id),{action:"dispute",disputeReason:n});case 2:r=e.v,r.data.success?(i=r.data.message,r.data.data.blockchain&&(i+="\n区块链交易哈希: ".concat(r.data.data.blockchain.transactionHash)),ae.nk.success(i),pe()):ae.nk.error(r.data.message||"申诉提交失败"),e.n=4;break;case 3:e.p=3,s=e.v,"cancel"!==s&&(console.error("申诉漏洞失败:",s),ae.nk.error("申诉提交失败，请稍后重试"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(a){return e.apply(this,arguments)}}(),ke=function(){y.value=!1,L.value=null},ge=function(e){if(!e||!e.urls)return[];try{var a="string"===typeof e.urls?JSON.parse(e.urls):e.urls;return Array.isArray(a)?a.filter(function(e){return e&&e.trim()}):[]}catch(t){return[]}},we=function(e){if(!e||!e.attachments)return[];try{var a="string"===typeof e.attachments?JSON.parse(e.attachments):e.attachments;return Array.isArray(a)?a.filter(function(e){return e&&e.trim()}):[]}catch(t){return[]}},ye=function(e){return!!e&&!(!e.confirmationTransactionHash&&!e.disputeTransactionHash)},Le=function(e){return!!e&&!!e.submissionTransactionHash},Fe=function(e){return!!e&&!!e.approvalTransactionHash},_e=function(e){return!!e&&!!e.adminReviewTransactionHash},Ce=function(e){return e&&e.status?"enterprise_confirmed"===e.status?"success":"enterprise_rejected"===e.status?"danger":"info":""},xe=function(e){return e&&e.status?"enterprise_confirmed"===e.status?"已确认":"enterprise_rejected"===e.status?"已拒绝":"待审批":"未审批"},Ae=function(e){return e&&e.status?"admin_confirmed"===e.status?"success":"admin_rejected"===e.status?"danger":"info":""},Se=function(e){return e&&e.status?"admin_confirmed"===e.status?"已通过":"admin_rejected"===e.status?"已拒绝":"待审核":"未审核"},Te=function(e){if(!e)return"未知";try{var a=new Date(1e3*e);return a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(t){return"格式错误"}},ze=function(){var e=(0,k.A)((0,b.A)().m(function e(a){var t,n,r,i=arguments;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(t=i.length>1&&void 0!==i[1]?i[1]:"",e.p=1,!navigator.clipboard||!window.isSecureContext){e.n=3;break}return e.n=2,navigator.clipboard.writeText(a);case 2:ae.nk.success(t?"".concat(t,"已复制到剪贴板"):"已复制到剪贴板"),e.n=4;break;case 3:n=document.createElement("textarea"),n.value=a,n.style.position="fixed",n.style.left="-999999px",n.style.top="-999999px",document.body.appendChild(n),n.focus(),n.select(),r=document.execCommand("copy"),document.body.removeChild(n),r?ae.nk.success(t?"".concat(t,"已复制到剪贴板"):"已复制到剪贴板"):ae.nk.error("复制失败，请手动复制");case 4:e.n=6;break;case 5:e.p=5,e.v,ae.nk.error("复制失败，请手动复制");case 6:return e.a(2)}},e,null,[[1,5]])}));return function(a){return e.apply(this,arguments)}}(),Me=function(e){var a={critical:"danger",high:"danger",medium:"warning",low:"info",info:"success"};return a[e]||"info"},Ee=function(e){var a={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"提示"};return a[e]||"未知"},Ve=function(e){var a=document.createElement("a");a.href=e.url,a.download=e.name,document.body.appendChild(a),a.click(),document.body.removeChild(a)},Re=function(e){var a={sql_injection:"SQL注入",xss:"XSS跨站脚本",csrf:"CSRF跨站请求伪造",file_upload:"文件上传漏洞",auth_bypass:"身份认证绕过",privilege_escalation:"权限提升",info_disclosure:"信息泄露",rce:"远程代码执行",other:"其他"};return a[e]||e},We=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return C.value=!0,e.p=1,e.n=2,Xe();case 2:return e.n=3,te.A.get("/rewards/my-rewards");case 3:a=e.v,a.data.success?(_.value=a.data.data.rewards||[],A.totalEarned=S.totalEarnings,A.thisMonth=a.data.data.thisMonth||0,A.totalRewards=_.value.length):(A.totalEarned=S.totalEarnings,A.thisMonth=0,A.totalRewards=0),e.n=5;break;case 4:e.p=4,t=e.v,console.error("获取奖励记录失败:",t),A.totalEarned=S.totalEarnings,A.thisMonth=0,A.totalRewards=0,ae.nk.error("获取奖励记录失败");case 5:return e.p=5,C.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),He=function(){pe()},Ie=function(){We()},Be=function(){We()},Xe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,te.A.get("/withdrawals/balance");case 1:a=e.v,a.data.success&&(S.currentBalance=a.data.data.currentBalance,S.totalEarnings=a.data.data.totalEarnings),e.n=3;break;case 2:e.p=2,t=e.v,console.error("获取余额信息失败:",t);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),De=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(V.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,V.value.validate();case 2:return z.value=!0,e.n=3,te.A.post("/withdrawals/apply",R);case 3:a=e.v,a.data.success?(ae.nk.success("提现申请提交成功"),Ne(),Xe(),Pe()):ae.nk.error(a.data.message||"提现申请失败"),e.n=5;break;case 4:e.p=4,t=e.v,console.error("提现申请失败:",t),t.response&&t.response.data&&t.response.data.message?ae.nk.error(t.response.data.message):ae.nk.error("提现申请失败，请稍后重试");case 5:return e.p=5,z.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),Ne=function(){R.amount="",R.bankCardNumber="",R.bankName="",R.accountHolderName="",V.value&&V.value.resetFields()},Pe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,M.value=!0,e.n=1,te.A.get("/withdrawals/my-withdrawals");case 1:a=e.v,a.data.success&&(E.value=a.data.data),e.n=3;break;case 2:e.p=2,t=e.v,console.error("获取提现记录失败:",t),ae.nk.error("获取提现记录失败");case 3:return e.p=3,M.value=!1,e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),Ue=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,H.value=!0,a={page:B.value.page,limit:B.value.limit},I.value&&(a.severity=I.value),e.n=1,te.A.get("/certificates/my-certificates",{params:a});case 1:t=e.v,t.data.success&&(W.value=t.data.data,t.data.pagination&&(B.value=(0,oi.A)((0,oi.A)({},B.value),t.data.pagination))),e.n=3;break;case 2:e.p=2,n=e.v,console.error("获取证书列表失败:",n),ae.nk.error("获取证书列表失败");case 3:return e.p=3,H.value=!1,e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),Ke=function(){B.value.page=1,Ue()},je=function(e){B.value.page=e,Ue()},qe=function(e){B.value.limit=e,B.value.page=1,Ue()},Qe=function(){var e=(0,k.A)((0,b.A)().m(function e(a){var t,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,te.A.get("/certificates/".concat(a.id));case 1:t=e.v,t.data.success?(D.value=t.data.data,X.value=!0):ae.nk.error("获取证书详情失败"),e.n=3;break;case 2:e.p=2,n=e.v,console.error("获取证书详情失败:",n),ae.nk.error("获取证书详情失败");case 3:return e.a(2)}},e,null,[[0,2]])}));return function(a){return e.apply(this,arguments)}}(),Oe=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,te.A.get("/withdrawals/balance");case 1:a=e.v,a.data.success&&(U.currentBalance=a.data.data.currentBalance,U.totalSpent=0),e.n=3;break;case 2:e.p=2,t=e.v,console.error("获取账户余额失败:",t);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),Ye=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,j.value=!0,e.n=1,te.A.get("/security-tasks/my/tasks");case 1:a=e.v,a.data.success&&(K.value=a.data.data.tasks),e.n=3;break;case 2:e.p=2,t=e.v,console.error("获取我的任务失败:",t),ae.nk.error("获取任务列表失败");case 3:return e.p=3,j.value=!1,e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),$e=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(Y.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,Y.value.validate();case 2:return O.value=!0,e.n=3,te.A.post("/recharge/create-order",{amount:$.amount,paymentMethod:$.paymentMethod});case 3:a=e.v,a.data.success?(U.currentBalance=a.data.data.newBalance,ae.nk.success("充值成功！已为您的账户充值 ¥".concat($.amount)),$.amount=1e3,Oe()):ae.nk.error(a.data.message||"充值失败"),e.n=5;break;case 4:e.p=4,t=e.v,console.error("充值失败:",t),t.response&&t.response.data?ae.nk.error(t.response.data.message||"充值失败，请稍后重试"):ae.nk.error("充值失败，请稍后重试");case 5:return e.p=5,O.value=!1,e.f(5);case 6:return e.a(2)}},e,null,[[1,4,5,6]])}));return function(){return e.apply(this,arguments)}}(),Je=function(e){Q.value=e,q.value=!0},Ge=function(){q.value=!1,a.push("/enterprise/review")},Ze=function(e){var a={draft:"info",published:"success",in_progress:"warning",completed:"",cancelled:"danger"};return a[e]||""},ea=function(e){var a={draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已完成",cancelled:"已取消"};return a[e]||e},aa=function(e){var a={pending:"",processing:"warning",completed:"success",failed:"danger",cancelled:"info"};return a[e]||""},ta=function(e){var a={pending:"待处理",processing:"处理中",completed:"已完成",failed:"失败",cancelled:"已取消"};return a[e]||e},na=function(){var e=Date.now().toString(16),a=Math.random().toString(36).substring(2,15);return"0x".concat(e).concat(a).toLowerCase()},ra=function(e){var a={pending:"warning",confirmed:"success",rejected:"danger",fixed:"info",duplicate:"info"};return a[e]||""},ia=function(e){var a={pending:"待审核",confirmed:"已确认",rejected:"已拒绝",fixed:"已修复",duplicate:"重复"};return a[e]||e},sa=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t,n,r,i;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(D.value){e.n=1;break}return e.a(2);case 1:if(e.p=1,ae.nk.info("正在生成PDF证书，请稍候..."),"undefined"!==typeof window){e.n=2;break}return ae.nk.error("当前环境不支持下载功能"),e.a(2);case 2:if(a=document.getElementById("certificate-content"),a){e.n=3;break}return ae.nk.error("证书内容未找到"),e.a(2);case 3:if(t=window.open("","_blank"),t){e.n=4;break}return ae.nk.error("无法打开打印窗口，请检查浏览器设置"),e.a(2);case 4:n=a.outerHTML,r=Array.from(document.styleSheets).map(function(e){try{return Array.from(e.cssRules).map(function(e){return e.cssText}).join("\n")}catch(a){return""}}).join("\n"),t.document.open(),t.document.write("<!DOCTYPE html>"),t.document.write("<html><head><title>安全漏洞发现证书</title>"),t.document.write('<meta charset="UTF-8">'),t.document.write("<style>"),t.document.write(r),t.document.write("\n          body {\n            margin: 0;\n            padding: 20px;\n            font-family: 'Microsoft YaHei', serif;\n            background: white;\n          }\n          .certificate-paper {\n            box-shadow: none !important;\n            background: white !important;\n            min-height: auto !important;\n          }\n          .certificate-border {\n            border: 8px solid #d4af37 !important;\n            padding: 40px !important;\n            background: white !important;\n          }\n          .logo-circle {\n            width: 60px !important;\n            height: 60px !important;\n            background: #d4af37 !important;\n            border-radius: 50% !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            margin: 0 auto 20px !important;\n          }\n          .logo-circle svg {\n            width: 40px !important;\n            height: 40px !important;\n            fill: white !important;\n          }\n          .certificate-logo {\n            text-align: center !important;\n            margin-bottom: 20px !important;\n          }\n          @media print {\n            body {\n              margin: 0 !important;\n              padding: 0 !important;\n            }\n            .certificate-paper {\n              width: 100% !important;\n              height: auto !important;\n              box-shadow: none !important;\n              page-break-inside: avoid !important;\n            }\n            .certificate-border {\n              page-break-inside: avoid !important;\n            }\n            .logo-circle {\n              -webkit-print-color-adjust: exact !important;\n              color-adjust: exact !important;\n            }\n            .logo-circle svg {\n              -webkit-print-color-adjust: exact !important;\n              color-adjust: exact !important;\n            }\n          }\n        "),t.document.write("</style>"),t.document.write("</head><body>"),t.document.write(n),t.document.write("</body></html>"),t.document.close(),setTimeout(function(){t.print(),setTimeout(function(){t.close()},2e3)},1e3),ae.nk.success('请在打印对话框中选择"另存为PDF"，或选择"Microsoft Print to PDF"'),e.n=6;break;case 5:e.p=5,i=e.v,console.error("下载证书失败:",i),ae.nk.error("证书下载失败，请重试");case 6:return e.a(2)}},e,null,[[1,5]])}));return function(){return e.apply(this,arguments)}}();(0,u.wB)(function(){return e.activeTab},function(e){n.value=e||"info"}),(0,u.wB)(n,function(e){"submissions"===e?pe():"rewards"===e?We():"withdrawal"===e?(Xe(),Pe()):"certificates"===e?Ue():"account"===e&&(Oe(),Ye())}),(0,u.sV)(function(){se(),"submissions"===e.activeTab?pe():"rewards"===e.activeTab?We():"withdrawal"===e.activeTab?(Xe(),Pe()):"certificates"===e.activeTab?Ue():"account"===e.activeTab&&(Oe(),Ye())});var la=function(e){return e?new Date(e).toLocaleDateString("zh-CN"):""},ca=function(e){return e?new Date(e).toLocaleString("zh-CN"):""},oa=function(e){return e?e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):"0"},da=function(e){var a={critical:"danger",high:"danger",medium:"warning",low:"info",info:"info"};return a[e]||"info"},ua=function(e){var a={critical:"严重",high:"高危",medium:"中危",low:"低危",info:"提示"};return a[e]||e},va=function(e){var a={pending:"warning",enterprise_confirmed:"primary",enterprise_rejected:"danger",user_confirmed:"success",dispute:"warning",admin_confirmed:"success",admin_rejected:"danger",auto_confirmed:"success",fixed:"info",duplicate:"info"};return a[e]||"info"},fa=function(e){var a={pending:"待企业审核",enterprise_confirmed:"待用户确认",enterprise_rejected:"企业已拒绝",user_confirmed:"用户已确认",dispute:"争议中",admin_confirmed:"管理员确认",admin_rejected:"管理员拒绝",auto_confirmed:"自动确认",fixed:"已修复",duplicate:"重复漏洞"};return a[e]||e},pa=function(e){var a={vulnerability:"success",bonus:"warning",activity:"info"};return a[e]||"info"},ha=function(e){var a={vulnerability:"漏洞奖励",bonus:"额外奖励",activity:"活动奖励"};return a[e]||e},ma=function(e){var a={pending:"warning",paid:"success",failed:"danger"};return a[e]||"info"},ba=function(e){var a={pending:"待发放",paid:"已发放",failed:"发放失败"};return a[e]||e},ka=function(e){di.s.alert("奖励金额：¥".concat(oa(e.amount),"\n奖励类型：").concat(ha(e.type),"\n发放状态：").concat(ba(e.status)),"奖励详情",{confirmButtonText:"确定"})};return{user:t,currentTab:n,loading:r,saving:i,editMode:s,editForm:p,userRules:Z,userFormRef:c,enableEditMode:le,cancelEdit:ce,saveUserInfo:oe,uploadLoading:o,avatarUrl:d,uploadUrl:v,uploadHeaders:f,beforeAvatarUpload:de,handleAvatarSuccess:ue,handleAvatarError:ve,passwordForm:G,passwordRules:re,passwordFormRef:l,changePassword:fe,submissions:h,loadingSubmissions:m,submissionFilter:w,submissionStats:F,fetchSubmissions:pe,refreshSubmissions:He,rewards:_,loadingRewards:C,rewardDateRange:x,rewardStats:A,fetchRewards:We,refreshRewards:Ie,filterRewards:Be,formatDate:la,formatDateTime:ca,formatNumber:oa,getSeverityType:da,getSeverityText:ua,getStatusType:va,getStatusText:fa,getRewardType:pa,getRewardTypeText:ha,getPaymentStatusType:ma,getPaymentStatusText:ba,viewSubmissionDetail:he,confirmVulnerability:me,disputeVulnerability:be,viewRewardDetail:ka,detailDialogVisible:y,selectedSubmission:L,closeDetailDialog:ke,downloadAttachment:Ve,getVulnTypeLabel:Re,getUrlList:ge,getAttachmentList:we,hasBlockchainInfo:ye,hasSubmissionBlockchainInfo:Le,hasApprovalBlockchainInfo:Fe,hasAdminReviewBlockchainInfo:_e,getApprovalStatusType:Ce,getApprovalStatusText:xe,getAdminReviewStatusType:Ae,getAdminReviewStatusText:Se,formatTimestamp:Te,copyToClipboard:ze,getAISeverityType:Me,getAISeverityText:Ee,balanceInfo:S,showWithdrawalHistory:T,loadingWithdrawal:z,loadingWithdrawalHistory:M,withdrawalHistory:E,withdrawalFormRef:V,withdrawalForm:R,withdrawalRules:ie,submitWithdrawal:De,resetWithdrawalForm:Ne,fetchWithdrawalHistory:Pe,getWithdrawalStatusType:aa,getWithdrawalStatusText:ta,certificates:W,loadingCertificates:H,certificateFilter:I,certificatePagination:B,fetchCertificates:Ue,handleFilterChange:Ke,handleCertificatePageChange:je,handleCertificatePageSizeChange:qe,viewCertificateDetail:Qe,showCertificateDetail:X,selectedCertificate:D,getVulnStatusType:ra,getVulnStatusText:ia,downloadCertificate:sa,generateBlockchainHash:na,accountBalance:U,myTasks:K,loadingMyTasks:j,rechargingLoading:O,rechargeFormRef:Y,rechargeForm:$,rechargeRules:J,fetchAccountBalance:Oe,fetchMyTasks:Ye,submitRecharge:$e,viewTaskDetail:Je,goToReviewTask:Ge,taskDetailVisible:q,selectedTask:Q,getTaskStatusType:Ze,getTaskStatusText:ea}}},vi=(0,p.A)(ui,[["render",ci],["__scopeId","data-v-********"]]),fi=vi;var pi={class:"projects-container"},hi={class:"search-section"},mi={class:"container"},bi={class:"search-container"},ki={class:"search-box"},gi={class:"filter-box"},wi={class:"projects-section"},yi={class:"container"},Li={class:"projects-header"},Fi={class:"view-options"},_i={key:0,class:"projects-grid"},Ci={style:{display:"flex","align-items":"center"}},xi={class:"project-header"},Ai={class:"project-content"},Si={class:"project-title"},Ti={class:"project-info"},zi={class:"company-name"},Mi={class:"project-desc"},Ei={class:"project-stats"},Vi={class:"stat-item"},Ri={key:0},Wi={key:1},Hi={key:2},Ii={class:"stat-item"},Bi={class:"stat-item"},Xi={class:"stat-item"},Di={key:0,class:"stat-item blockchain-info"},Ni={class:"project-footer"},Pi={key:1,class:"projects-list"},Ui={class:"table-project-info"},Ki={class:"table-project-details"},ji={class:"table-project-title"},qi={class:"table-company-name"},Qi={key:0},Oi={key:1},Yi={key:2},$i={key:1,class:"no-blockchain"},Ji={class:"pagination-container"};function Gi(e,a,t,n,r,i){var s=(0,u.g2)("TheHeader"),l=(0,u.g2)("el-button"),c=(0,u.g2)("el-input"),o=(0,u.g2)("el-option"),d=(0,u.g2)("el-select"),v=(0,u.g2)("el-radio-button"),f=(0,u.g2)("el-radio-group"),p=(0,u.g2)("el-empty"),h=(0,u.g2)("el-skeleton-item"),m=(0,u.g2)("el-card"),b=(0,u.g2)("el-skeleton"),k=(0,u.g2)("el-tooltip"),g=(0,u.g2)("el-table-column"),y=(0,u.g2)("el-table"),L=(0,u.g2)("el-pagination"),F=(0,u.g2)("AppFooter"),_=(0,u.gN)("loading");return(0,u.uX)(),(0,u.CE)("div",pi,[(0,u.bF)(s),(0,u.Lk)("section",hi,[(0,u.Lk)("div",mi,[(0,u.Lk)("div",bi,[(0,u.Lk)("div",ki,[(0,u.bF)(c,{modelValue:n.searchQuery,"onUpdate:modelValue":a[0]||(a[0]=function(e){return n.searchQuery=e}),placeholder:"搜索项目名称、企业名称或关键词","prefix-icon":"el-icon-search",clearable:"",onInput:n.handleSearch},{append:(0,u.k6)(function(){return[(0,u.bF)(l,{type:"primary",onClick:n.handleSearch},{default:(0,u.k6)(function(){return a[9]||(a[9]=[(0,u.eW)("搜索")])}),_:1,__:[9]},8,["onClick"])]}),_:1},8,["modelValue","onInput"])]),(0,u.Lk)("div",gi,[(0,u.bF)(d,{modelValue:n.filters.industry,"onUpdate:modelValue":a[1]||(a[1]=function(e){return n.filters.industry=e}),placeholder:"行业分类",clearable:""},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{label:"互联网",value:"internet"}),(0,u.bF)(o,{label:"金融",value:"finance"}),(0,u.bF)(o,{label:"教育",value:"education"}),(0,u.bF)(o,{label:"政府",value:"government"}),(0,u.bF)(o,{label:"医疗",value:"healthcare"}),(0,u.bF)(o,{label:"制造业",value:"manufacturing"}),(0,u.bF)(o,{label:"其他",value:"other"})]}),_:1},8,["modelValue"]),(0,u.bF)(d,{modelValue:n.filters.reward,"onUpdate:modelValue":a[2]||(a[2]=function(e){return n.filters.reward=e}),placeholder:"预算范围",clearable:""},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{label:"1000以下",value:"0-1000"}),(0,u.bF)(o,{label:"1000-5000",value:"1000-5000"}),(0,u.bF)(o,{label:"5000-10000",value:"5000-10000"}),(0,u.bF)(o,{label:"10000以上",value:"10000+"})]}),_:1},8,["modelValue"]),(0,u.bF)(d,{modelValue:n.filters.status,"onUpdate:modelValue":a[3]||(a[3]=function(e){return n.filters.status=e}),placeholder:"项目状态",clearable:""},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{label:"进行中",value:"active"}),(0,u.bF)(o,{label:"即将开始",value:"upcoming"}),(0,u.bF)(o,{label:"已结束",value:"ended"})]}),_:1},8,["modelValue"]),(0,u.bF)(d,{modelValue:n.filters.tags,"onUpdate:modelValue":a[4]||(a[4]=function(e){return n.filters.tags=e}),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"安全领域",clearable:""},{default:(0,u.k6)(function(){return[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.tagOptions,function(e){return(0,u.uX)(),(0,u.Wv)(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])}),128))]}),_:1},8,["modelValue"])])])])]),(0,u.Lk)("section",wi,[(0,u.Lk)("div",yi,[(0,u.Lk)("div",Li,[a[12]||(a[12]=(0,u.Lk)("h2",{class:"section-title"},"项目列表",-1)),(0,u.Lk)("div",Fi,[(0,u.bF)(f,{modelValue:n.viewMode,"onUpdate:modelValue":a[5]||(a[5]=function(e){return n.viewMode=e}),size:"small"},{default:(0,u.k6)(function(){return[(0,u.bF)(v,{label:"card"},{default:(0,u.k6)(function(){return a[10]||(a[10]=[(0,u.eW)("卡片视图")])}),_:1,__:[10]}),(0,u.bF)(v,{label:"list"},{default:(0,u.k6)(function(){return a[11]||(a[11]=[(0,u.eW)("列表视图")])}),_:1,__:[11]})]}),_:1},8,["modelValue"]),(0,u.bF)(d,{modelValue:n.sortBy,"onUpdate:modelValue":a[6]||(a[6]=function(e){return n.sortBy=e}),placeholder:"排序方式",size:"small"},{default:(0,u.k6)(function(){return[(0,u.bF)(o,{label:"最新发布",value:"latest"}),(0,u.bF)(o,{label:"预算最高",value:"reward"}),(0,u.bF)(o,{label:"参与人数",value:"participants"})]}),_:1},8,["modelValue"])])]),"card"===n.viewMode?((0,u.uX)(),(0,u.CE)("div",_i,[n.loading||0!==n.projects.length?n.loading?((0,u.uX)(),(0,u.Wv)(b,{key:1,rows:3,animated:"",loading:n.loading,count:8},{template:(0,u.k6)(function(){return[(0,u.bF)(m,{class:"project-card"},{header:(0,u.k6)(function(){return[(0,u.Lk)("div",Ci,[(0,u.bF)(h,{variant:"image",style:{width:"80px",height:"80px","margin-right":"16px"}}),(0,u.bF)(h,{variant:"text",style:{width:"60%"}})])]}),default:(0,u.k6)(function(){return[(0,u.bF)(h,{variant:"p",style:{width:"100%"}}),(0,u.bF)(h,{variant:"text",style:{width:"80%"}}),(0,u.bF)(h,{variant:"text",style:{width:"60%"}})]}),_:1})]}),_:1},8,["loading"])):((0,u.uX)(!0),(0,u.CE)(u.FK,{key:2},(0,u.pI)(n.projects,function(e){return(0,u.uX)(),(0,u.Wv)(m,{key:e.id,class:"project-card","body-style":{padding:"0px"}},{default:(0,u.k6)(function(){return[(0,u.Lk)("div",xi,[(0,u.Lk)("img",{src:"/assets/logo.png",class:"company-logo",onError:a[7]||(a[7]=function(){return n.handleImageError&&n.handleImageError.apply(n,arguments)})},null,32)]),(0,u.Lk)("div",Ai,[(0,u.Lk)("h3",Si,(0,w.v_)(e.title),1),(0,u.Lk)("div",Ti,[(0,u.Lk)("span",zi,(0,w.v_)(e.companyName),1)]),(0,u.Lk)("p",Mi,(0,w.v_)(e.description),1),(0,u.Lk)("div",Ei,[(0,u.Lk)("div",Vi,[a[13]||(a[13]=(0,u.Lk)("i",{class:"el-icon-money"},null,-1)),e.reward?((0,u.uX)(),(0,u.CE)("span",Ri,"预算范围：¥"+(0,w.v_)(n.formatNumber(e.reward)),1)):e.rewards?((0,u.uX)(),(0,u.CE)("span",Wi,"预算范围：¥"+(0,w.v_)(n.formatNumber(e.rewards.high)),1)):((0,u.uX)(),(0,u.CE)("span",Hi,"预算范围：¥0"))]),(0,u.Lk)("div",Ii,[a[14]||(a[14]=(0,u.Lk)("i",{class:"el-icon-user"},null,-1)),(0,u.Lk)("span",null,"参与："+(0,w.v_)(e.participants||e.participantCount||0)+"人",1)]),(0,u.Lk)("div",Bi,[a[15]||(a[15]=(0,u.Lk)("i",{class:"el-icon-document"},null,-1)),(0,u.Lk)("span",null,"提交："+(0,w.v_)(e.submissionCount||e.submissions_count||0)+"次",1)]),(0,u.Lk)("div",Xi,[a[16]||(a[16]=(0,u.Lk)("i",{class:"el-icon-time"},null,-1)),(0,u.Lk)("span",null,"结束时间："+(0,w.v_)(n.formatDate(e.endTime)),1)]),e.transactionHash?((0,u.uX)(),(0,u.CE)("div",Di,[a[18]||(a[18]=(0,u.Lk)("i",{class:"el-icon-link"},null,-1)),a[19]||(a[19]=(0,u.Lk)("span",{class:"blockchain-text"},"已上链",-1)),(0,u.bF)(k,{content:"此任务已记录在区块链上，具有不可篡改性",placement:"top"},{default:(0,u.k6)(function(){return a[17]||(a[17]=[(0,u.Lk)("i",{class:"el-icon-info blockchain-icon"},null,-1)])}),_:1,__:[17]})])):(0,u.Q3)("",!0)])]),(0,u.Lk)("div",Ni,[(0,u.bF)(l,{type:"primary",onClick:function(a){return n.viewProjectDetail(e.id)}},{default:(0,u.k6)(function(){return a[20]||(a[20]=[(0,u.eW)("查看详情")])}),_:2,__:[20]},1032,["onClick"])])]}),_:2},1024)}),128)):((0,u.uX)(),(0,u.Wv)(p,{key:0,description:"暂无项目"}))])):((0,u.uX)(),(0,u.CE)("div",Pi,[(0,u.bo)(((0,u.uX)(),(0,u.Wv)(y,{data:n.projects,style:{width:"100%"},"empty-text":n.loading?"加载中...":"暂无数据"},{default:(0,u.k6)(function(){return[(0,u.bF)(g,{prop:"title",label:"项目名称","min-width":"200"},{default:(0,u.k6)(function(e){return[(0,u.Lk)("div",Ui,[(0,u.Lk)("img",{src:"/assets/logo.png",class:"table-company-logo",onError:a[8]||(a[8]=function(){return n.handleImageError&&n.handleImageError.apply(n,arguments)})},null,32),(0,u.Lk)("div",Ki,[(0,u.Lk)("span",ji,(0,w.v_)(e.row.title),1),(0,u.Lk)("span",qi,(0,w.v_)(e.row.companyName),1)])])]}),_:1}),(0,u.bF)(g,{prop:"reward",label:"预算范围",width:"120"},{default:(0,u.k6)(function(e){return[e.row.reward?((0,u.uX)(),(0,u.CE)("span",Qi,"¥"+(0,w.v_)(n.formatNumber(e.row.reward)),1)):e.row.rewards?((0,u.uX)(),(0,u.CE)("span",Oi,"¥"+(0,w.v_)(n.formatNumber(e.row.rewards.high)),1)):((0,u.uX)(),(0,u.CE)("span",Yi,"¥0"))]}),_:1}),(0,u.bF)(g,{label:"参与人数",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(e.row.participants||e.row.participantCount||0),1)]}),_:1}),(0,u.bF)(g,{label:"提交次数",width:"100"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(e.row.submissionCount||e.row.submissions_count||0),1)]}),_:1}),(0,u.bF)(g,{prop:"endTime",label:"结束时间",width:"120"},{default:(0,u.k6)(function(e){return[(0,u.eW)((0,w.v_)(n.formatDate(e.row.endTime)),1)]}),_:1}),(0,u.bF)(g,{label:"区块链",width:"80",align:"center"},{default:(0,u.k6)(function(e){return[e.row.transactionHash?((0,u.uX)(),(0,u.Wv)(k,{key:0,content:"此任务已记录在区块链上",placement:"top"},{default:(0,u.k6)(function(){return a[21]||(a[21]=[(0,u.Lk)("i",{class:"el-icon-link blockchain-icon-table"},null,-1)])}),_:1,__:[21]})):((0,u.uX)(),(0,u.CE)("span",$i,"-"))]}),_:1}),(0,u.bF)(g,{label:"操作",width:"120",fixed:"right"},{default:(0,u.k6)(function(e){return[(0,u.bF)(l,{size:"small",type:"primary",onClick:function(a){return n.viewProjectDetail(e.row.id)}},{default:(0,u.k6)(function(){return a[22]||(a[22]=[(0,u.eW)("查看详情")])}),_:2,__:[22]},1032,["onClick"])]}),_:1})]}),_:1},8,["data","empty-text"])),[[_,n.loading]])])),(0,u.Lk)("div",Ji,[(0,u.bF)(L,{background:"",layout:"prev, pager, next",total:n.totalProjects,"page-size":n.pageSize,"current-page":n.currentPage,onCurrentChange:n.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])])])]),(0,u.bF)(F)])}t(50113),t(20116),t(68156);const Zi={name:"Projects",components:{TheHeader:U.A,AppFooter:K.A},setup:function(){var e=(0,g.rd)(),a=(0,N.KR)(null),t=(0,N.KR)(!1),n=(0,N.KR)(null),r=(0,N.KR)(""),i=(0,N.KR)("card"),s=(0,N.KR)("latest"),l=(0,N.KR)(1),c=(0,N.KR)(12),o=(0,N.KR)(0),d=(0,N.KR)([]),v=(0,N.KR)(!1),f=(0,N.Kh)({industry:"",reward:"",status:"",tags:[]}),p=[{value:"web",label:"Web安全"},{value:"app",label:"APP安全"},{value:"iot",label:"IoT安全"},{value:"api",label:"API安全"},{value:"code",label:"代码审计"},{value:"network",label:"网络安全"},{value:"other",label:"其他"}],h=function(){var e=(0,k.A)((0,b.A)().m(function e(){var t,n;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(!P.A.isLoggedIn()){e.n=5;break}return a.value=P.A.getCurrentUser(),e.p=1,e.n=2,P.A.validateLoginStatus();case 2:t=e.v,t||(a.value=null),e.n=4;break;case 3:e.p=3,n=e.v,console.error("验证登录状态失败:",n);case 4:e.n=6;break;case 5:a.value=null;case 6:return e.a(2)}},e,null,[[1,3]])}));return function(){return e.apply(this,arguments)}}(),m=function(){t.value=!t.value},w=function(e){n.value&&!n.value.contains(e.target)&&(t.value=!1)},y=function(){var e=(0,k.A)((0,b.A)().m(function e(){var a,t;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return v.value=!0,e.p=1,e.n=2,te.A.get("/security-tasks",{params:{page:l.value,limit:c.value,search:r.value,status:"published"}});case 2:a=e.v,a.data.success?(d.value=a.data.data.projects,o.value=a.data.data.pagination.total):ae.nk.error("获取项目列表失败"),e.n=4;break;case 3:e.p=3,t=e.v,console.error("获取项目列表失败:",t),ae.nk.error("获取项目列表失败，请稍后重试");case 4:return e.p=4,v.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),L=function(e){return void 0===e||null===e||""===e?"0":e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},F=function(e){if(!e)return"";var a=new Date(e);return"".concat(a.getFullYear(),"-").concat(String(a.getMonth()+1).padStart(2,"0"),"-").concat(String(a.getDate()).padStart(2,"0"))},_=function(e){var a=new Date(e),t=new Date,n=a-t;if(n<=0)return"已结束";var r=Math.floor(n/864e5),i=Math.floor(n%864e5/36e5);return r>0?"".concat(r,"天").concat(i,"小时"):"".concat(i,"小时")},C=function(e){var a={active:"进行中",upcoming:"即将开始",ended:"已结束"};return a[e]||e},x=function(e){var a={active:"success",upcoming:"warning",ended:"info"};return a[e]||"info"},A=function(e){var a={web:"",app:"success",iot:"warning",api:"danger",code:"info",network:"warning",other:"info"};return a[e]||"info"},S=function(e){var a=p.find(function(a){return a.value===e});return a?a.label:e},T=function(a){e.push("/projects/".concat(a))},z=function(e){e.target.src="/assets/logo.png"},M=function(){var t=(0,k.A)((0,b.A)().m(function t(n){var r,i,s;return(0,b.A)().w(function(t){while(1)switch(t.n){case 0:if(a.value){t.n=1;break}return ae.nk.warning("请先登录后再收藏项目"),e.push("/login"),t.a(2);case 1:if(t.p=1,r=d.value.find(function(e){return e.id===n}),r){t.n=2;break}return t.a(2);case 2:return t.n=3,te.A.post("/projects/".concat(n,"/favorite"),{action:r.isFavorite?"remove":"add"});case 3:i=t.v,i.data.success?(r.isFavorite=!r.isFavorite,ae.nk.success(r.isFavorite?"收藏成功":"已取消收藏")):ae.nk.error(i.data.message||"操作失败"),t.n=5;break;case 4:t.p=4,s=t.v,console.error("收藏操作失败:",s),ae.nk.error("操作失败，请稍后重试");case 5:return t.a(2)}},t,null,[[1,4]])}));return function(e){return t.apply(this,arguments)}}(),E=function(e){var a={title:"".concat(e.title," - 衍盾星云安全众测平台"),text:"我在衍盾星云发现了一个有趣的项目：".concat(e.title),url:"".concat(window.location.origin,"/projects/").concat(e.id)};navigator.share?navigator.share(a).then(function(){ae.nk.success("分享成功")})["catch"](function(e){console.error("分享失败:",e),V(a.url)}):V(a.url)},V=function(e){navigator.clipboard.writeText(e).then(function(){(0,ae.nk)({message:"项目链接已复制到剪贴板",type:"success"})})["catch"](function(){ae.nk.error("复制失败，请手动复制")})},R=function(){l.value=1,y()},W=function(e){l.value=e,y()};return(0,u.wB)([s,f],function(){l.value=1,y()}),(0,u.sV)(function(){h(),document.addEventListener("click",w),y()}),(0,u.xo)(function(){document.removeEventListener("click",w)}),{currentUser:a,showDropdown:t,userDropdown:n,searchQuery:r,viewMode:i,sortBy:s,currentPage:l,pageSize:c,totalProjects:o,projects:d,loading:v,filters:f,toggleDropdown:m,formatNumber:L,formatDate:F,formatTimeLeft:_,getStatusText:C,getStatusType:x,viewProjectDetail:T,handleImageError:z,handleSearch:R,handlePageChange:W,tagOptions:p,getTagType:A,getTagLabel:S,toggleFavorite:M,shareProject:E}}},es=(0,p.A)(Zi,[["render",Gi],["__scopeId","data-v-e13a262e"]]),as=es;var ts=t(33153),ns={class:"ai-container"},rs={class:"background-animation"},is={class:"neural-network"},ss={class:"floating-particles"},ls={class:"ai-content"},cs={class:"ai-chat-container"},os={class:"ai-header"},ds={class:"header-left"},us={class:"ai-avatar-container"},vs={class:"ai-avatar"},fs={class:"ai-info"},ps={class:"ai-status-text"},hs={class:"header-right"},ms={class:"control-panel"},bs={class:"chat-messages",ref:"chatContainer"},ks={key:0,class:"message-avatar"},gs={key:1,class:"message-avatar"},ws={class:"message-content"},ys={class:"message-bubble"},Ls=["innerHTML"],Fs={class:"message-time"},_s={key:0,class:"message assistant typing"},Cs={class:"chat-input"},xs={class:"input-container"},As={class:"input-field"},Ss=["disabled"],Ts={class:"btn-content"},zs={key:0,class:"send-icon"},Ms={key:1,class:"loading-spinner"};function Es(e,a,t,n,r,i){var s=(0,u.g2)("TheHeader"),l=(0,u.g2)("el-input");return(0,u.uX)(),(0,u.CE)("div",ns,[(0,u.bF)(s),(0,u.Lk)("div",rs,[(0,u.Lk)("div",is,[((0,u.uX)(),(0,u.CE)(u.FK,null,(0,u.pI)(50,function(e){return(0,u.Lk)("div",{key:e,class:"neural-node",style:(0,w.Tr)(n.getNodeStyle(e))},null,4)}),64))]),(0,u.Lk)("div",ss,[((0,u.uX)(),(0,u.CE)(u.FK,null,(0,u.pI)(20,function(e){return(0,u.Lk)("div",{key:e,class:"particle",style:(0,w.Tr)(n.getParticleStyle(e))},null,4)}),64))])]),(0,u.Lk)("div",ls,[(0,u.Lk)("div",cs,[(0,u.Lk)("div",os,[(0,u.Lk)("div",ds,[(0,u.Lk)("div",us,[(0,u.Lk)("div",vs,[a[4]||(a[4]=(0,u.Fv)('<div class="avatar-core" data-v-83e18de0><div class="core-pulse" data-v-83e18de0></div><div class="core-ring" data-v-83e18de0></div><div class="core-center" data-v-83e18de0><img src="'+ts+'" alt="AI" class="core-logo" data-v-83e18de0></div></div>',1)),(0,u.Lk)("div",{class:(0,w.C4)(["avatar-status",{active:n.isConnected}])},a[3]||(a[3]=[(0,u.Lk)("div",{class:"status-ring"},null,-1),(0,u.Lk)("div",{class:"status-dot"},null,-1)]),2)]),(0,u.Lk)("div",fs,[a[6]||(a[6]=(0,u.Lk)("h2",{class:"ai-title"},[(0,u.Lk)("span",{class:"title-text"},"衍盾 AI 助手"),(0,u.Lk)("div",{class:"title-glow"})],-1)),(0,u.Lk)("div",ps,[a[5]||(a[5]=(0,u.Lk)("span",{class:"status-indicator"},null,-1)),(0,u.eW)(" "+(0,w.v_)(n.isConnected?"网络已连接":"正在建立连接..."),1)])])])]),(0,u.Lk)("div",hs,[(0,u.Lk)("div",ms,[(0,u.Lk)("button",{class:"clear-chat-btn",onClick:a[0]||(a[0]=function(){return n.clearChat&&n.clearChat.apply(n,arguments)}),title:"清空对话"},a[7]||(a[7]=[(0,u.Lk)("div",{class:"btn-icon"},[(0,u.Lk)("span",{class:"control-icon"},"🗑️")],-1),(0,u.Lk)("span",{class:"btn-text"},"清空对话",-1),(0,u.Lk)("div",{class:"btn-glow"},null,-1)]))])])]),(0,u.Lk)("div",bs,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.messages,function(e,t){return(0,u.uX)(),(0,u.CE)("div",{key:t,class:(0,w.C4)(["message",e.role]),style:(0,w.Tr)({animationDelay:"".concat(.1*t,"s")})},["assistant"===e.role?((0,u.uX)(),(0,u.CE)("div",ks,a[8]||(a[8]=[(0,u.Lk)("div",{class:"ai-message-avatar"},[(0,u.Lk)("div",{class:"avatar-glow"}),(0,u.Lk)("div",{class:"avatar-icon"},[(0,u.Lk)("img",{src:ts,alt:"AI",class:"avatar-img"})])],-1)]))):((0,u.uX)(),(0,u.CE)("div",gs,a[9]||(a[9]=[(0,u.Lk)("div",{class:"user-message-avatar"},[(0,u.Lk)("div",{class:"avatar-glow"}),(0,u.Lk)("div",{class:"avatar-icon"},[(0,u.Lk)("img",{src:ts,alt:"User",class:"avatar-img"})])],-1)]))),(0,u.Lk)("div",ws,[(0,u.Lk)("div",ys,[a[10]||(a[10]=(0,u.Lk)("div",{class:"bubble-glow"},null,-1)),(0,u.Lk)("div",{class:"message-text",innerHTML:n.formatMessage(e.content)},null,8,Ls),(0,u.Lk)("div",Fs,(0,w.v_)(n.formatTime(e.timestamp)),1)])])],6)}),128)),n.isTyping?((0,u.uX)(),(0,u.CE)("div",_s,a[11]||(a[11]=[(0,u.Fv)('<div class="message-avatar" data-v-83e18de0><div class="ai-message-avatar" data-v-83e18de0><div class="avatar-glow pulsing" data-v-83e18de0></div><div class="avatar-icon" data-v-83e18de0><img src="'+ts+'" alt="AI" class="avatar-img" data-v-83e18de0></div></div></div><div class="message-content" data-v-83e18de0><div class="typing-bubble" data-v-83e18de0><div class="bubble-glow" data-v-83e18de0></div><div class="typing-indicator" data-v-83e18de0><div class="typing-dots" data-v-83e18de0><span data-v-83e18de0></span><span data-v-83e18de0></span><span data-v-83e18de0></span></div><div class="typing-text" data-v-83e18de0>AI正在思考中...</div></div></div></div>',2)]))):(0,u.Q3)("",!0)],512),(0,u.Lk)("div",Cs,[(0,u.Lk)("div",xs,[a[14]||(a[14]=(0,u.Lk)("div",{class:"input-glow"},null,-1)),(0,u.Lk)("div",As,[(0,u.bF)(l,{modelValue:n.userInput,"onUpdate:modelValue":a[1]||(a[1]=function(e){return n.userInput=e}),type:"textarea",rows:1,placeholder:"与AI对话，探索无限可能...",onKeyup:(0,c.jR)((0,c.D$)(n.sendMessage,["exact"]),["enter"]),disabled:n.isTyping,class:"cyber-input"},null,8,["modelValue","onKeyup","disabled"])]),(0,u.Lk)("button",{class:(0,w.C4)(["send-btn",{loading:n.isTyping,active:n.userInput.trim()}]),onClick:a[2]||(a[2]=function(){return n.sendMessage&&n.sendMessage.apply(n,arguments)}),disabled:n.isTyping},[(0,u.Lk)("div",Ts,[n.isTyping?((0,u.uX)(),(0,u.CE)("div",Ms,a[12]||(a[12]=[(0,u.Lk)("div",{class:"spinner-ring"},null,-1)]))):((0,u.uX)(),(0,u.CE)("span",zs,"➤"))]),a[13]||(a[13]=(0,u.Lk)("div",{class:"btn-glow"},null,-1))],10,Ss)])])])]),a[15]||(a[15]=(0,u.Fv)('<div class="login-footer" data-v-83e18de0><p class="footer-text" data-v-83e18de0> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-83e18de0>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-83e18de0>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-83e18de0>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-83e18de0><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-83e18de0> 渝公网安备50011702501094号 </a></p></div>',1))])}t(11392);var Vs=t(13574),Rs=t(94059),Ws=t(8880),Hs=t.n(Ws);const Is={name:"AI",components:{TheHeader:U.A},setup:function(){var e=(0,N.KR)([]),a=(0,N.KR)(""),t=(0,N.KR)(!1),n=(0,N.KR)(!0),r=(0,N.KR)(null),i=(0,N.KR)(null),s=function(){var e=100*Math.random(),a=100*Math.random(),t=5*Math.random(),n=3+4*Math.random();return{left:"".concat(e,"%"),top:"".concat(a,"%"),animationDelay:"".concat(t,"s"),animationDuration:"".concat(n,"s")}},l=function(){var e=100*Math.random(),a=100*Math.random(),t=3*Math.random(),n=4+6*Math.random(),r=2+4*Math.random();return{left:"".concat(e,"%"),top:"".concat(a,"%"),width:"".concat(r,"px"),height:"".concat(r,"px"),animationDelay:"".concat(t,"s"),animationDuration:"".concat(n,"s")}};(0,u.sV)(function(){setTimeout(function(){e.value.push({role:"assistant",content:"**衍盾AI助手已激活**\n\n我是您的专属网络安全AI顾问，具备以下能力：\n\n• **漏洞分析** - 深度解析安全漏洞\n• **防护建议** - 提供专业安全方案\n• **渗透测试** - 指导安全测试流程\n• **威胁情报** - 分析最新安全态势\n\n准备好开始我们的安全之旅了吗？",timestamp:new Date})},1e3)});var c=function(e){return Rs.A.sanitize((0,Vs.xI)(e))},o=function(e){return Hs()(e).format("HH:mm")},d=function(){var e=(0,k.A)((0,b.A)().m(function e(){return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:return e.n=1,(0,u.dY)();case 1:r.value&&(r.value.scrollTop=r.value.scrollHeight);case 2:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}();(0,u.wB)(e,function(){d()});var v=function(){var n=(0,k.A)((0,b.A)().m(function n(){var r,s,l,c,o,d,u;return(0,b.A)().w(function(n){while(1)switch(n.n){case 0:if(a.value.trim()||i.value){n.n=1;break}return n.a(2);case 1:return r={text:a.value.trim(),image:i.value},e.value.push({role:"user",content:a.value,timestamp:new Date}),a.value="",i.value=null,t.value=!0,n.p=2,n.n=3,te.A.post("/ai/chat",r);case 3:s=n.v,l=s.data.message,e.value.push({role:"assistant",content:l,timestamp:new Date}),n.n=5;break;case 4:n.p=4,u=n.v,console.error("AI响应错误:",u),d=(null===(c=u.response)||void 0===c||null===(c=c.data)||void 0===c?void 0:c.error)||(null===(o=u.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.message)||"抱歉，我遇到了一些问题。请稍后再试。",e.value.push({role:"assistant",content:d,timestamp:new Date});case 5:return n.p=5,t.value=!1,n.f(5);case 6:return n.a(2)}},n,null,[[2,4,5,6]])}));return function(){return n.apply(this,arguments)}}(),f=function(){var e=(0,k.A)((0,b.A)().m(function e(a){var t,n,r;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(t=a.type.startsWith("image/"),n=a.size/1024/1024<2,t){e.n=1;break}return ae.nk.error("只能上传图片文件！"),e.a(2,!1);case 1:if(n){e.n=2;break}return ae.nk.error("图片大小不能超过 2MB！"),e.a(2,!1);case 2:return r=new FileReader,r.readAsDataURL(a),r.onload=function(e){i.value=e.target.result},e.a(2,!1)}},e)}));return function(a){return e.apply(this,arguments)}}(),p=function(){e.value=[{role:"assistant",content:"对话已清空。有什么我可以帮你的吗？",timestamp:new Date}]};return{messages:e,userInput:a,isTyping:t,isConnected:n,chatContainer:r,sendMessage:v,formatMessage:c,formatTime:o,clearChat:p,beforeImageUpload:f,getNodeStyle:s,getParticleStyle:l}}},Bs=(0,p.A)(Is,[["render",Es],["__scopeId","data-v-83e18de0"]]),Xs=Bs;var Ds={class:"ranking-container"},Ns={class:"ranking-content"},Ps={class:"ranking-header"},Us={class:"ranking-tabs"},Ks=["onClick"],js={class:"ranking-main"},qs={class:"ranking-cards"},Qs={class:"ranking-top3"},Os={class:"rank-badge"},Ys={class:"user-avatar"},$s=["src","alt"],Js={class:"user-name"},Gs={class:"user-score"},Zs={class:"user-stats"},el={class:"stat-item"},al={class:"stat-value"},tl={class:"stat-item"},nl={class:"stat-value"},rl={class:"stat-item"},il={class:"stat-value"},sl={class:"ranking-table"},ll={class:"col-rank"},cl={class:"col-user"},ol={class:"user-info"},dl=["src","alt"],ul={class:"col-submissions"},vl={class:"col-rewards"},fl={class:"col-accuracy"},pl={class:"accuracy-bar"},hl={class:"col-levels"},ml={class:"level-bars"},bl={key:0},kl={key:0},gl={key:0},wl={key:0},yl={key:0,class:"loading-state"},Ll={key:1,class:"empty-state"};function Fl(e,a,t,n,r,i){var s=(0,u.g2)("TheHeader");return(0,u.uX)(),(0,u.CE)("div",Ds,[(0,u.bF)(s),(0,u.Lk)("div",Ns,[(0,u.Lk)("div",Ps,[a[0]||(a[0]=(0,u.Lk)("h1",{class:"ranking-title"},"白帽排行榜",-1)),(0,u.Lk)("div",Us,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.tabs,function(e){return(0,u.uX)(),(0,u.CE)("div",{key:e.value,class:(0,w.C4)(["tab-item",{active:n.currentPeriod===e.value}]),onClick:function(a){return n.changePeriod(e.value)}},(0,w.v_)(e.label),11,Ks)}),128))])]),(0,u.Lk)("div",js,[(0,u.Lk)("div",qs,[(0,u.Lk)("div",Qs,[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.topUsers,function(e,t){var r,i,s;return(0,u.uX)(),(0,u.CE)("div",{key:e.id,class:(0,w.C4)(["top-user-card","rank-".concat(t+1)])},[(0,u.Lk)("div",Os,(0,w.v_)(t+1),1),(0,u.Lk)("div",Ys,[(0,u.Lk)("img",{src:(null===(r=e.user)||void 0===r?void 0:r.avatar)||"/assets/logo.png",alt:null===(i=e.user)||void 0===i?void 0:i.username},null,8,$s)]),(0,u.Lk)("div",Js,(0,w.v_)(null===(s=e.user)||void 0===s?void 0:s.username),1),(0,u.Lk)("div",Gs,(0,w.v_)(e.score)+"分",1),(0,u.Lk)("div",Zs,[(0,u.Lk)("div",el,[(0,u.Lk)("div",al,(0,w.v_)(e.submission_count),1),a[1]||(a[1]=(0,u.Lk)("div",{class:"stat-label"},"漏洞数",-1))]),(0,u.Lk)("div",tl,[(0,u.Lk)("div",nl,(0,w.v_)(n.formatReward(e.reward_amount)),1),a[2]||(a[2]=(0,u.Lk)("div",{class:"stat-label"},"奖励",-1))]),(0,u.Lk)("div",rl,[(0,u.Lk)("div",il,(0,w.v_)(e.accuracy_rate)+"%",1),a[3]||(a[3]=(0,u.Lk)("div",{class:"stat-label"},"准确率",-1))])])],2)}),128))]),(0,u.Lk)("div",sl,[a[6]||(a[6]=(0,u.Fv)('<div class="table-header" data-v-27290c7c><div class="col-rank" data-v-27290c7c>排名</div><div class="col-user" data-v-27290c7c>白帽子</div><div class="col-submissions" data-v-27290c7c>漏洞提交</div><div class="col-rewards" data-v-27290c7c>获得奖励</div><div class="col-accuracy" data-v-27290c7c>准确率</div><div class="col-levels" data-v-27290c7c>漏洞等级分布</div></div>',1)),((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(n.otherUsers,function(e){var t,r,i;return(0,u.uX)(),(0,u.CE)("div",{key:e.id,class:"table-row"},[(0,u.Lk)("div",ll,(0,w.v_)(e.rank),1),(0,u.Lk)("div",cl,[(0,u.Lk)("div",ol,[(0,u.Lk)("img",{src:(null===(t=e.user)||void 0===t?void 0:t.avatar)||"/assets/logo.png",alt:null===(r=e.user)||void 0===r?void 0:r.username,class:"user-avatar-small"},null,8,dl),(0,u.Lk)("span",null,(0,w.v_)(null===(i=e.user)||void 0===i?void 0:i.username),1)])]),(0,u.Lk)("div",ul,(0,w.v_)(e.submission_count),1),(0,u.Lk)("div",vl,(0,w.v_)(n.formatReward(e.reward_amount)),1),(0,u.Lk)("div",fl,[(0,u.Lk)("div",pl,[(0,u.Lk)("div",{class:"accuracy-progress",style:(0,w.Tr)("width: ".concat(e.accuracy_rate,"%"))},null,4)]),(0,u.Lk)("span",null,(0,w.v_)(e.accuracy_rate)+"%",1)]),(0,u.Lk)("div",hl,[(0,u.Lk)("div",ml,[(0,u.Lk)("div",{class:"level-bar critical",style:(0,w.Tr)("height: ".concat(n.getLevelBarHeight(e.critical_vuln_count,e)))},[e.critical_vuln_count>0?((0,u.uX)(),(0,u.CE)("span",bl,(0,w.v_)(e.critical_vuln_count),1)):(0,u.Q3)("",!0)],4),(0,u.Lk)("div",{class:"level-bar high",style:(0,w.Tr)("height: ".concat(n.getLevelBarHeight(e.high_vuln_count,e)))},[e.high_vuln_count>0?((0,u.uX)(),(0,u.CE)("span",kl,(0,w.v_)(e.high_vuln_count),1)):(0,u.Q3)("",!0)],4),(0,u.Lk)("div",{class:"level-bar medium",style:(0,w.Tr)("height: ".concat(n.getLevelBarHeight(e.medium_vuln_count,e)))},[e.medium_vuln_count>0?((0,u.uX)(),(0,u.CE)("span",gl,(0,w.v_)(e.medium_vuln_count),1)):(0,u.Q3)("",!0)],4),(0,u.Lk)("div",{class:"level-bar low",style:(0,w.Tr)("height: ".concat(n.getLevelBarHeight(e.low_vuln_count,e)))},[e.low_vuln_count>0?((0,u.uX)(),(0,u.CE)("span",wl,(0,w.v_)(e.low_vuln_count),1)):(0,u.Q3)("",!0)],4)]),a[4]||(a[4]=(0,u.Fv)('<div class="level-labels" data-v-27290c7c><span class="level-label critical" data-v-27290c7c>严重</span><span class="level-label high" data-v-27290c7c>高危</span><span class="level-label medium" data-v-27290c7c>中危</span><span class="level-label low" data-v-27290c7c>低危</span></div>',1))])])}),128)),n.loading?((0,u.uX)(),(0,u.CE)("div",yl,a[5]||(a[5]=[(0,u.Lk)("div",{class:"loading-spinner"},null,-1),(0,u.Lk)("span",null,"加载中...",-1)]))):(0,u.Q3)("",!0),n.loading||0!==n.rankings.length?(0,u.Q3)("",!0):((0,u.uX)(),(0,u.CE)("div",Ll," 暂无排行数据 "))])]),a[7]||(a[7]=(0,u.Fv)('<div class="score-rules" data-v-27290c7c><h3 class="rules-title" data-v-27290c7c>分数计算规则</h3><div class="rules-card" data-v-27290c7c><div class="rule-item" data-v-27290c7c><div class="rule-header" data-v-27290c7c><div class="rule-icon" data-v-27290c7c><i class="el-icon-warning" data-v-27290c7c></i></div><div class="rule-name" data-v-27290c7c>漏洞等级</div></div><div class="rule-content" data-v-27290c7c><div class="rule-row" data-v-27290c7c><span class="level-tag critical" data-v-27290c7c>严重</span><span data-v-27290c7c>1000分/个</span></div><div class="rule-row" data-v-27290c7c><span class="level-tag high" data-v-27290c7c>高危</span><span data-v-27290c7c>500分/个</span></div><div class="rule-row" data-v-27290c7c><span class="level-tag medium" data-v-27290c7c>中危</span><span data-v-27290c7c>200分/个</span></div><div class="rule-row" data-v-27290c7c><span class="level-tag low" data-v-27290c7c>低危</span><span data-v-27290c7c>50分/个</span></div></div></div><div class="rule-item" data-v-27290c7c><div class="rule-header" data-v-27290c7c><div class="rule-icon" data-v-27290c7c><i class="el-icon-check" data-v-27290c7c></i></div><div class="rule-name" data-v-27290c7c>准确率加成</div></div><div class="rule-content" data-v-27290c7c><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>准确率 ≥ 90%</span><span data-v-27290c7c>总分 × 1.5</span></div><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>准确率 ≥ 80%</span><span data-v-27290c7c>总分 × 1.3</span></div><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>准确率 ≥ 70%</span><span data-v-27290c7c>总分 × 1.1</span></div></div></div><div class="rule-item" data-v-27290c7c><div class="rule-header" data-v-27290c7c><div class="rule-icon" data-v-27290c7c><i class="el-icon-medal" data-v-27290c7c></i></div><div class="rule-name" data-v-27290c7c>活跃度加成</div></div><div class="rule-content" data-v-27290c7c><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>连续提交 &gt; 30天</span><span data-v-27290c7c>额外 500分</span></div><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>月提交 &gt; 10个</span><span data-v-27290c7c>额外 300分</span></div></div></div><div class="rule-item" data-v-27290c7c><div class="rule-header" data-v-27290c7c><div class="rule-icon" data-v-27290c7c><i class="el-icon-star-off" data-v-27290c7c></i></div><div class="rule-name" data-v-27290c7c>特殊奖励</div></div><div class="rule-content" data-v-27290c7c><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>首次发现某类漏洞</span><span data-v-27290c7c>额外 200分</span></div><div class="rule-row" data-v-27290c7c><span data-v-27290c7c>高质量漏洞报告</span><span data-v-27290c7c>额外 100分</span></div></div></div></div><div class="rules-note" data-v-27290c7c><p data-v-27290c7c>注：最终分数可能会根据平台规则进行调整</p><p data-v-27290c7c>排行榜每日更新，实时反映最新得分情况</p></div></div>',1))]),a[8]||(a[8]=(0,u.Fv)('<div class="ranking-footer" data-v-27290c7c><div class="ranking-info" data-v-27290c7c><div class="info-item" data-v-27290c7c><div class="info-icon" data-v-27290c7c><i class="el-icon-info" data-v-27290c7c></i></div><div class="info-text" data-v-27290c7c><h3 data-v-27290c7c>排行说明</h3><p data-v-27290c7c>排行榜数据基于白帽子提交的漏洞数量、质量、奖励金额等综合计算得出</p></div></div><div class="info-item" data-v-27290c7c><div class="info-icon" data-v-27290c7c><i class="el-icon-refresh" data-v-27290c7c></i></div><div class="info-text" data-v-27290c7c><h3 data-v-27290c7c>更新周期</h3><p data-v-27290c7c>月度榜单：每月1日更新</p><p data-v-27290c7c>季度榜单：每季度首月1日更新</p><p data-v-27290c7c>年度榜单：每年1月1日更新</p></div></div></div></div>',1))]),a[9]||(a[9]=(0,u.Fv)('<div class="login-footer" data-v-27290c7c><p class="footer-text" data-v-27290c7c> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-27290c7c>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-27290c7c>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-27290c7c>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-27290c7c><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-27290c7c> 渝公网安备50011702501094号 </a></p></div>',1))])}t(2892);const _l={name:"Ranking",components:{TheHeader:U.A},setup:function(){var e=(0,N.KR)([]),a=(0,N.KR)(!0),t=(0,N.KR)("month"),n=[{label:"月度榜",value:"month"},{label:"季度榜",value:"quarter"},{label:"年度榜",value:"year"}],r=function(){var n=(0,k.A)((0,b.A)().m(function n(){var r,i;return(0,b.A)().w(function(n){while(1)switch(n.n){case 0:return a.value=!0,n.p=1,n.n=2,te.A.get("/rankings?period=".concat(t.value,"&limit=50"));case 2:r=n.v,r.data.success?(e.value=r.data.data||[],console.log("排行榜数据:",e.value)):(console.error("获取排行榜失败:",r.data.message),e.value=[]),n.n=4;break;case 3:n.p=3,i=n.v,console.error("获取排行榜数据失败:",i),e.value=[];case 4:return n.p=4,a.value=!1,n.f(4);case 5:return n.a(2)}},n,null,[[1,3,4,5]])}));return function(){return n.apply(this,arguments)}}(),i=function(e){t.value=e,r()},s=function(e){return"¥".concat(Number(e).toLocaleString())},l=function(e,a){var t=a.critical_vuln_count+a.high_vuln_count+a.medium_vuln_count+a.low_vuln_count;return 0===t?"0%":"".concat(Math.max(10,e/t*100),"%")},c=(0,u.EW)(function(){return e.value.slice(0,3)}),o=(0,u.EW)(function(){return e.value.slice(3)});return(0,u.sV)(function(){r()}),{rankings:e,loading:a,currentPeriod:t,tabs:n,topUsers:c,otherUsers:o,changePeriod:i,formatReward:s,getLevelBarHeight:l}}},Cl=(0,p.A)(_l,[["render",Fl],["__scopeId","data-v-27290c7c"]]),xl=Cl;var Al=[{path:"/",name:"Home",component:Y,meta:{title:"首页 - 衍盾星云"}},{path:"/login",name:"Login",component:ie,meta:{title:"登录 - 衍盾星云"}},{path:"/register",name:"Register",component:fe,meta:{title:"注册 - 衍盾星云"}},{path:"/user/profile",name:"UserProfile",component:fi,meta:{title:"个人中心 - 衍盾星云",requiresAuth:!0}},{path:"/projects",name:"Projects",component:as,meta:{title:"项目大厅 - 衍盾星云"}},{path:"/projects/:id",name:"ProjectDetail",component:function(){return t.e(331).then(t.bind(t,65331))},meta:{title:"项目详情 - 衍盾星云"}},{path:"/projects/:id/participate",name:"ProjectParticipate",component:function(){return t.e(252).then(t.bind(t,92252))},meta:{title:"参与项目 - 衍盾星云",requiresAuth:!0}},{path:"/user/submissions",name:"UserSubmissions",component:fi,props:{activeTab:"submissions"},meta:{title:"我的提交 - 衍盾星云",requiresAuth:!0}},{path:"/user/rewards",name:"UserRewards",component:fi,props:{activeTab:"rewards"},meta:{title:"我的奖励 - 衍盾星云",requiresAuth:!0}},{path:"/submit",name:"SubmitVulnerability",component:function(){return t.e(772).then(t.bind(t,80772))},meta:{title:"提交漏洞 - 衍盾星云",requiresAuth:!0,requiresUserType:"whiteHat"}},{path:"/enterprise/publish",name:"EnterprisePublish",component:function(){return t.e(957).then(t.bind(t,65957))},meta:{title:"任务发布 - 衍盾星云",requiresAuth:!0,requiresUserType:"enterprise"}},{path:"/enterprise/review",name:"EnterpriseReview",component:function(){return t.e(25).then(t.bind(t,72025))},meta:{title:"任务审核 - 衍盾星云",requiresAuth:!0,requiresUserType:"enterprise"}},{path:"/ai",name:"AI",component:Xs,meta:{title:"AI问答 - 衍盾星云"}},{path:"/query",name:"QueryCenter",component:function(){return t.e(282).then(t.bind(t,21282))},meta:{title:"查询中心 - 衍盾星云"}},{path:"/ranking",name:"Ranking",component:xl,meta:{title:"排行榜 - 衍盾星云"}},{path:"/activities",name:"Activities",component:function(){return t.e(211).then(t.bind(t,18211))},meta:{title:"活动中心 - 衍盾星云"}},{path:"/activities/:id",name:"ActivityDetail",component:function(){return t.e(121).then(t.bind(t,77121))},meta:{title:"活动详情 - 衍盾星云"}},{path:"/community",name:"Community",component:function(){return t.e(4).then(t.bind(t,49004))},meta:{title:"交流中心 - 衍盾星云"}},{path:"/community/posts/:id",name:"PostDetail",component:function(){return t.e(129).then(t.bind(t,72129))},meta:{title:"帖子详情 - 衍盾星云"}},{path:"/help",name:"Help",component:function(){return t.e(415).then(t.bind(t,415))},meta:{title:"帮助中心 - 衍盾星云"}},{path:"/admin/login",name:"AdminLogin",component:function(){return t.e(304).then(t.bind(t,96304))},meta:{title:"管理员登录 - 衍盾星云"}},{path:"/admin",component:function(){return t.e(435).then(t.bind(t,6435))},meta:{requiresAuth:!0,requiresUserType:"admin"},children:[{path:"dashboard",name:"AdminDashboard",component:function(){return t.e(232).then(t.bind(t,93232))},meta:{title:"管理后台 - 衍盾星云"}},{path:"users",name:"AdminUsers",component:function(){return t.e(971).then(t.bind(t,40971))},meta:{title:"用户管理 - 衍盾星云"}},{path:"posts",name:"AdminPosts",component:function(){return t.e(404).then(t.bind(t,66404))},meta:{title:"帖子管理 - 衍盾星云"}},{path:"projects",name:"AdminProjects",component:function(){return t.e(201).then(t.bind(t,86201))},meta:{title:"项目管理 - 衍盾星云"}},{path:"announcements",name:"AdminAnnouncements",component:function(){return t.e(675).then(t.bind(t,6675))},meta:{title:"公告管理 - 衍盾星云"}},{path:"disputes",name:"AdminDisputes",component:function(){return t.e(50).then(t.bind(t,74050))},meta:{title:"争议审核 - 衍盾星云"}},{path:"settings",name:"AdminSettings",component:function(){return t.e(193).then(t.bind(t,12193))},meta:{title:"系统设置 - 衍盾星云"}}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:ge,meta:{title:"页面未找到 - 衍盾星云"}}],Sl=(0,g.aE)({history:(0,g.LA)(),routes:Al,scrollBehavior:function(){return{top:0}}});Sl.beforeEach(function(){var e=(0,k.A)((0,b.A)().m(function e(a,t,n){var r;return(0,b.A)().w(function(e){while(1)switch(e.n){case 0:if(document.title=a.meta.title||"衍盾星云 - 基于区块链的漏洞悬赏智能响应平台",!a.meta.requiresAuth){e.n=3;break}if(P.A.isLoggedIn()){e.n=1;break}return n({path:"/login",query:{redirect:a.fullPath}}),e.a(2);case 1:if(P.A.validateLoginStatus()["catch"](function(e){console.error("路由守卫验证登录状态失败:",e)}),!a.meta.requiresUserType){e.n=2;break}if(r=P.A.getCurrentUser(),r&&r.userType===a.meta.requiresUserType){e.n=2;break}return n({path:"/"}),e.a(2);case 2:n(),e.n=4;break;case 3:n();case 4:return e.a(2)}},e)}));return function(a,t,n){return e.apply(this,arguments)}}());const Tl=Sl;var zl=function(e,a){var t;return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];clearTimeout(t),t=setTimeout(function(){return e.apply(null,r)},a)}},Ml=window.ResizeObserver;window.ResizeObserver=function(e){function a(e){return(0,i.A)(this,a),e=zl(e,20),(0,s.A)(this,a,[e])}return(0,l.A)(a,e),(0,r.A)(a)}(Ml),window.addEventListener("error",function(e){"ResizeObserver loop completed with undelivered notifications."===e.message&&e.stopImmediatePropagation()});var El=(0,c.Ef)(m);El.use(o.A),El.use(Tl);for(var Vl=0,Rl=Object.entries(d);Vl<Rl.length;Vl++){var Wl=(0,n.A)(Rl[Vl],2),Hl=Wl[0],Il=Wl[1];El.component(Hl,Il)}El.mount("#app")},74010:(e,a,t)=>{t.d(a,{A:()=>g});var n=t(95976),r=t(29746),i={class:"app-footer"},s={class:"container"},l={class:"footer-content"},c={class:"footer-section"},o={class:"footer-links"},d={class:"footer-section"},u={class:"footer-links"};function v(e,a,t,v,f,p){var h=(0,n.g2)("router-link");return(0,n.uX)(),(0,n.CE)("footer",i,[(0,n.Lk)("div",s,[(0,n.Lk)("div",l,[(0,n.Lk)("div",c,[a[3]||(a[3]=(0,n.Lk)("h3",{class:"footer-title"},"企业服务",-1)),(0,n.Lk)("ul",o,[(0,n.Lk)("li",null,[(0,n.Lk)("a",{href:"#",onClick:a[0]||(a[0]=(0,r.D$)(function(){return v.handlePublishTask&&v.handlePublishTask.apply(v,arguments)},["prevent"]))},"发布任务")]),(0,n.Lk)("li",null,[(0,n.bF)(h,{to:"/projects"},{default:(0,n.k6)(function(){return a[2]||(a[2]=[(0,n.eW)("任务大厅")])}),_:1,__:[2]})])])]),(0,n.Lk)("div",d,[a[6]||(a[6]=(0,n.Lk)("h3",{class:"footer-title"},"白帽服务",-1)),(0,n.Lk)("ul",u,[(0,n.Lk)("li",null,[(0,n.bF)(h,{to:"/projects"},{default:(0,n.k6)(function(){return a[4]||(a[4]=[(0,n.eW)("项目大厅")])}),_:1,__:[4]})]),(0,n.Lk)("li",null,[(0,n.bF)(h,{to:"/help"},{default:(0,n.k6)(function(){return a[5]||(a[5]=[(0,n.eW)("帮助中心")])}),_:1,__:[5]})]),(0,n.Lk)("li",null,[(0,n.Lk)("a",{href:"#",onClick:a[1]||(a[1]=(0,r.D$)(function(){return v.handleUserProfile&&v.handleUserProfile.apply(v,arguments)},["prevent"]))},"个人首页")])])]),a[7]||(a[7]=(0,n.Lk)("div",{class:"footer-section"},[(0,n.Lk)("h3",{class:"footer-title"},"联系我们"),(0,n.Lk)("ul",{class:"footer-links"},[(0,n.Lk)("li",null,"企业咨询：XXXXXXXX"),(0,n.Lk)("li",null,"白帽咨询：XXXXXXXX"),(0,n.Lk)("li",null,"咨询邮箱：<EMAIL>"),(0,n.Lk)("li",null,"工作时间：周一至周五，09:30～18:30")])],-1))]),a[8]||(a[8]=(0,n.Fv)('<div class="copyright" data-v-2b4bc877><p class="copyright-text" data-v-2b4bc877> Copyright © 2025 衍盾星云 版权所有 <span class="beian-separator" data-v-2b4bc877>|</span><a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" data-v-2b4bc877>渝ICP备2025053738号-1</a><span class="beian-separator" data-v-2b4bc877>|</span><a href="https://beian.mps.gov.cn/#/query/webSearch?code=50011702501094" rel="noreferrer" target="_blank" class="police-beian-link" data-v-2b4bc877><img src="/assets/images/备案图标.png" alt="公安备案" class="police-beian-icon" data-v-2b4bc877> 渝公网安备50011702501094号 </a></p></div>',1))])])}t(44114);var f=t(39053),p=t(18057),h=t(20907);const m={name:"AppFooter",setup:function(){var e=(0,f.rd)(),a=function(){if(!h.A.isLoggedIn())return p.nk.warning("请先登录再进行操作"),void e.push("/login");e.push("/enterprise/publish")},t=function(){if(!h.A.isLoggedIn())return p.nk.warning("请先登录再进行操作"),void e.push("/login");e.push("/user/profile")};return{handlePublishTask:a,handleUserProfile:t}}};var b=t(1169);const k=(0,b.A)(m,[["render",v],["__scopeId","data-v-2b4bc877"]]),g=k},80401:(e,a,t)=>{t.d(a,{A:()=>C});var n=t(95976),r=t(10160),i={class:"header"},s={class:"container header-content"},l={class:"nav"},c={class:"nav-list"},o={class:"user-actions"},d={key:1,class:"user-info"},u={class:"user-dropdown",ref:"userDropdown"},v=["src"],f={key:1,class:"user-avatar-placeholder"},p={class:"username"};function h(e,a,t,h,m,b){var k=(0,n.g2)("router-link");return(0,n.uX)(),(0,n.CE)("header",i,[(0,n.Lk)("div",s,[a[22]||(a[22]=(0,n.Lk)("div",{class:"logo"},[(0,n.Lk)("img",{src:"/assets/logo.png",alt:"衍盾星云",class:"logo-img"}),(0,n.Lk)("span",{class:"logo-text"},"衍盾星云")],-1)),(0,n.Lk)("nav",l,[(0,n.Lk)("ul",c,[(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/"===h.currentRoute}])},[(0,n.bF)(k,{to:"/"},{default:(0,n.k6)(function(){return a[2]||(a[2]=[(0,n.eW)("首页")])}),_:1,__:[2]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/projects"===h.currentRoute}])},[(0,n.bF)(k,{to:"/projects"},{default:(0,n.k6)(function(){return a[3]||(a[3]=[(0,n.eW)("项目大厅")])}),_:1,__:[3]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/ranking"===h.currentRoute}])},[(0,n.bF)(k,{to:"/ranking"},{default:(0,n.k6)(function(){return a[4]||(a[4]=[(0,n.eW)("白帽排行")])}),_:1,__:[4]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/activities"===h.currentRoute}])},[(0,n.bF)(k,{to:"/activities"},{default:(0,n.k6)(function(){return a[5]||(a[5]=[(0,n.eW)("公告活动")])}),_:1,__:[5]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/community"===h.currentRoute}])},[(0,n.bF)(k,{to:"/community"},{default:(0,n.k6)(function(){return a[6]||(a[6]=[(0,n.eW)("交流中心")])}),_:1,__:[6]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/ai"===h.currentRoute}])},[(0,n.bF)(k,{to:"/ai"},{default:(0,n.k6)(function(){return a[7]||(a[7]=[(0,n.eW)("AI问答")])}),_:1,__:[7]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/query"===h.currentRoute}])},[(0,n.bF)(k,{to:"/query"},{default:(0,n.k6)(function(){return a[8]||(a[8]=[(0,n.eW)("查询中心")])}),_:1,__:[8]})],2),(0,n.Lk)("li",{class:(0,r.C4)(["nav-item",{active:"/help"===h.currentRoute}])},[(0,n.bF)(k,{to:"/help"},{default:(0,n.k6)(function(){return a[9]||(a[9]=[(0,n.eW)("帮助中心")])}),_:1,__:[9]})],2)])]),(0,n.Lk)("div",o,[h.currentUser?(0,n.Q3)("",!0):((0,n.uX)(),(0,n.CE)(n.FK,{key:0},[(0,n.bF)(k,{to:"/submit",class:"submit-btn"},{default:(0,n.k6)(function(){return a[10]||(a[10]=[(0,n.eW)("提交漏洞")])}),_:1,__:[10]}),(0,n.bF)(k,{to:"/login",class:"login-btn"},{default:(0,n.k6)(function(){return a[11]||(a[11]=[(0,n.eW)("登录/注册")])}),_:1,__:[11]}),(0,n.bF)(k,{to:"/admin/login",class:"admin-btn"},{default:(0,n.k6)(function(){return a[12]||(a[12]=[(0,n.eW)("管理员登录")])}),_:1,__:[12]})],64)),h.currentUser?((0,n.uX)(),(0,n.CE)("div",d,["enterprise"===h.currentUser.userType?((0,n.uX)(),(0,n.CE)(n.FK,{key:0},[(0,n.bF)(k,{to:"/enterprise/publish",class:"action-btn"},{default:(0,n.k6)(function(){return a[13]||(a[13]=[(0,n.eW)("任务发布")])}),_:1,__:[13]}),(0,n.bF)(k,{to:"/enterprise/review",class:"action-btn"},{default:(0,n.k6)(function(){return a[14]||(a[14]=[(0,n.eW)("任务审核")])}),_:1,__:[14]})],64)):((0,n.uX)(),(0,n.Wv)(k,{key:1,to:"/submit",class:"submit-btn"},{default:(0,n.k6)(function(){return a[15]||(a[15]=[(0,n.eW)("提交漏洞")])}),_:1,__:[15]})),(0,n.Lk)("div",u,[(0,n.Lk)("div",{class:"user-dropdown-trigger",onClick:a[0]||(a[0]=function(){return h.toggleDropdown&&h.toggleDropdown.apply(h,arguments)})},[h.currentUser.avatar?((0,n.uX)(),(0,n.CE)("img",{key:0,src:h.currentUser.avatar,class:"user-avatar",alt:"用户头像"},null,8,v)):((0,n.uX)(),(0,n.CE)("div",f,(0,r.v_)(h.currentUser.username.charAt(0).toUpperCase()),1)),(0,n.Lk)("span",p,(0,r.v_)(h.currentUser.username),1),a[16]||(a[16]=(0,n.Lk)("i",{class:"el-icon-arrow-down"},null,-1))]),(0,n.Lk)("div",{class:(0,r.C4)(["user-dropdown-menu",{show:h.showDropdown}])},[(0,n.bF)(k,{to:"/user/profile",class:"dropdown-item"},{default:(0,n.k6)(function(){return a[17]||(a[17]=[(0,n.Lk)("i",{class:"el-icon-user"},null,-1),(0,n.eW)(" 个人中心 ")])}),_:1,__:[17]}),"whiteHat"===h.currentUser.userType?((0,n.uX)(),(0,n.Wv)(k,{key:0,to:"/user/submissions",class:"dropdown-item"},{default:(0,n.k6)(function(){return a[18]||(a[18]=[(0,n.Lk)("i",{class:"el-icon-document"},null,-1),(0,n.eW)(" 我的提交 ")])}),_:1,__:[18]})):(0,n.Q3)("",!0),"whiteHat"===h.currentUser.userType?((0,n.uX)(),(0,n.Wv)(k,{key:1,to:"/user/rewards",class:"dropdown-item"},{default:(0,n.k6)(function(){return a[19]||(a[19]=[(0,n.Lk)("i",{class:"el-icon-money"},null,-1),(0,n.eW)(" 我的奖励 ")])}),_:1,__:[19]})):(0,n.Q3)("",!0),a[21]||(a[21]=(0,n.Lk)("div",{class:"dropdown-divider"},null,-1)),(0,n.Lk)("div",{onClick:a[1]||(a[1]=function(){return h.handleLogout&&h.handleLogout.apply(h,arguments)}),class:"dropdown-item"},a[20]||(a[20]=[(0,n.Lk)("i",{class:"el-icon-switch-button"},null,-1),(0,n.eW)(" 退出登录 ")]))],2)],512)])):(0,n.Q3)("",!0)])])])}var m=t(24059),b=t(698),k=(t(44114),t(76031),t(12040)),g=t(39053),w=t(18057),y=t(20907);const L={name:"TheHeader",setup:function(){var e=(0,g.rd)(),a=(0,g.lq)(),t=(0,k.KR)(null),r=(0,k.KR)(!1),i=(0,k.KR)(null),s=(0,k.KR)(a.path);e.afterEach(function(e){s.value=e.path});var l=function(){var e=(0,b.A)((0,m.A)().m(function e(){return(0,m.A)().w(function(e){while(1)switch(e.n){case 0:y.A.isLoggedIn()?(t.value=y.A.getCurrentUser(),y.A.validateLoginStatus().then(function(e){e||(t.value=null)})["catch"](function(e){console.error("验证登录状态失败:",e)})):t.value=null;case 1:return e.a(2)}},e)}));return function(){return e.apply(this,arguments)}}(),c=function(){y.A.logout(),t.value=null,r.value=!1,(0,w.nk)({type:"success",message:"退出登录成功"}),e.push("/")},o=function(){r.value=!r.value},d=function(e){i.value&&!i.value.contains(e.target)&&(r.value=!1)},u=function(e,a){t.value=e?a:null};return(0,n.sV)(function(){l(),document.addEventListener("click",d),y.A.addLoginStatusListener(u);var e=setInterval(l,18e5);(0,n.xo)(function(){clearInterval(e),y.A.removeLoginStatusListener(u)})}),(0,n.xo)(function(){document.removeEventListener("click",d)}),{currentUser:t,showDropdown:r,userDropdown:i,currentRoute:s,handleLogout:c,toggleDropdown:o}}};var F=t(1169);const _=(0,F.A)(L,[["render",h],["__scopeId","data-v-3ae0d336"]]),C=_}},a={};function t(n){var r=a[n];if(void 0!==r)return r.exports;var i=a[n]={id:n,loaded:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}t.m=e,(()=>{var e=[];t.O=(a,n,r,i)=>{if(!n){var s=1/0;for(d=0;d<e.length;d++){for(var[n,r,i]=e[d],l=!0,c=0;c<n.length;c++)(!1&i||s>=i)&&Object.keys(t.O).every(e=>t.O[e](n[c]))?n.splice(c--,1):(l=!1,i<s&&(s=i));if(l){e.splice(d--,1);var o=r();void 0!==o&&(a=o)}}return a}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[n,r,i]}})(),(()=>{t.n=e=>{var a=e&&e.__esModule?()=>e["default"]:()=>e;return t.d(a,{a}),a}})(),(()=>{t.d=(e,a)=>{for(var n in a)t.o(a,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:a[n]})}})(),(()=>{t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((a,n)=>(t.f[n](e,a),a),[]))})(),(()=>{t.u=e=>"js/"+e+"."+{4:"764f25a8",25:"f02ccc41",50:"a2937aba",121:"b59c5fe5",129:"99de2720",193:"baf370f4",201:"9024dd4c",211:"fff6ccec",232:"f54294e6",252:"3845d1db",282:"1bca8e91",304:"eef5d00f",331:"30830918",404:"7b4cb998",415:"ad18bd4a",435:"04ecd386",675:"ed7a20ea",772:"a7c741cd",957:"0c042bf3",971:"bcbc63a8"}[e]+".js"})(),(()=>{t.miniCssF=e=>"css/"+e+"."+{4:"4b0d116b",25:"83a7768e",50:"0ed00ccb",121:"e0313239",129:"cf83fdaa",193:"8d2f74c6",201:"648c10d1",211:"b159fa50",232:"af6e5af3",252:"89be6188",282:"afd3ce32",304:"a66f550b",331:"9bc8e34f",404:"5edd64cf",415:"b2eacc35",435:"2f381ea2",675:"9111636e",772:"40b8013d",957:"9f1a8901",971:"5ec125da"}[e]+".css"})(),(()=>{t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{t.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a)})(),(()=>{var e={},a="yandun-nebula-frontend:";t.l=(n,r,i,s)=>{if(e[n])e[n].push(r);else{var l,c;if(void 0!==i)for(var o=document.getElementsByTagName("script"),d=0;d<o.length;d++){var u=o[d];if(u.getAttribute("src")==n||u.getAttribute("data-webpack")==a+i){l=u;break}}l||(c=!0,l=document.createElement("script"),l.charset="utf-8",l.timeout=120,t.nc&&l.setAttribute("nonce",t.nc),l.setAttribute("data-webpack",a+i),l.src=n),e[n]=[r];var v=(a,t)=>{l.onerror=l.onload=null,clearTimeout(f);var r=e[n];if(delete e[n],l.parentNode&&l.parentNode.removeChild(l),r&&r.forEach(e=>e(t)),a)return a(t)},f=setTimeout(v.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=v.bind(null,l.onerror),l.onload=v.bind(null,l.onload),c&&document.head.appendChild(l)}}})(),(()=>{t.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{t.nmd=e=>(e.paths=[],e.children||(e.children=[]),e)})(),(()=>{t.p=""})(),(()=>{if("undefined"!==typeof document){var e=(e,a,n,r,i)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",t.nc&&(s.nonce=t.nc);var l=t=>{if(s.onerror=s.onload=null,"load"===t.type)r();else{var n=t&&t.type,l=t&&t.target&&t.target.href||a,c=new Error("Loading CSS chunk "+e+" failed.\n("+n+": "+l+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=n,c.request=l,s.parentNode&&s.parentNode.removeChild(s),i(c)}};return s.onerror=s.onload=l,s.href=a,n?n.parentNode.insertBefore(s,n.nextSibling):document.head.appendChild(s),s},a=(e,a)=>{for(var t=document.getElementsByTagName("link"),n=0;n<t.length;n++){var r=t[n],i=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(i===e||i===a))return r}var s=document.getElementsByTagName("style");for(n=0;n<s.length;n++){r=s[n],i=r.getAttribute("data-href");if(i===e||i===a)return r}},n=n=>new Promise((r,i)=>{var s=t.miniCssF(n),l=t.p+s;if(a(s,l))return r();e(n,l,null,r,i)}),r={524:0};t.f.miniCss=(e,a)=>{var t={4:1,25:1,50:1,121:1,129:1,193:1,201:1,211:1,232:1,252:1,282:1,304:1,331:1,404:1,415:1,435:1,675:1,772:1,957:1,971:1};r[e]?a.push(r[e]):0!==r[e]&&t[e]&&a.push(r[e]=n(e).then(()=>{r[e]=0},a=>{throw delete r[e],a}))}}})(),(()=>{var e={524:0};t.f.j=(a,n)=>{var r=t.o(e,a)?e[a]:void 0;if(0!==r)if(r)n.push(r[2]);else{var i=new Promise((t,n)=>r=e[a]=[t,n]);n.push(r[2]=i);var s=t.p+t.u(a),l=new Error,c=n=>{if(t.o(e,a)&&(r=e[a],0!==r&&(e[a]=void 0),r)){var i=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.src;l.message="Loading chunk "+a+" failed.\n("+i+": "+s+")",l.name="ChunkLoadError",l.type=i,l.request=s,r[1](l)}};t.l(s,c,"chunk-"+a,a)}},t.O.j=a=>0===e[a];var a=(a,n)=>{var r,i,[s,l,c]=n,o=0;if(s.some(a=>0!==e[a])){for(r in l)t.o(l,r)&&(t.m[r]=l[r]);if(c)var d=c(t)}for(a&&a(n);o<s.length;o++)i=s[o],t.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return t.O(d)},n=self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[];n.forEach(a.bind(null,0)),n.push=a.bind(null,n.push.bind(n))})();var n=t.O(void 0,[504],()=>t(40267));n=t.O(n)})();