"use strict";(self["webpackChunkyandun_nebula_frontend"]=self["webpackChunkyandun_nebula_frontend"]||[]).push([[404],{66404:(e,n,t)=>{t.r(n),t.d(n,{default:()=>C});var a=t(24059),r=t(88844),u=t(698),l=(t(23288),t(27495),t(5746),t(95976)),i=t(12040),o=t(10160),c=t(18057),s=t(30578),d=t(17383),f=t(36149),p={class:"post-management"},v={class:"page-header"},k={class:"search-filters"},_={class:"table-container"},b={class:"post-title"},g={class:"post-badges"},h={class:"author-info"},m={class:"post-stats"},w={class:"pagination"};const y={__name:"PostManagement",setup:function(e){var n=(0,i.KR)(!1),t=(0,i.KR)([]),y=(0,i.Kh)({search:"",status:""}),F=(0,i.Kh)({page:1,limit:10,total:0}),A=function(){var e=(0,u.A)((0,a.A)().m(function e(){var u,l,i;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return n.value=!0,e.p=1,u=(0,r.A)({page:F.page,limit:F.limit},y),e.n=2,f.A.get("/admin/posts",{params:u});case 2:l=e.v,l.data.success&&(t.value=l.data.data.posts,F.total=l.data.data.total),e.n=4;break;case 3:e.p=3,i=e.v,console.error("加载帖子列表失败:",i),c.nk.error("加载帖子列表失败");case 4:return e.p=4,n.value=!1,e.f(4);case 5:return e.a(2)}},e,null,[[1,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),C=function(e){window.open("/community/posts/".concat(e),"_blank")},W=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要隐藏该帖子吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:console.log("隐藏帖子ID:",n),c.nk.success("帖子已隐藏"),A(),e.n=3;break;case 2:e.p=2,t=e.v,"cancel"!==t&&(console.error("隐藏帖子失败:",t),c.nk.error("操作失败"));case 3:return e.a(2)}},e,null,[[0,2]])}));return function(n){return e.apply(this,arguments)}}(),L=function(){var e=(0,u.A)((0,a.A)().m(function e(n){return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:try{console.log("显示帖子ID:",n),c.nk.success("帖子已显示"),A()}catch(t){console.error("显示帖子失败:",t),c.nk.error("操作失败")}case 1:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}(),z=function(){var e=(0,u.A)((0,a.A)().m(function e(n){var t,r;return(0,a.A)().w(function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,s.s.confirm("确定要删除该帖子吗？此操作不可恢复！","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 1:return e.n=2,f.A["delete"]("/admin/posts/".concat(n));case 2:t=e.v,t.data.success&&(c.nk.success("删除成功"),A()),e.n=4;break;case 3:e.p=3,r=e.v,"cancel"!==r&&(console.error("删除帖子失败:",r),c.nk.error("删除失败"));case 4:return e.a(2)}},e,null,[[0,3]])}));return function(n){return e.apply(this,arguments)}}(),x=function(e){var n={published:"已发布",draft:"草稿",deleted:"已删除",hidden:"已隐藏"};return n[e]||e},V=function(e){var n={published:"success",draft:"info",deleted:"danger",hidden:"warning"};return n[e]||""},X=function(e){F.limit=e,F.page=1,A()},B=function(e){F.page=e,A()},D=function(e){return e?new Date(e).toLocaleString("zh-CN"):"-"};return(0,l.sV)(function(){A()}),function(e,a){var r=(0,l.g2)("el-button"),u=(0,l.g2)("el-icon"),c=(0,l.g2)("el-input"),s=(0,l.g2)("el-col"),f=(0,l.g2)("el-option"),I=(0,l.g2)("el-select"),K=(0,l.g2)("el-row"),S=(0,l.g2)("el-table-column"),T=(0,l.g2)("el-tag"),R=(0,l.g2)("el-avatar"),U=(0,l.g2)("el-table"),N=(0,l.g2)("el-pagination"),Q=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",p,[(0,l.Lk)("div",v,[a[4]||(a[4]=(0,l.Lk)("h1",null,"帖子管理",-1)),(0,l.bF)(r,{onClick:a[0]||(a[0]=function(n){return e.$router.go(-1)})},{default:(0,l.k6)(function(){return a[3]||(a[3]=[(0,l.eW)("返回")])}),_:1,__:[3]})]),(0,l.Lk)("div",k,[(0,l.bF)(K,{gutter:20},{default:(0,l.k6)(function(){return[(0,l.bF)(s,{span:8},{default:(0,l.k6)(function(){return[(0,l.bF)(c,{modelValue:y.search,"onUpdate:modelValue":a[1]||(a[1]=function(e){return y.search=e}),placeholder:"搜索帖子标题或内容",clearable:"",onChange:A},{prefix:(0,l.k6)(function(){return[(0,l.bF)(u,null,{default:(0,l.k6)(function(){return[(0,l.bF)((0,i.R1)(d.Search))]}),_:1})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(s,{span:4},{default:(0,l.k6)(function(){return[(0,l.bF)(I,{modelValue:y.status,"onUpdate:modelValue":a[2]||(a[2]=function(e){return y.status=e}),placeholder:"帖子状态",clearable:"",onChange:A},{default:(0,l.k6)(function(){return[(0,l.bF)(f,{label:"已发布",value:"published"}),(0,l.bF)(f,{label:"草稿",value:"draft"}),(0,l.bF)(f,{label:"已删除",value:"deleted"}),(0,l.bF)(f,{label:"已隐藏",value:"hidden"})]}),_:1},8,["modelValue"])]}),_:1}),(0,l.bF)(s,{span:4},{default:(0,l.k6)(function(){return[(0,l.bF)(r,{type:"primary",onClick:A},{default:(0,l.k6)(function(){return a[5]||(a[5]=[(0,l.eW)("搜索")])}),_:1,__:[5]})]}),_:1})]}),_:1})]),(0,l.Lk)("div",_,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(U,{data:t.value,stripe:"",style:{width:"100%"}},{default:(0,l.k6)(function(){return[(0,l.bF)(S,{prop:"id",label:"ID",width:"80"}),(0,l.bF)(S,{prop:"title",label:"标题",width:"300"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.Lk)("div",b,[(0,l.Lk)("span",null,(0,o.v_)(n.title),1),(0,l.Lk)("div",g,[n.isSticky?((0,l.uX)(),(0,l.Wv)(T,{key:0,type:"warning",size:"small"},{default:(0,l.k6)(function(){return a[6]||(a[6]=[(0,l.eW)("置顶")])}),_:1,__:[6]})):(0,l.Q3)("",!0),n.isHighlighted?((0,l.uX)(),(0,l.Wv)(T,{key:1,type:"success",size:"small"},{default:(0,l.k6)(function(){return a[7]||(a[7]=[(0,l.eW)("精华")])}),_:1,__:[7]})):(0,l.Q3)("",!0)])])]}),_:1}),(0,l.bF)(S,{label:"作者",width:"120"},{default:(0,l.k6)(function(e){var n,t,a=e.row;return[(0,l.Lk)("div",h,[(0,l.bF)(R,{src:null===(n=a.user)||void 0===n?void 0:n.avatar,size:30},{default:(0,l.k6)(function(){var e;return[(0,l.eW)((0,o.v_)(null===(e=a.user)||void 0===e||null===(e=e.username)||void 0===e?void 0:e.charAt(0).toUpperCase()),1)]}),_:2},1032,["src"]),(0,l.Lk)("span",null,(0,o.v_)(null===(t=a.user)||void 0===t?void 0:t.username),1)])]}),_:1}),(0,l.bF)(S,{label:"状态",width:"100"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.bF)(T,{type:V(n.status),size:"small"},{default:(0,l.k6)(function(){return[(0,l.eW)((0,o.v_)(x(n.status)),1)]}),_:2},1032,["type"])]}),_:1}),(0,l.bF)(S,{label:"统计",width:"120"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.Lk)("div",m,[(0,l.Lk)("span",null,"👁 "+(0,o.v_)(n.views||0),1),(0,l.Lk)("span",null,"👍 "+(0,o.v_)(n.likes||0),1),(0,l.Lk)("span",null,"💬 "+(0,o.v_)(n.commentCount||0),1)])]}),_:1}),(0,l.bF)(S,{prop:"createdAt",label:"发布时间",width:"180"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.eW)((0,o.v_)(D(n.createdAt)),1)]}),_:1}),(0,l.bF)(S,{prop:"lastActivityAt",label:"最后活动",width:"180"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.eW)((0,o.v_)(D(n.lastActivityAt)),1)]}),_:1}),(0,l.bF)(S,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)(function(e){var n=e.row;return[(0,l.bF)(r,{type:"primary",size:"small",onClick:function(e){return C(n.id)}},{default:(0,l.k6)(function(){return a[8]||(a[8]=[(0,l.eW)(" 查看 ")])}),_:2,__:[8]},1032,["onClick"]),"hidden"!==n.status?((0,l.uX)(),(0,l.Wv)(r,{key:0,type:"warning",size:"small",onClick:function(e){return W(n.id)}},{default:(0,l.k6)(function(){return a[9]||(a[9]=[(0,l.eW)(" 隐藏 ")])}),_:2,__:[9]},1032,["onClick"])):((0,l.uX)(),(0,l.Wv)(r,{key:1,type:"success",size:"small",onClick:function(e){return L(n.id)}},{default:(0,l.k6)(function(){return a[10]||(a[10]=[(0,l.eW)(" 显示 ")])}),_:2,__:[10]},1032,["onClick"])),(0,l.bF)(r,{type:"danger",size:"small",onClick:function(e){return z(n.id)}},{default:(0,l.k6)(function(){return a[11]||(a[11]=[(0,l.eW)(" 删除 ")])}),_:2,__:[11]},1032,["onClick"])]}),_:1})]}),_:1},8,["data"])),[[Q,n.value]]),(0,l.Lk)("div",w,[(0,l.bF)(N,{"current-page":F.page,"page-size":F.limit,"page-sizes":[10,20,50,100],total:F.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:B},null,8,["current-page","page-size","total"])])])])}}};var F=t(1169);const A=(0,F.A)(y,[["__scopeId","data-v-36a4d7a9"]]),C=A}}]);