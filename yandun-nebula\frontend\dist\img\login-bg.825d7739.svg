<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="1000" viewBox="0 0 1000 1000">
  <defs>
    <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="0.5"/>
    </pattern>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <g opacity="0.3">
    <circle cx="200" cy="200" r="50" fill="rgba(255,255,255,0.2)" />
    <circle cx="800" cy="300" r="70" fill="rgba(255,255,255,0.1)" />
    <circle cx="500" cy="800" r="100" fill="rgba(255,255,255,0.15)" />
    <circle cx="300" cy="600" r="60" fill="rgba(255,255,255,0.2)" />
    <circle cx="700" cy="700" r="80" fill="rgba(255,255,255,0.1)" />
    
    <path d="M100,100 L200,300 L300,200 Z" fill="rgba(255,255,255,0.1)" />
    <path d="M700,100 L900,200 L800,300 Z" fill="rgba(255,255,255,0.15)" />
    <path d="M100,700 L300,900 L200,800 Z" fill="rgba(255,255,255,0.1)" />
    <path d="M700,800 L900,700 L800,900 Z" fill="rgba(255,255,255,0.15)" />
  </g>
  
  <g opacity="0.5">
    <line x1="0" y1="0" x2="1000" y2="1000" stroke="rgba(255,255,255,0.1)" stroke-width="2" />
    <line x1="1000" y1="0" x2="0" y2="1000" stroke="rgba(255,255,255,0.1)" stroke-width="2" />
  </g>
</svg> 